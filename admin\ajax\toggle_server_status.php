<?php
session_start();
require_once '../../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

$data = json_decode(file_get_contents('php://input'), true);
$server_id = $data['server_id'] ?? 0;
$new_status = $data['status'] ?? '';

if ($server_id && in_array($new_status, ['active', 'inactive'])) {
    $sql = "UPDATE servers SET status = ? WHERE id = ?";
    $stmt = $db->prepare($sql);
    
    if ($stmt->execute([$new_status, $server_id])) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database error']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid data']);
}