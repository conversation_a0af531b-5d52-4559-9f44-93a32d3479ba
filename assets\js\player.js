document.addEventListener('DOMContentLoaded', function() {
    const serverSelect = document.getElementById('server-select');
    if (!serverSelect) return;

    serverSelect.addEventListener('change', function() {
        const serverId = this.value;
        const contentId = this.dataset.contentId;
        const contentType = this.dataset.contentType;

        // Save user preference
        fetch('save-preference.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                server_id: serverId,
                content_id: contentId,
                content_type: contentType
            })
        });

        // Load new server content
        loadServerContent(serverId);
    });
});
function updateWatchProgress(contentType, contentId, progress) {
    fetch('includes/update_watch_progress.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `content_type=${contentType}&content_id=${contentId}&progress=${progress}`
    });
}

// Add this to your video player event listener
video.addEventListener('timeupdate', function() {
    const progress = (video.currentTime / video.duration) * 100;
    if (progress % 5 === 0) { // Update every 5% progress
        updateWatchProgress(contentType, contentId, progress);
    }
});
