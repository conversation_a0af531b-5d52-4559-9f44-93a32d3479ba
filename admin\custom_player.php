<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$content_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? $_GET['type'] : 'movie';

// Get content details
if ($type === 'movie') {
    $stmt = $db->prepare("SELECT id, title, poster_path, tmdb_id FROM movies WHERE id = ?");
} else {
    $stmt = $db->prepare("SELECT id, title, poster_path, tmdb_id FROM series WHERE id = ?");
}
$stmt->execute([$content_id]);
$content = $stmt->fetch(PDO::FETCH_ASSOC);

// Get direct sources
$stmt = $db->prepare("SELECT * FROM direct_sources WHERE content_type = ? AND content_id = ? ORDER BY quality DESC");
$stmt->execute([$type, $content_id]);
$sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Player - <?php echo htmlspecialchars($content['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/plyr/3.7.8/plyr.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #fff;
        }
        .player-wrapper {
            position: relative;
            padding-top: 56.25%;
            background: #000;
        }
        .player-wrapper video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .quality-selector {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .quality-btn {
            padding: 5px 15px;
            border: 1px solid #666;
            background: #333;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
        }
        .quality-btn.active {
            background: #007bff;
            border-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-9">
                <!-- Video Player -->
                <div class="player-wrapper">
                    <video id="player" playsinline controls crossorigin>
                        <?php foreach ($sources as $source): ?>
                            <source src="<?php echo htmlspecialchars($source['video_url']); ?>" 
                                    type="video/mp4" 
                                    size="<?php echo htmlspecialchars($source['quality']); ?>">
                            <?php if (!empty($source['subtitle_url'])): ?>
                                <track kind="subtitles" 
                                       label="Subtitle" 
                                       src="<?php echo htmlspecialchars($source['subtitle_url']); ?>" 
                                       default>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </video>
                </div>

                <!-- Quality Selector -->
                <div class="quality-selector mt-3">
                    <?php foreach ($sources as $source): ?>
                        <button class="quality-btn" data-quality="<?php echo htmlspecialchars($source['quality']); ?>">
                            <?php echo htmlspecialchars($source['quality']); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="col-md-3">
                <!-- Content Info -->
                <div class="card bg-dark">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($content['title']); ?></h5>
                        <p class="card-text">
                            Type: <?php echo ucfirst($type); ?><br>
                            TMDB ID: <?php echo $content['tmdb_id']; ?>
                        </p>
                        <a href="manage_direct_sources.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/plyr@3.7.8/dist/plyr.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Plyr
            const player = new Plyr('#player', {
                controls: [
                    'play-large', 'play', 'progress', 'current-time', 'duration',
                    'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
                ],
                settings: ['quality', 'speed', 'loop'],
            });

            // Quality button handling
            const qualityButtons = document.querySelectorAll('.quality-btn');
            qualityButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const quality = button.dataset.quality;
                    const sources = player.media.querySelectorAll('source');
                    
                    // Remove active class from all buttons
                    qualityButtons.forEach(btn => btn.classList.remove('active'));
                    
                    // Add active class to clicked button
                    button.classList.add('active');
                    
                    // Find matching source and set it
                    sources.forEach(source => {
                        if (source.getAttribute('size') === quality) {
                            const currentTime = player.currentTime;
                            player.source = {
                                type: 'video',
                                sources: [{
                                    src: source.src,
                                    type: source.type,
                                    size: quality
                                }]
                            };
                            player.on('loadedmetadata', () => {
                                player.currentTime = currentTime;
                                player.play();
                            });
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>