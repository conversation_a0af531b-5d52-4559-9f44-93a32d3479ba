<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/tmdb_handler.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$tmdb = new TMDBHandler();
$message = '';
$error = '';

// Handle TMDB Search
$tmdb_results = [];
if (isset($_POST['tmdb_search'])) {
    $search_query = trim($_POST['tmdb_query']);
    $content_type = $_POST['content_type'];
    
    if (strlen($search_query) < 2) {
        $error = "Please enter at least 2 characters to search";
    } else {
        try {
            if ($content_type === 'movie') {
                $response = $tmdb->searchMovie($search_query);
            } else {
                $response = $tmdb->searchTV($search_query);
            }
            
            if (isset($response['results'])) {
                $tmdb_results = $response['results'];
            }
        } catch (Exception $e) {
            $error = "Search failed: " . $e->getMessage();
        }
    }
}

// Get existing content with direct sources
$content_query = "
    SELECT 
        CASE 
            WHEN m.id IS NOT NULL THEN 'movie'
            ELSE 'series'
        END as content_type,
        COALESCE(m.id, s.id) as id,
        COALESCE(m.title, s.title) as title,
        COALESCE(m.tmdb_id, s.tmdb_id) as tmdb_id,
        COUNT(ds.id) as source_count
    FROM direct_sources ds
    LEFT JOIN movies m ON ds.content_id = m.id AND ds.content_type = 'movie'
    LEFT JOIN series s ON ds.content_id = s.id AND ds.content_type = 'series'
    GROUP BY ds.content_id, ds.content_type
    ORDER BY ds.created_at DESC
";

$content_list = $db->query($content_query)->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Direct Sources</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .tmdb-poster {
            max-width: 100px;
            height: auto;
        }
        .content-list {
            max-height: 600px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <h2 class="mb-4">Manage Direct Sources</h2>
        
        <!-- Search Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Search TMDB</h5>
            </div>
            <div class="card-body">
                <form method="POST" class="row g-3">
                    <div class="col-md-6">
                        <input type="text" class="form-control" name="tmdb_query" placeholder="Search movies or TV shows..." required>
                    </div>
                    <div class="col-md-4">
                        <select name="content_type" class="form-select">
                            <option value="movie">Movie</option>
                            <option value="tv">TV Show</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" name="tmdb_search" class="btn btn-primary w-100">Search</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- TMDB Results -->
        <?php if (!empty($tmdb_results)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Search Results</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($tmdb_results as $result): ?>
                        <div class="col-md-3 mb-3">
                            <div class="card h-100">
                                <?php 
                                $poster_path = $result['poster_path'] ?? '';
                                $poster_url = $poster_path ? "https://image.tmdb.org/t/p/w200{$poster_path}" : 'path/to/default/poster.jpg';
                                ?>
                                <img src="<?php echo $poster_url; ?>" class="card-img-top" alt="Poster">
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($result['title'] ?? $result['name']); ?></h6>
                                    <p class="card-text small">
                                        Released: <?php echo $result['release_date'] ?? $result['first_air_date'] ?? 'N/A'; ?>
                                    </p>
                                    <a href="direct_player.php?tmdb_id=<?php echo $result['id']; ?>&type=<?php echo $_POST['content_type']; ?>" 
                                       class="btn btn-sm btn-primary">
                                        Add Sources
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Existing Content List -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Content with Direct Sources</h5>
            </div>
            <div class="card-body content-list">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Sources</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($content_list as $content): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($content['title']); ?></td>
                                <td><?php echo ucfirst($content['content_type']); ?></td>
                                <td><?php echo $content['source_count']; ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="direct_player.php?id=<?php echo $content['id']; ?>&type=<?php echo $content['content_type']; ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="custom_player.php?id=<?php echo $content['id']; ?>&type=<?php echo $content['content_type']; ?>" 
                                           class="btn btn-sm btn-warning ms-1">
                                            <i class="fas fa-play"></i> Play
                                        </a>
                                        <a href="../<?php echo $content['content_type']; ?>.php?id=<?php echo $content['id']; ?>" 
                                           class="btn btn-sm btn-info ms-1" 
                                           target="_blank">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
