<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $content_type = $_POST['content_type'];
    $title = trim($_POST['title']);
    $original_title = trim($_POST['original_title']);
    $overview = trim($_POST['overview']);
    $poster_path = trim($_POST['poster_path']);
    $backdrop_path = trim($_POST['backdrop_path']);
    $release_date = $_POST['release_date'];
    $runtime = (int)$_POST['runtime'];
    $genres = $_POST['genres'];
    
    // Generate slug
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
    
    try {
        $db->beginTransaction();
        
        if ($content_type === 'movie') {
            // Insert movie
            $stmt = $db->prepare("
                INSERT INTO movies (
                    title, original_title, slug, overview,
                    poster_path, backdrop_path, release_date, runtime,
                    genres, language, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            $stmt->execute([
                $title,
                $original_title,
                $slug,
                $overview,
                $poster_path,
                $backdrop_path,
                $release_date,
                $runtime,
                $genres,
                'bn'
            ]);
            
            $content_id = $db->lastInsertId();
            
            // Add direct source if provided
            if (!empty($_POST['source_url'])) {
                $stmt = $db->prepare("
                    INSERT INTO direct_sources (
                        content_type, content_id, video_url,
                        quality, created_at
                    ) VALUES ('movie', ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $content_id,
                    $_POST['source_url'],
                    $_POST['quality']
                ]);
            }
            
        } else {
            // Insert series
            $stmt = $db->prepare("
                INSERT INTO series (
                    title, original_title, slug, overview,
                    poster_path, backdrop_path, first_air_date,
                    genres, language, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            $stmt->execute([
                $title,
                $original_title,
                $slug,
                $overview,
                $poster_path,
                $backdrop_path,
                $release_date,
                $genres,
                'bn'
            ]);
            
            $series_id = $db->lastInsertId();
            
            // Add season if provided
            if (!empty($_POST['season_number'])) {
                $stmt = $db->prepare("
                    INSERT INTO seasons (
                        series_id, season_number, name, overview,
                        poster_path, air_date, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $series_id,
                    $_POST['season_number'],
                    "Season " . $_POST['season_number'],
                    $overview,
                    $poster_path,
                    $release_date
                ]);
                
                $season_id = $db->lastInsertId();
                
                // Add episode if provided
                if (!empty($_POST['episode_number']) && !empty($_POST['source_url'])) {
                    $stmt = $db->prepare("
                        INSERT INTO episodes (
                            series_id, season_id, episode_number,
                            name, overview, still_path, air_date,
                            created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                    ");
                    
                    $stmt->execute([
                        $series_id,
                        $season_id,
                        $_POST['episode_number'],
                        $_POST['episode_title'] ?: "Episode " . $_POST['episode_number'],
                        $overview,
                        $poster_path,
                        $release_date
                    ]);
                    
                    $episode_id = $db->lastInsertId();
                    
                    // Add direct source for episode
                    $stmt = $db->prepare("
                        INSERT INTO direct_sources (
                            content_type, content_id, video_url,
                            quality, episode_id, created_at
                        ) VALUES ('series', ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $series_id,
                        $_POST['source_url'],
                        $_POST['quality'],
                        $episode_id
                    ]);
                }
            }
        }
        
        $db->commit();
        $message = "Content added successfully!";
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = "Error: " . $e->getMessage();
        error_log("Error in add_custom_content.php: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Custom Content</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2 class="mb-4">Add Custom Content</h2>
        
        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label class="form-label">Content Type</label>
                        <select name="content_type" class="form-select" id="contentType" required>
                            <option value="movie">Movie</option>
                            <option value="series">TV Series</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Title</label>
                        <input type="text" name="title" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Original Title</label>
                        <input type="text" name="original_title" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Overview</label>
                        <textarea name="overview" class="form-control" rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Poster Path (URL)</label>
                        <input type="url" name="poster_path" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Backdrop Path (URL)</label>
                        <input type="url" name="backdrop_path" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Release Date</label>
                        <input type="date" name="release_date" class="form-control" required>
                    </div>
                    
                    <div class="mb-3 movie-only">
                        <label class="form-label">Runtime (minutes)</label>
                        <input type="number" name="runtime" class="form-control">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Genres (comma-separated)</label>
                        <input type="text" name="genres" class="form-control" required>
                    </div>
                    
                    <div class="series-only" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Season Number</label>
                            <input type="number" name="season_number" class="form-control">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Episode Number</label>
                            <input type="number" name="episode_number" class="form-control">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Episode Title</label>
                            <input type="text" name="episode_title" class="form-control">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Source URL</label>
                        <input type="url" name="source_url" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Quality</label>
                        <select name="quality" class="form-select" required>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Add Content</button>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('contentType').addEventListener('change', function() {
            const isMovie = this.value === 'movie';
            document.querySelectorAll('.movie-only').forEach(el => {
                el.style.display = isMovie ? 'block' : 'none';
            });
            document.querySelectorAll('.series-only').forEach(el => {
                el.style.display = isMovie ? 'none' : 'block';
            });
        });
    </script>
</body>
</html>
