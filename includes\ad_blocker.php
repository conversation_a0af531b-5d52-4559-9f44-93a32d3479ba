<?php
class AdBlocker {
    private static $blockPatterns = [
        '/\.doubleclick\.net/',
        '/google-analytics\.com/',
        '/googleadservices\.com/',
        '/googlesyndication\.com/',
        '/adnxs\.com/',
        '/\.pop\./',
        '/popads\./',
        '/\.popup\./',
        '/\.redirect\./',
        '/\.advertising\./',
        '/\.ads\./',
        '/adsystem\./',
        '/adserver\./',
        '/\.track\./',
        '/\.tracker\./',
        '/tracking\./',
        '/analytics\./',
    ];

    private static $blockHosts = [
        'doubleclick.net',
        'google-analytics.com',
        'googleadservices.com',
        'googlesyndication.com',
        'adnxs.com',
        'popads.net',
        'popcash.net',
        'propellerads.com',
        'adsterra.com',
    ];

    public static function shouldBlock($url) {
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'] ?? '';

        // Check host blocks
        foreach (self::$blockHosts as $blockHost) {
            if (stripos($host, $blockHost) !== false) {
                return true;
            }
        }

        // Check pattern blocks
        foreach (self::$blockPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    public static function cleanHtml($html) {
        // Remove known ad scripts
        $html = preg_replace('/<script[^>]*(?:googlead|adsbygoogle|doubleclick|adsense)[^>]*>.*?<\/script>/is', '', $html);
        
        // Remove popup scripts
        $html = preg_replace('/<script[^>]*(?:popup|popunder|pop_under)[^>]*>.*?<\/script>/is', '', $html);
        
        // Remove redirect scripts
        $html = preg_replace('/<script[^>]*(?:redirect|window\.location)[^>]*>.*?<\/script>/is', '', $html);
        
        // Remove tracking pixels
        $html = preg_replace('/<img[^>]*(?:tracking|tracker|pixel)[^>]*>/i', '', $html);
        
        return $html;
    }

    public static function injectProtection() {
        return "
        <script>
            (function() {
                // Block common ad networks and redirects
                const blockList = " . json_encode(self::$blockHosts) . ";
                
                // Override XMLHttpRequest
                const XHR = XMLHttpRequest.prototype;
                const open = XHR.open;
                XHR.open = function(method, url) {
                    if (blockList.some(domain => url.includes(domain))) {
                        return;
                    }
                    return open.apply(this, arguments);
                };
                
                // Block window.open
                window.open = function() { return null; };
                
                // Block redirects
                Object.defineProperty(window, 'location', {
                    set: function() { return window.location; }
                });
                
                // Clean intervals that might contain ads
                setInterval(() => {
                    for (let i = 0; i < 1000; i++) {
                        clearInterval(i);
                        clearTimeout(i);
                    }
                }, 100);
            })();
        </script>
        ";
    }

    public static function injectAntiRedirectScript() {
        return <<<HTML
        <script>
        (function() {
            // Save original methods
            const _open = window.open;
            const _eval = window.eval;
            const _setTimeout = window.setTimeout;
            const _setInterval = window.setInterval;
            
            // Block window.open
            window.open = function() { return null; };
            
            // Block eval
            window.eval = function() { return null; };
            
            // Protect location
            let _location = window.location;
            Object.defineProperty(window, 'location', {
                get: function() { return _location; },
                set: function(value) {
                    if (document.hasFocus()) {
                        _location = value;
                        return value;
                    }
                    console.log('Blocked redirect attempt');
                    return _location;
                }
            });

            // Clean existing intervals and timeouts
            for (let i = 0; i < 1000; i++) {
                clearInterval(i);
                clearTimeout(i);
            }

            // Override setTimeout and setInterval
            window.setTimeout = function(callback, delay) {
                if (delay < 1000) return null; // Block short timeouts
                return _setTimeout(callback, delay);
            };
            
            window.setInterval = function(callback, delay) {
                if (delay < 1000) return null; // Block short intervals
                return _setInterval(callback, delay);
            };

            // Block common redirect methods
            ['assign', 'replace', 'reload'].forEach(method => {
                const original = window.location[method];
                window.location[method] = function() {
                    if (document.hasFocus()) {
                        return original.apply(this, arguments);
                    }
                    console.log('Blocked redirect attempt:', method);
                    return null;
                };
            });

            // Detect and block iframe redirects
            function blockIframeRedirects() {
                const iframes = document.getElementsByTagName('iframe');
                for (let iframe of iframes) {
                    try {
                        const doc = iframe.contentDocument || iframe.contentWindow.document;
                        if (doc) {
                            Object.defineProperty(doc, 'location', {
                                get: function() { return iframe.src; },
                                set: function() { return iframe.src; }
                            });
                        }
                    } catch (e) {}
                }
            }

            // Monitor DOM changes for new iframes
            const observer = new MutationObserver(function(mutations) {
                blockIframeRedirects();
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Block history manipulation
            const _pushState = history.pushState;
            const _replaceState = history.replaceState;
            
            history.pushState = function() {
                if (document.hasFocus()) {
                    return _pushState.apply(this, arguments);
                }
                return null;
            };
            
            history.replaceState = function() {
                if (document.hasFocus()) {
                    return _replaceState.apply(this, arguments);
                }
                return null;
            };

            // Clean suspicious elements
            function cleanSuspiciousElements() {
                const suspicious = document.querySelectorAll(
                    'iframe[style*="position: absolute"], ' +
                    'iframe[style*="opacity: 0"], ' +
                    'iframe[width="1"], ' +
                    'iframe[height="1"], ' +
                    'div[style*="position: absolute"][style*="opacity: 0"]'
                );
                suspicious.forEach(el => el.remove());
            }

            // Run cleanup periodically
            setInterval(cleanSuspiciousElements, 1000);

            // Block programmatic clicks
            document.addEventListener('click', function(e) {
                if (!e.isTrusted) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }, true);

            // Additional protection for the player iframe
            function protectPlayer() {
                const player = document.querySelector('.movie-player');
                if (player) {
                    player.addEventListener('load', function() {
                        try {
                            const doc = player.contentDocument || player.contentWindow.document;
                            if (doc) {
                                doc.addEventListener('click', function(e) {
                                    if (!e.isTrusted) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                    }
                                }, true);
                            }
                        } catch (e) {}
                    });
                }
            }

            // Run initial protection
            document.addEventListener('DOMContentLoaded', function() {
                blockIframeRedirects();
                cleanSuspiciousElements();
                protectPlayer();
            });
        })();
        </script>
HTML;
    }
}
