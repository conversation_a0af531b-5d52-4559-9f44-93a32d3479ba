<?php
require_once __DIR__ . '/../includes/init.php';

// Check if user is logged in and is admin
if (!$auth->isLoggedIn() || !$auth->isAdmin()) {
    // Clear any existing sessions for security
    session_destroy();
    
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        // For AJAX requests
        header('HTTP/1.1 401 Unauthorized');
        exit(json_encode(['error' => 'Unauthorized access']));
    } else {
        // For regular requests
        header('Location: /login.php?error=unauthorized');
        exit;
    }
}