<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$success = $error = '';

// Get current settings
$stmt = $db->query("SELECT * FROM settings WHERE id = 1");
$settings = $stmt->fetch(PDO::FETCH_ASSOC);

// Alter the settings table to add new fields
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS site_logo VARCHAR(255) DEFAULT NULL");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS site_favicon VARCHAR(255) DEFAULT NULL");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS default_language VARCHAR(10) DEFAULT 'en'");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS timezone VARCHAR(100) DEFAULT 'UTC'");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS enable_registration TINYINT(1) DEFAULT 1");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS enable_user_reviews TINYINT(1) DEFAULT 1");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS enable_social_login TINYINT(1) DEFAULT 1");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS footer_text TEXT");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS custom_css TEXT");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS custom_js TEXT");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS google_analytics_id VARCHAR(50)");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS recaptcha_site_key VARCHAR(255)");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS recaptcha_secret_key VARCHAR(255)");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS smtp_host VARCHAR(255)");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS smtp_port INT");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS smtp_username VARCHAR(255)");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS smtp_password VARCHAR(255)");
$db->exec("ALTER TABLE settings 
    ADD COLUMN IF NOT EXISTS smtp_encryption VARCHAR(10)");

// Handle file uploads
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate settings first
        $validation_errors = validate_settings($_POST);
        if (!empty($validation_errors)) {
            throw new Exception(implode("<br>", $validation_errors));
        }

        $db->beginTransaction();

        // Handle logo upload
        if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === 0) {
            $logo_path = handle_file_upload($_FILES['site_logo'], 'images/logo/');
            $_POST['site_logo'] = $logo_path;
        }

        // Handle favicon upload
        if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === 0) {
            $favicon_path = handle_file_upload($_FILES['site_favicon'], 'images/favicon/');
            $_POST['site_favicon'] = $favicon_path;
        }

        // Update settings
        $stmt = $db->prepare("
            UPDATE settings SET 
            site_name = ?,
            site_description = ?,
            site_keywords = ?,
            tmdb_api_key = ?,
            items_per_page = ?,
            maintenance_mode = ?,
            site_logo = ?,
            site_favicon = ?,
            default_language = ?,
            timezone = ?,
            enable_registration = ?,
            enable_user_reviews = ?,
            enable_social_login = ?,
            footer_text = ?,
            custom_css = ?,
            custom_js = ?,
            google_analytics_id = ?,
            recaptcha_site_key = ?,
            recaptcha_secret_key = ?,
            smtp_host = ?,
            smtp_port = ?,
            smtp_username = ?,
            smtp_password = ?,
            smtp_encryption = ?,
            updated_at = NOW()
            WHERE id = 1
        ");

        $stmt->execute([
            $_POST['site_name'],
            $_POST['site_description'],
            $_POST['site_keywords'],
            $_POST['tmdb_api_key'],
            (int)$_POST['items_per_page'],
            isset($_POST['maintenance_mode']) ? 1 : 0,
            $_POST['site_logo'] ?? $settings['site_logo'],
            $_POST['site_favicon'] ?? $settings['site_favicon'],
            $_POST['default_language'],
            $_POST['timezone'],
            isset($_POST['enable_registration']) ? 1 : 0,
            isset($_POST['enable_user_reviews']) ? 1 : 0,
            isset($_POST['enable_social_login']) ? 1 : 0,
            $_POST['footer_text'],
            $_POST['custom_css'],
            $_POST['custom_js'],
            $_POST['google_analytics_id'],
            $_POST['recaptcha_site_key'],
            $_POST['recaptcha_secret_key'],
            $_POST['smtp_host'],
            $_POST['smtp_port'],
            $_POST['smtp_username'],
            $_POST['smtp_password'],
            $_POST['smtp_encryption']
        ]);

        $db->commit();
        $success = "Settings updated successfully";
        
        // Refresh settings
        $stmt = $db->query("SELECT * FROM settings WHERE id = 1");
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);

        // Clear cache if exists
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    } catch (Exception $e) {
        $db->rollBack();
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Helper function for file uploads
function handle_file_upload($file, $target_dir) {
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0777, true);
    }
    
    $target_file = $target_dir . basename($file["name"]);
    $imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));
    
    // Check if image file is actual image
    $check = getimagesize($file["tmp_name"]);
    if($check === false) {
        throw new Exception("File is not an image.");
    }
    
    // Check file size
    if ($file["size"] > 500000) {
        throw new Exception("File is too large.");
    }
    
    // Allow certain file formats
    if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg" && $imageFileType != "gif" ) {
        throw new Exception("Only JPG, JPEG, PNG & GIF files are allowed.");
    }
    
    if (move_uploaded_file($file["tmp_name"], $target_file)) {
        return $target_file;
    } else {
        throw new Exception("Error uploading file.");
    }
}

function validate_settings($post_data) {
    $errors = [];
    
    // Required fields validation
    $required_fields = ['site_name', 'tmdb_api_key', 'items_per_page'];
    foreach ($required_fields as $field) {
        if (empty($post_data[$field])) {
            $errors[] = ucfirst(str_replace('_', ' ', $field)) . " is required";
        }
    }
    
    // Items per page validation
    if (!empty($post_data['items_per_page'])) {
        if (!is_numeric($post_data['items_per_page']) || $post_data['items_per_page'] < 1) {
            $errors[] = "Items per page must be a positive number";
        }
    }
    
    // Custom CSS validation
    if (!empty($post_data['custom_css'])) {
        if (!validate_css($post_data['custom_css'])) {
            $errors[] = "Invalid CSS syntax";
        }
    }
    
    // Custom JavaScript validation
    if (!empty($post_data['custom_js'])) {
        if (!validate_javascript($post_data['custom_js'])) {
            $errors[] = "Invalid JavaScript syntax";
        }
    }
    
    // Google Analytics ID validation (Format: UA-XXXXXXXX-X or G-XXXXXXXXXX)
    if (!empty($post_data['google_analytics_id'])) {
        if (!preg_match('/^(UA-\d{4,10}-\d{1,4}|G-[A-Z0-9]{10})$/', $post_data['google_analytics_id'])) {
            $errors[] = "Invalid Google Analytics ID format";
        }
    }
    
    // reCAPTCHA keys validation
    if (!empty($post_data['recaptcha_site_key'])) {
        if (strlen($post_data['recaptcha_site_key']) !== 40) {
            $errors[] = "Invalid reCAPTCHA site key length";
        }
    }
    if (!empty($post_data['recaptcha_secret_key'])) {
        if (strlen($post_data['recaptcha_secret_key']) !== 40) {
            $errors[] = "Invalid reCAPTCHA secret key length";
        }
    }
    
    // Image validation helper functions
    if (isset($_FILES['site_logo'])) {
        $logo_errors = validate_image_upload($_FILES['site_logo'], 'logo');
        $errors = array_merge($errors, $logo_errors);
    }
    
    if (isset($_FILES['site_favicon'])) {
        $favicon_errors = validate_image_upload($_FILES['site_favicon'], 'favicon');
        $errors = array_merge($errors, $favicon_errors);
    }
    
    return $errors;
}

// Helper function to validate CSS
function validate_css($css) {
    // Remove comments and whitespace
    $css = preg_replace('!/\*.*?\*/!s', '', $css);
    $css = preg_replace('/\s+/', ' ', $css);
    
    // Basic CSS structure validation
    if (substr_count($css, '{') !== substr_count($css, '}')) {
        return false;
    }
    
    // Check for common CSS properties
    $pattern = '/[a-z-]+\s*:\s*[^;{}]+;/i';
    if (!empty($css) && !preg_match($pattern, $css)) {
        return false;
    }
    
    return true;
}

// Helper function to validate JavaScript
function validate_javascript($js) {
    // Remove comments and whitespace
    $js = preg_replace('/\/\*[\s\S]*?\*\/|([^\\:]|^)\/\/.*$/m', '', $js);
    
    // Basic JavaScript structure validation
    if (substr_count($js, '{') !== substr_count($js, '}')) {
        return false;
    }
    
    // Check for dangerous functions
    $dangerous_functions = ['eval', 'Function', 'setTimeout', 'setInterval'];
    foreach ($dangerous_functions as $func) {
        if (stripos($js, $func) !== false) {
            return false;
        }
    }
    
    return true;
}

// Helper function to validate image uploads
function validate_image_upload($file, $type = 'logo') {
    $errors = [];
    
    if ($file['error'] === 0) {
        // Check file size (2MB max for logo, 512KB for favicon)
        $max_size = ($type === 'logo') ? 2097152 : 524288;
        if ($file['size'] > $max_size) {
            $errors[] = ucfirst($type) . " file size must be less than " . ($max_size/1048576) . "MB";
        }
        
        // Check file type
        $allowed_types = [
            'logo' => ['image/jpeg', 'image/png', 'image/gif'],
            'favicon' => ['image/png', 'image/x-icon', 'image/vnd.microsoft.icon']
        ];
        
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mime_type = $finfo->file($file['tmp_name']);
        
        if (!in_array($mime_type, $allowed_types[$type])) {
            $errors[] = "Invalid " . ucfirst($type) . " file type. Allowed types: " . 
                       implode(', ', array_map(function($mime) {
                           return strtoupper(substr($mime, 6));
                       }, $allowed_types[$type]));
        }
        
        // Check image dimensions
        $image_info = getimagesize($file['tmp_name']);
        if ($type === 'logo') {
            if ($image_info[0] > 1920 || $image_info[1] > 1080) {
                $errors[] = "Logo dimensions should not exceed 1920x1080 pixels";
            }
        } else { // favicon
            if ($image_info[0] > 256 || $image_info[1] > 256) {
                $errors[] = "Favicon dimensions should not exceed 256x256 pixels";
            }
        }
    }
    
    return $errors;
}

// Get timezone list
$timezones = DateTimeZone::listIdentifiers();

$page_title = 'Site Settings';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="page-title">Site Settings</h2>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- Settings Form -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">General Settings</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- Site Information -->
                        <div class="mb-4">
                            <h6 class="text-uppercase text-secondary mb-3">Site Information</h6>
                            <div class="mb-3">
                                <label class="form-label">Site Name</label>
                                <input type="text" name="site_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Site Description</label>
                                <textarea name="site_description" class="form-control" rows="3"><?php 
                                    echo htmlspecialchars($settings['site_description'] ?? ''); 
                                ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Meta Keywords</label>
                                <input type="text" name="site_keywords" class="form-control" 
                                       value="<?php echo htmlspecialchars($settings['site_keywords'] ?? ''); ?>">
                                <small class="text-muted">Separate keywords with commas</small>
                            </div>
                        </div>

                        <!-- API Settings -->
                        <div class="mb-4">
                            <h6 class="text-uppercase text-secondary mb-3">API Settings</h6>
                            <div class="mb-3">
                                <label class="form-label">TMDB API Key</label>
                                <input type="text" name="tmdb_api_key" class="form-control" 
                                       value="<?php echo htmlspecialchars($settings['tmdb_api_key'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <!-- Display Settings -->
                        <div class="mb-4">
                            <h6 class="text-uppercase text-secondary mb-3">Display Settings</h6>
                            <div class="mb-3">
                                <label class="form-label">Items Per Page</label>
                                <input type="number" name="items_per_page" class="form-control" 
                                       value="<?php echo (int)($settings['items_per_page'] ?? 24); ?>" 
                                       min="12" max="100" required>
                            </div>
                        </div>

                        <!-- Maintenance Mode -->
                        <div class="mb-4">
                            <h6 class="text-uppercase text-secondary mb-3">Maintenance</h6>
                            <div class="form-check form-switch">
                                <input type="checkbox" name="maintenance_mode" class="form-check-input" 
                                       id="maintenance_mode" <?php echo ($settings['maintenance_mode'] ?? 0) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maintenance_mode">Enable Maintenance Mode</label>
                            </div>
                            <small class="text-muted">Only administrators can access the site when enabled</small>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Quick Info Card -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">System Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            PHP Version
                            <span class="badge bg-primary"><?php echo PHP_VERSION; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            MySQL Version
                            <span class="badge bg-primary"><?php echo $db->query('select version()')->fetchColumn(); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Upload Max Size
                            <span class="badge bg-primary"><?php echo ini_get('upload_max_filesize'); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Memory Limit
                            <span class="badge bg-primary"><?php echo ini_get('memory_limit'); ?></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Settings specific styles */
.form-check-input:checked {
    background-color: var(--sidebar-active);
    border-color: var(--sidebar-active);
}

.list-group-item {
    background: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary) !important;
}
</style>

<?php require_once 'includes/footer.php'; ?> 
