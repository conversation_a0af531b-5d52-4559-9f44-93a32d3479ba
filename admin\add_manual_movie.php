<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/tmdb_handler.php';

$auth = new Auth($db);
$tmdb = new TMDBHandler();

if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$success = $error = '';

// Handle TMDB Import
if (isset($_POST['import_tmdb'])) {
    try {
        $tmdb_id = $_POST['tmdb_id'];
        $custom_title = trim($_POST['custom_title']); // Optional custom title
        
        // Get movie details from TMDB
        $movie_data = $tmdb->getMovieById($tmdb_id);
        
        if (!$movie_data) {
            throw new Exception("Movie not found on TMDB");
        }

        $db->beginTransaction();
        
        // Generate slug from title (using custom title if provided)
        $title_to_use = !empty($custom_title) ? $custom_title : $movie_data['title'];
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title_to_use)));

        // Insert movie
        $stmt = $db->prepare("
            INSERT INTO manual_movies (
                tmdb_id, title, overview, poster_path, backdrop_path, 
                release_date, runtime, rating, slug, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $tmdb_id,
            $title_to_use,
            $movie_data['overview'],
            $movie_data['poster_path'],
            $movie_data['backdrop_path'],
            $movie_data['release_date'],
            $movie_data['runtime'],
            $movie_data['vote_average'],
            $slug,
            'active'
        ]);
        
        $movie_id = $db->lastInsertId();
        
        $db->commit();
        $success = 'Movie imported successfully from TMDB!';
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = 'Error importing movie: ' . $e->getMessage();
    }
}

// Handle TMDB Search
if (isset($_POST['search_tmdb'])) {
    try {
        $search_query = trim($_POST['search_query']);
        if (empty($search_query)) {
            throw new Exception("Please enter a search term");
        }
        
        $search_results = $tmdb->searchMovies($search_query);
        
    } catch (Exception $e) {
        $error = 'Search error: ' . $e->getMessage();
    }
}

// Your existing POST handler for manual addition remains here
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['import_tmdb'])) {
    try {
        $db->beginTransaction();
        
        // Insert movie details
        $stmt = $db->prepare("
            INSERT INTO manual_movies (
                title, overview, poster_path, backdrop_path, 
                release_date, runtime, rating, slug, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        // Generate slug from title
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $_POST['title'])));

        $stmt->execute([
            $_POST['title'],
            $_POST['overview'],
            $_POST['poster_path'],
            $_POST['backdrop_path'],
            $_POST['release_date'],
            $_POST['runtime'],
            $_POST['rating'],
            $slug,
            'active' // Default status
        ]);
        
        $movie_id = $db->lastInsertId();
        
        // Insert sources
        $stmt = $db->prepare("
            INSERT INTO manual_movie_sources (
                movie_id, server_name, source_url, quality
            ) VALUES (?, ?, ?, ?)
        ");
        
        foreach ($_POST['sources'] as $source) {
            if (!empty($source['url'])) {
                $stmt->execute([
                    $movie_id,
                    $source['server_name'],
                    $source['url'],
                    $source['quality']
                ]);
            }
        }
        
        $db->commit();
        $success = 'Movie added successfully!';
    } catch (Exception $e) {
        $db->rollBack();
        $error = 'Error adding movie: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Manual Movie</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-result {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .search-result:hover {
            background-color: rgba(255,255,255,0.1);
        }
        .movie-poster-small {
            width: 50px;
            height: 75px;
            object-fit: cover;
        }
        /* Mobile-friendly styles */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .btn {
                padding: 8px 12px;
                font-size: 14px;
            }
            .form-label {
                font-size: 14px;
                margin-bottom: 4px;
            }
            .movie-poster-small {
                width: 40px;
                height: 60px;
            }
            .search-result {
                padding: 8px !important;
            }
        }
        /* Loading spinner */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading.active {
            display: flex;
        }
        /* Source fields styling */
        .source-group {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        /* Sticky header for search results */
        .search-results-header {
            position: sticky;
            top: 0;
            background: #212529;
            padding: 10px 0;
            z-index: 100;
        }
    </style>
</head>
<body class="bg-dark text-light">
    <!-- Loading Spinner -->
    <div class="loading">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="container py-3">
        <h1 class="h3 mb-4">Add Manual Movie</h1>
        
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- TMDB Search Section -->
        <div class="card bg-dark border-secondary mb-4">
            <div class="card-body">
                <h3 class="card-title h5">Search TMDB</h3>
                <form method="POST" class="row g-2">
                    <div class="col-12">
                        <div class="input-group">
                            <input type="text" 
                                   name="search_query" 
                                   class="form-control" 
                                   placeholder="Enter movie name..." 
                                   required>
                            <button type="submit" 
                                    name="search_tmdb" 
                                    class="btn btn-primary">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </div>
                </form>

                <?php if (isset($search_results) && !empty($search_results)): ?>
                    <div class="search-results mt-3">
                        <div class="search-results-header">
                            <h4 class="h6">Search Results</h4>
                        </div>
                        <div class="list-group bg-dark">
                            <?php foreach ($search_results as $result): ?>
                                <div class="list-group-item bg-dark text-light search-result d-flex align-items-center gap-2 p-2" 
                                     onclick="fillTmdbId(<?php echo $result['id']; ?>)">
                                    <img src="https://image.tmdb.org/t/p/w92<?php echo $result['poster_path']; ?>" 
                                         class="movie-poster-small rounded"
                                         alt="<?php echo htmlspecialchars($result['title']); ?>"
                                         loading="lazy">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0"><?php echo $result['title']; ?></h6>
                                        <small class="text-muted">
                                            <?php echo substr($result['release_date'], 0, 4); ?>
                                            • Rating: <?php echo number_format($result['vote_average'], 1); ?>
                                        </small>
                                    </div>
                                    <i class="fas fa-chevron-right text-muted"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- TMDB Import Form -->
                <form method="POST" class="row g-3 mt-3">
                    <div class="col-md-6">
                        <label class="form-label">TMDB ID</label>
                        <input type="number" 
                               name="tmdb_id" 
                               id="tmdb_id" 
                               class="form-control" 
                               required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Custom Title (Optional)</label>
                        <input type="text" 
                               name="custom_title" 
                               class="form-control">
                        <div class="form-text text-muted">Leave empty to use original title</div>
                    </div>
                    <div class="col-12">
                        <button type="submit" 
                                name="import_tmdb" 
                                class="btn btn-primary w-100">
                            <i class="fas fa-download"></i> Import from TMDB
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Manual Addition Form -->
        <div class="card bg-dark border-secondary">
            <div class="card-body">
                <h3 class="card-title h5">Manual Addition</h3>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Title</label>
                            <input type="text" name="title" class="form-control" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Release Date</label>
                            <input type="date" name="release_date" class="form-control">
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Runtime (minutes)</label>
                            <input type="number" name="runtime" class="form-control">
                        </div>
                        
                        <div class="col-12">
                            <label class="form-label">Overview</label>
                            <textarea name="overview" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Poster Path</label>
                            <input type="text" name="poster_path" class="form-control">
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Backdrop Path</label>
                            <input type="text" name="backdrop_path" class="form-control">
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Rating</label>
                            <input type="number" name="rating" class="form-control" step="0.1" min="0" max="10">
                        </div>

                        <!-- Sources Section -->
                        <div class="col-12">
                            <h4 class="h6 mt-3">Movie Sources</h4>
                            <div id="sources-container">
                                <div class="source-group">
                                    <div class="row g-2">
                                        <div class="col-md-3">
                                            <input type="text" 
                                                   name="sources[0][server_name]" 
                                                   class="form-control" 
                                                   placeholder="Server Name">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" 
                                                   name="sources[0][url]" 
                                                   class="form-control" 
                                                   placeholder="Source URL">
                                        </div>
                                        <div class="col-md-3">
                                            <select name="sources[0][quality]" class="form-select">
                                                <option value="HD">HD</option>
                                                <option value="SD">SD</option>
                                                <option value="CAM">CAM</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" 
                                    class="btn btn-secondary btn-sm mt-2" 
                                    onclick="addSource()">
                                <i class="fas fa-plus"></i> Add Another Source
                            </button>
                        </div>

                        <div class="col-12 mt-4">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-save"></i> Save Movie
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fill TMDB ID function
        function fillTmdbId(id) {
            document.getElementById('tmdb_id').value = id;
            document.getElementById('tmdb_id').scrollIntoView({ behavior: 'smooth' });
        }

        // Add source field function
        function addSource() {
            const container = document.getElementById('sources-container');
            const sourceCount = container.children.length;
            
            const sourceGroup = document.createElement('div');
            sourceGroup.className = 'source-group';
            sourceGroup.innerHTML = `
                <div class="row g-2">
                    <div class="col-md-3">
                        <input type="text" 
                               name="sources[${sourceCount}][server_name]" 
                               class="form-control" 
                               placeholder="Server Name">
                    </div>
                    <div class="col-md-6">
                        <input type="text" 
                               name="sources[${sourceCount}][url]" 
                               class="form-control" 
                               placeholder="Source URL">
                    </div>
                    <div class="col-md-3">
                        <select name="sources[${sourceCount}][quality]" class="form-select">
                            <option value="HD">HD</option>
                            <option value="SD">SD</option>
                            <option value="CAM">CAM</option>
                        </select>
                    </div>
                </div>
                <button type="button" 
                        class="btn btn-danger btn-sm mt-2" 
                        onclick="this.parentElement.remove()">
                    <i class="fas fa-trash"></i> Remove
                </button>
            `;
            container.appendChild(sourceGroup);
        }

        // Show loading spinner on form submit
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', () => {
                document.querySelector('.loading').classList.add('active');
            });
        });
    </script>
</body>
</html>
