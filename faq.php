<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';
$auth = new Auth($db);

$page_title = 'সচরাচর জিজ্ঞাসিত প্রশ্নাবলী (FAQ)';
require_once 'includes/header.php';
?>

<div class="faq-wrapper">
    <div class="container py-5">
        <div class="faq-header text-center mb-5">
            <h1 class="main-title">সচরাচর জিজ্ঞাসিত প্রশ্নাবলী</h1>
            <p class="lead">আপনার সকল প্রশ্নের উত্তর এখানে</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="accordion custom-accordion" id="faqAccordion">
                    <!-- প্রাইসিং সম্পর্কিত 
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="pricing">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#pricingContent" aria-expanded="true" aria-controls="pricingContent">
                                <i class="fas fa-tags me-2"></i>প্রাইসিং সম্পর্কিত
                            </button>
                        </h2>
                        <div id="pricingContent" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <div class="faq-item mb-4">
                                    <h5>সাবস্ক্রিপশন প্ল্যানের মূল্য কত?</h5>
                                    <div class="pricing-cards">
                                        <div class="price-card">
                                            <h6>ফ্রি প্ল্যান</h6>
                                            <p class="price">৳০</p>
                                            <ul>
                                                <li>৪৮০p কোয়ালিটি</li>
                                                <li>বিজ্ঞাপনসহ</li>
                                            </ul>
                                        </div>
                                        <div class="price-card featured">
                                            <h6>প্রিমিয়াম প্ল্যান</h6>
                                            <p class="price">৳১৯৯/মাস</p>
                                            <ul>
                                                <li>১০৮০p HD কোয়ালিটি</li>
                                                <li>বিজ্ঞাপনমুক্ত</li>
                                                <li>ডাউনলোড সুবিধা</li>
                                            </ul>
                                        </div>
                                        <div class="price-card">
                                            <h6>VIP প্ল্যান</h6>
                                            <p class="price">৳২৯৯/মাস</p>
                                            <ul>
                                                <li>4K কোয়ালিটি</li>
                                                <li>সকল প্রিমিয়াম ফিচার</li>
                                                <li>আরলি অ্যাক্সেস</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> -->

                    <!-- স্মার্ট টিভিতে ব্যবহার -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="smartTv">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#smartTvContent" aria-expanded="false" aria-controls="smartTvContent">
                                <i class="fas fa-tv me-2"></i>স্মার্ট টিভিতে ব্যবহার
                            </button>
                        </h2>
                        <div id="smartTvContent" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <div class="tv-guide">
                                    <h5>এন্ড্রয়েড টিভিতে ব্যবহারের নির্দেশিকা</h5>
                                    
                                    <div class="method-card">
                                        <h6><i class="fas fa-globe"></i> রেকমেন্ডেড ব্রাউজার</h6>
                                        <ul>
                                            <li>Puffin TV Browser (সর্বোত্তম অপশন)</li>
                                            <li>TV Bro (ফ্রি অপশন)</li>
                                            <li>Kiwi Browser (এক্সটেনশন সাপোর্টসহ)</li>
                                        </ul>
                                    </div>

                                    <div class="method-card">
                                        <h6><i class="fas fa-mobile-alt"></i> স্ক্রিন মিররিং</h6>
                                        <ul>
                                            <li>Google Chromecast ব্যবহার করুন</li>
                                            <li>AirScreen অ্যাপ দিয়ে AirPlay/Miracast করুন</li>
                                            <li>Smart View (Samsung)/Screen Cast ব্যবহার করুন</li>
                                        </ul>
                                    </div>

                                    <div class="method-card">
                                        <h6><i class="fas fa-cog"></i> অতিরিক্ত টিপস</h6>
                                        <ul>
                                            <li>সর্বোচ্চ ইন্টারনেট স্পিড নিশ্চিত করুন</li>
                                            <li>ক্যাশ মেমরি রেগুলার ক্লিয়ার করুন</li>
                                            <li>অটো-প্লে সেটিংস অন করে রাখুন</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- অন্যান্য -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="others">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#othersContent" aria-expanded="false" aria-controls="othersContent">
                                <i class="fas fa-info-circle me-2"></i>অন্যান্য
                            </button>
                        </h2>
                        <div id="othersContent" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <!-- Add other FAQ items here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.faq-wrapper {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    min-height: 100vh;
    color: #e6e6e6;
}

.faq-header {
    padding: 3rem 0;
}

.main-title {
    color: #fff;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255,255,255,0.1);
    margin-bottom: 1rem;
}

.lead {
    color: #a8b2d1;
    font-size: 1.2rem;
}

.custom-accordion .accordion-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
}

.custom-accordion .accordion-button {
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    font-weight: 600;
    padding: 1.2rem;
    transition: all 0.3s ease;
}

.custom-accordion .accordion-button:not(.collapsed) {
    background: rgba(62, 84, 172, 0.2);
    color: #64ffda;
}

.custom-accordion .accordion-button::after {
    filter: invert(1);
}

.custom-accordion .accordion-body {
    background: rgba(0, 0, 0, 0.2);
    color: #ddd;
    padding: 1.5rem;
}

.price-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.price-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(100, 255, 218, 0.3);
}

.price-card.featured {
    background: linear-gradient(135deg, rgba(62, 84, 172, 0.2) 0%, rgba(100, 255, 218, 0.1) 100%);
    border-color: #64ffda;
}

.price-card h6 {
    color: #64ffda;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.price-card .price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 1rem;
}

.price-card ul {
    list-style: none;
    padding: 0;
}

.price-card ul li {
    color: #a8b2d1;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.price-card ul li:before {
    content: "→";
    color: #64ffda;
    position: absolute;
    left: 0;
}

.method-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.method-card h6 {
    color: #64ffda;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.method-card h6 i {
    margin-right: 0.5rem;
}

.method-card ul {
    list-style: none;
    padding: 0;
}

.method-card ul li {
    color: #a8b2d1;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.method-card ul li:before {
    content: "•";
    color: #64ffda;
    position: absolute;
    left: 0;
}

/* Hover Effects */
.accordion-button:hover {
    background: rgba(62, 84, 172, 0.1);
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.faq-item, .price-card, .method-card {
    animation: fadeIn 0.5s ease forwards;
}
</style>

<?php require_once 'includes/footer.php'; ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all accordions
    var accordionElements = document.querySelectorAll('.accordion-button');
    
    accordionElements.forEach(function(button) {
        button.addEventListener('click', function() {
            // Toggle aria-expanded attribute
            var isExpanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !isExpanded);
        });
    });
});
</script>
