<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$tmdb = new TMDBHandler();
$error = $success = '';
$results = [];
$total_pages = 0;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Handle bulk import
if (isset($_POST['bulk_import'])) {
    $year = $_POST['year'];
    $with_genres = $_POST['genres'] ?? '';
    $sort_by = $_POST['sort_by'] ?? 'popularity.desc';
    
    try {
        $endpoint = "discover/tv";
        $params = [
            'first_air_date_year' => $year,
            'with_genres' => $with_genres,
            'sort_by' => $sort_by,
            'page' => $current_page,
            'language' => 'en-US'
        ];
        
        $response = $tmdb->makeRequest($endpoint, $params);
        
        if (isset($response['results'])) {
            $results = $response['results'];
            $total_pages = $response['total_pages'];
            
            // Import selected series
            if (isset($_POST['selected_series']) && is_array($_POST['selected_series'])) {
                $imported_count = 0;
                $failed_count = 0;
                
                foreach ($_POST['selected_series'] as $tmdb_id) {
                    try {
                        $show_data = $tmdb->getTVShowById($tmdb_id);
                        
                        if ($show_data) {
                            // Check if series already exists
                            $stmt = $db->prepare("SELECT id FROM series WHERE tmdb_id = ?");
                            $stmt->execute([$tmdb_id]);
                            
                            if (!$stmt->fetch()) {
                                $db->beginTransaction();
                                
                                try {
                                    // Generate slug
                                    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $show_data['name'])));
                                    
                                    // Insert series
                                    $sql = "INSERT INTO series (tmdb_id, title, original_title, slug, overview, 
                                            poster_path, backdrop_path, first_air_date, status) 
                                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')";
                                    $stmt = $db->prepare($sql);
                                    $stmt->execute([
                                        $show_data['id'],
                                        $show_data['name'],
                                        $show_data['original_name'],
                                        $slug,
                                        $show_data['overview'],
                                        $show_data['poster_path'],
                                        $show_data['backdrop_path'],
                                        $show_data['first_air_date']
                                    ]);
                                    
                                    $series_id = $db->lastInsertId();
                                    
                                    // Import seasons and episodes
                                    if (isset($show_data['number_of_seasons'])) {
                                        for ($season_num = 1; $season_num <= $show_data['number_of_seasons']; $season_num++) {
                                            $season_data = $tmdb->getTVSeasonById($tmdb_id, $season_num);
                                            
                                            if ($season_data && isset($season_data['episodes'])) {
                                                // Insert season
                                                $stmt = $db->prepare("
                                                    INSERT INTO seasons (series_id, tmdb_id, season_number, name, 
                                                    overview, poster_path, air_date) 
                                                    VALUES (?, ?, ?, ?, ?, ?, ?)
                                                ");
                                                $stmt->execute([
                                                    $series_id,
                                                    $season_data['id'],
                                                    $season_num,
                                                    $season_data['name'],
                                                    $season_data['overview'],
                                                    $season_data['poster_path'],
                                                    $season_data['air_date']
                                                ]);
                                                
                                                $season_id = $db->lastInsertId();
                                                
                                                // Insert episodes
                                                foreach ($season_data['episodes'] as $episode) {
                                                    $stmt = $db->prepare("
                                                        INSERT INTO episodes (
                                                            series_id, season_id, tmdb_id, season_number, 
                                                            episode_number, title, overview, still_path, 
                                                            air_date, status
                                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
                                                    ");
                                                    $stmt->execute([
                                                        $series_id,
                                                        $season_id,
                                                        $episode['id'],
                                                        $season_num,
                                                        $episode['episode_number'],
                                                        $episode['name'],
                                                        $episode['overview'],
                                                        $episode['still_path'],
                                                        $episode['air_date']
                                                    ]);
                                                }
                                            }
                                        }
                                    }
                                    
                                    $db->commit();
                                    $imported_count++;
                                    
                                } catch (Exception $e) {
                                    $db->rollBack();
                                    $failed_count++;
                                    error_log("Import failed for series ID $tmdb_id: " . $e->getMessage());
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $failed_count++;
                        error_log("Failed to fetch series ID $tmdb_id: " . $e->getMessage());
                    }
                }
                
                $success = "Imported $imported_count series successfully. Failed: $failed_count";
            }
        }
    } catch (Exception $e) {
        $error = "API Error: " . $e->getMessage();
    }
}

// Get genres list
try {
    $genres_response = $tmdb->makeRequest('genre/tv/list');
    $genres = $genres_response['genres'] ?? [];
} catch (Exception $e) {
    $genres = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Import Series</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-dark text-light">
    <div class="container py-4">
        <h1 class="mb-4">Bulk Import TV Series</h1>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <form method="post" class="mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Year</label>
                    <input type="number" name="year" class="form-control" value="<?php echo date('Y'); ?>" required>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">Genre</label>
                    <select name="genres" class="form-select">
                        <option value="">All Genres</option>
                        <?php foreach ($genres as $genre): ?>
                            <option value="<?php echo $genre['id']; ?>"><?php echo htmlspecialchars($genre['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">Sort By</label>
                    <select name="sort_by" class="form-select">
                        <option value="popularity.desc">Popularity Descending</option>
                        <option value="vote_average.desc">Rating Descending</option>
                        <option value="first_air_date.desc">Release Date Descending</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" name="bulk_import" class="btn btn-primary w-100">Search</button>
                </div>
            </div>
        </form>
        
        <?php if ($results): ?>
            <form method="post">
                <input type="hidden" name="year" value="<?php echo $_POST['year'] ?? date('Y'); ?>">
                <input type="hidden" name="genres" value="<?php echo $_POST['genres'] ?? ''; ?>">
                <input type="hidden" name="sort_by" value="<?php echo $_POST['sort_by'] ?? 'popularity.desc'; ?>">
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>Results</h3>
                    <button type="submit" name="bulk_import" class="btn btn-success">
                        Import Selected Series
                    </button>
                </div>
                
                <div class="row g-4">
                    <?php foreach ($results as $show): ?>
                        <div class="col-md-3">
                            <div class="card bg-dark h-100">
                                <img src="https://image.tmdb.org/t/p/w500<?php echo $show['poster_path']; ?>" 
                                     class="card-img-top" alt="<?php echo htmlspecialchars($show['name']); ?>">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($show['name']); ?></h5>
                                    <p class="card-text small">
                                        <?php echo substr($show['overview'], 0, 100) . '...'; ?>
                                    </p>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="selected_series[]" value="<?php echo $show['id']; ?>" 
                                               id="series_<?php echo $show['id']; ?>">
                                        <label class="form-check-label" for="series_<?php echo $show['id']; ?>">
                                            Select for import
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                <?php for ($i = 1; $i <= min($total_pages, 10); $i++): ?>
                                    <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&year=<?php echo $_POST['year'] ?? date('Y'); ?>&genres=<?php echo $_POST['genres'] ?? ''; ?>&sort_by=<?php echo $_POST['sort_by'] ?? 'popularity.desc'; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            </form>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>