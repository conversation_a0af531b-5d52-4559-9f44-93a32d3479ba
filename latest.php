<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';

// Pagination settings
$items_per_page = 20;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $items_per_page;

// Get latest movies with COALESCE for dates
$movies_query = "SELECT 
                id,
                title,
                CASE 
                    WHEN poster_path LIKE 'http%' THEN poster_path 
                    WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END as poster_path,
                release_date as date_added,
                COALESCE(rating, 0) as rating,
                'movie' as content_type
                FROM movies 
                WHERE status = 'active'";

// Get latest series with COALESCE for dates
$series_query = "SELECT 
                id,
                title,
                CASE 
                    WHEN poster_path LIKE 'http%' THEN poster_path 
                    WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END as poster_path,
                first_air_date as date_added,
                0 as rating,
                'series' as content_type
                FROM series 
                WHERE status = 'active'";

// Combine movies and series
$combined_query = "($movies_query) UNION ALL ($series_query) 
                  ORDER BY date_added DESC 
                  LIMIT :offset, :limit";

try {
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as count FROM (($movies_query) UNION ALL ($series_query)) as combined";
    $total_count = $db->query($count_query)->fetch(PDO::FETCH_ASSOC)['count'];
    
    $total_pages = ceil($total_count / $items_per_page);

    // Get combined results
    $stmt = $db->prepare($combined_query);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    error_log($e->getMessage());
    $error = "An error occurred while fetching data: " . $e->getMessage();
}

$page_title = 'New & Popular';
require_once 'includes/header.php';
?>

<div class="container-fluid px-4 py-5">
    <h1 class="mb-4">New & Popular</h1>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php else: ?>
        <div class="row row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-lg-5 row-cols-xl-6 g-4">
            <?php foreach ($results as $item): ?>
                <div class="col">
                    <div class="card h-100 bg-dark text-white border-0 content-card">
                        <a href="<?php echo $item['content_type'] === 'movie' ? 'movie.php?id=' : 'show.php?id='; ?><?php echo $item['id']; ?>" 
                           class="text-decoration-none">
                            <img src="<?php echo htmlspecialchars($item['poster_path']); ?>" 
                                 class="card-img-top" 
                                 alt="<?php echo htmlspecialchars($item['title']); ?>"
                                 loading="lazy"
                                 onerror="this.src='assets/images/default-poster.jpg'">
                            
                            <div class="card-body">
                                <div class="content-type-badge <?php echo $item['content_type']; ?>">
                                    <?php echo ucfirst($item['content_type']); ?>
                                </div>
                                <h5 class="card-title text-white"><?php echo htmlspecialchars($item['title']); ?></h5>
                                <div class="meta-info">
                                    <?php if (!empty($item['date_added'])): ?>
                                        <span class="year"><?php echo date('Y', strtotime($item['date_added'])); ?></span>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($item['rating'])): ?>
                                        <span class="rating">
                                            <i class="fas fa-star text-warning"></i>
                                            <?php echo number_format($item['rating'], 1); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page - 1; ?>">Previous</a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page + 1; ?>">Next</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.content-card {
    transition: transform 0.2s;
    position: relative;
    overflow: hidden;
}

.content-card:hover {
    transform: scale(1.05);
}

.card-img-top {
    aspect-ratio: 2/3;
    object-fit: cover;
}

.content-type-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.content-type-badge.movie {
    background-color: #e50914;
    color: white;
}

.content-type-badge.series {
    background-color: #1db954;
    color: white;
}

.card-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.meta-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #aaa;
}

.pagination .page-link {
    background-color: #222;
    border-color: #444;
    color: #fff;
}

.pagination .page-item.active .page-link {
    background-color: #e50914;
    border-color: #e50914;
}

.pagination .page-link:hover {
    background-color: #444;
    border-color: #666;
    color: #fff;
}
</style>

<?php require_once 'includes/footer.php'; ?>
