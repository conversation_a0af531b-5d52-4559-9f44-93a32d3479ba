<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/auth.php';

$auth = new Auth($db);

// লগইন চেক করা
if (!$auth->isLoggedIn()) {
    // বর্তমান URL সেশনে সেভ করা যাতে লগইন পরে এখানে ফিরে আসতে পারে
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    // লগইন পেজে রিডাইরেক্ট
    header('Location: login.php');
    exit;
}

$movie_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$user_id = $auth->getCurrentUserId();

// Get movie details
$stmt = $db->prepare("
    SELECT m.*, ms.source_url, ms.quality 
    FROM manual_movies m 
    LEFT JOIN manual_movie_sources ms ON m.id = ms.movie_id 
    WHERE m.id = ? AND m.status = 'active'
    LIMIT 1
");
$stmt->execute([$movie_id]);
$movie = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$movie) {
    header('Location: 404.php');
    exit;
}

// Get related manual movies
$stmt = $db->prepare("
    SELECT * FROM manual_movies 
    WHERE status = 'active' 
    AND id != ? 
    ORDER BY created_at DESC 
    LIMIT 6
");
$stmt->execute([$movie_id]);
$related_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = $movie['title'];
require_once 'includes/header.php';
?>

<div class="container-fluid p-0">
    <!-- Player Section -->
    <div class="player-wrapper">
        <div class="player-container">
            <video id="player" playsinline controls>
                <source src="<?php echo htmlspecialchars($movie['source_url']); ?>" type="video/mp4" size="<?php echo htmlspecialchars($movie['quality']); ?>">
            </video>
        </div>
    </div>

    <!-- Quality/Server Selection Section -->
    <div class="quality-server-section py-3">
        <div class="container">
            <div class="d-flex justify-content-center align-items-center flex-wrap gap-3">
                <?php
                // Get all available servers for this movie
                $stmt = $db->prepare("
                    SELECT DISTINCT server_name, source_url 
                    FROM manual_movie_sources 
                    WHERE movie_id = ? 
                    ORDER BY server_name
                ");
                $stmt->execute([$movie_id]);
                $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                ?>

                <?php if (!empty($servers)): ?>
                <div class="server-buttons">
                    <span class="section-label">Server:</span>
                    <?php foreach ($servers as $s): ?>
                        <button class="btn btn-server <?php echo ($s['source_url'] === $movie['source_url']) ? 'active' : ''; ?>"
                                data-url="<?php echo htmlspecialchars($s['source_url']); ?>">
                            <?php echo htmlspecialchars($s['server_name']); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Download Section - Moved here, after server selection -->
    <div class="download-section mt-4 p-4 bg-dark rounded">
        <div class="download-instructions mb-3">
            <h5 class="text-white mb-3"><i class="fas fa-download me-2"></i>ডাউনলোড নির্দেশনা</h5>
            <ol class="text-light mb-4">
                <li>নিচের "ডাউনলোড করুন" বাটনে ক্লিক করুন</li>
                <li>টেলিগ্রাম বটে /start কমান্ড দিন</li>
                <li>মুভির নাম টাইপ করে সার্চ করুন</li>
                <li>আপনার কাঙ্ক্ষিত মুভি সিলেক্ট করে ডাউনলোড করুন</li>
            </ol>
        </div>
        
        <div class="text-center">
            <a href="https://t.me/cinepix_premium_bot" 
               class="btn btn-danger btn-lg download-btn" 
               target="_blank">
                <i class="fab fa-telegram me-2"></i>ডাউনলোড করুন
            </a>
        </div>
    </div>

    <!-- Movie Details Section -->
    <div class="movie-details py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <div class="movie-poster">
                        <?php
                        $poster_url = !empty($movie['poster_path']) ? $movie['poster_path'] : 'assets/images/default-poster.jpg';
                        if (!empty($poster_url) && $poster_url !== 'assets/images/default-poster.jpg') {
                            if (!filter_var($poster_url, FILTER_VALIDATE_URL)) {
                                $poster_url = 'https://image.tmdb.org/t/p/w500' . $poster_url;
                            }
                        }
                        ?>
                        <img src="<?php echo htmlspecialchars($poster_url); ?>" 
                             alt="<?php echo htmlspecialchars($movie['title']); ?>" 
                             class="img-fluid rounded"
                             onerror="this.src='assets/images/default-poster.jpg'">
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="movie-info-content">
                        <h1 class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></h1>
                        
                        <div class="movie-meta">
                            <?php if (!empty($movie['release_date'])): ?>
                            <span class="meta-item">
                                <i class="fas fa-calendar-alt"></i> 
                                <?php echo date('Y', strtotime($movie['release_date'])); ?>
                            </span>
                            <?php endif; ?>
                            
                            <?php if (!empty($movie['runtime'])): ?>
                            <span class="meta-item">
                                <i class="fas fa-clock"></i> 
                                <?php echo $movie['runtime']; ?> minutes
                            </span>
                            <?php endif; ?>
                            
                            <?php if (!empty($movie['quality'])): ?>
                            <span class="meta-item">
                                <i class="fas fa-video"></i> 
                                Quality: <?php echo htmlspecialchars($movie['quality']); ?>
                            </span>
                            <?php endif; ?>
                            
                            <?php if (!empty($movie['genre'])): ?>
                            <span class="meta-item">
                                <i class="fas fa-film"></i> 
                                <?php echo htmlspecialchars($movie['genre']); ?>
                            </span>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($movie['overview'])): ?>
                        <div class="movie-overview mt-4">
                            <h3>Overview</h3>
                            <p><?php echo nl2br(htmlspecialchars($movie['overview'])); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Movies Section -->
    <?php if (!empty($related_movies)): ?>
    <div class="related-movies py-4">
        <div class="container">
            <h3 class="section-title">More Manual Movies</h3>
            <div class="row">
                <?php foreach ($related_movies as $related): ?>
                <div class="col-md-2 col-sm-4 col-6 mb-4">
                    <div class="movie-card">
                        <a href="manual_player.php?id=<?php echo $related['id']; ?>">
                            <?php
                            $related_poster = !empty($related['poster_path']) ? $related['poster_path'] : 'assets/images/default-poster.jpg';
                            if (!empty($related_poster) && $related_poster !== 'assets/images/default-poster.jpg') {
                                if (!filter_var($related_poster, FILTER_VALIDATE_URL)) {
                                    $related_poster = 'https://image.tmdb.org/t/p/w500' . $related_poster;
                                }
                            }
                            ?>
                            <img src="<?php echo htmlspecialchars($related_poster); ?>" 
                                 alt="<?php echo htmlspecialchars($related['title']); ?>" 
                                 class="img-fluid rounded"
                                 onerror="this.src='assets/images/default-poster.jpg'">
                            <h5 class="movie-card-title"><?php echo htmlspecialchars($related['title']); ?></h5>
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
body {
    background-color: #141414;
    color: #fff;
}

.player-wrapper {
    padding: 20px 0;
}

.player-container {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
}

#player {
    width: 100%;
    height: 100%;
}

/* Mobile styles */
@media (max-width: 768px) {
    .player-wrapper {
        padding: 0;
    }
    
    .player-container {
        max-width: 100%;
        aspect-ratio: 16/9;
    }
}

.quality-server-section {
    background: #1a1a1a;
    border-bottom: 1px solid #2a2a2a;
}

.section-label {
    color: #aaa;
    margin-right: 10px;
    font-size: 0.9rem;
}

.btn-quality,
.btn-server {
    background: #2a2a2a;
    color: #fff;
    border: none;
    padding: 5px 15px;
    margin: 0 5px;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-quality:hover,
.btn-server:hover {
    background: #e50914;
    color: #fff;
}

.btn-quality.active,
.btn-server.active {
    background: #e50914;
    color: #fff;
}

.movie-details {
    background-color: #1a1a1a;
}

.movie-poster img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0,0,0,0.4);
}

.movie-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fff;
}

.movie-meta {
    margin-bottom: 1.5rem;
}

.meta-item {
    display: inline-block;
    margin-right: 20px;
    color: #aaa;
}

.meta-item i {
    color: #e50914;
    margin-right: 5px;
}

.movie-overview h3 {
    color: #e50914;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.movie-overview p {
    color: #ddd;
    line-height: 1.6;
}

.related-movies {
    background-color: #141414;
}

.section-title {
    color: #fff;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.movie-card {
    transition: transform 0.3s ease;
}

.movie-card:hover {
    transform: scale(1.05);
}

.movie-card a {
    text-decoration: none;
}

.movie-card-title {
    color: #fff;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Custom Plyr Styling */
.plyr--video {
    border-radius: 8px;
    overflow: hidden;
}

.plyr--full-ui input[type=range] {
    color: #e50914;
}

.plyr__control--overlaid {
    background: rgba(229, 9, 20, 0.8);
}

.plyr--video .plyr__control:hover {
    background: #e50914;
}

/* Style for rewind and forward buttons */
.plyr__controls button[data-plyr="rewind"],
.plyr__controls button[data-plyr="fast-forward"] {
    padding: 6px;
}

.plyr__controls button[data-plyr="rewind"]::before {
    content: "⟲10";
    font-size: 14px;
}

.plyr__controls button[data-plyr="fast-forward"]::before {
    content: "⟳10";
    font-size: 14px;
}

@media (max-width: 768px) {
    .movie-title {
        font-size: 1.8rem;
    }
    
    .movie-poster {
        margin-bottom: 20px;
    }
    
    .meta-item {
        display: block;
        margin-bottom: 10px;
    }

    .quality-buttons,
    .server-buttons {
        width: 100%;
        margin: 10px 0;
        text-align: center;
    }
    
    .btn-quality,
    .btn-server {
        margin: 5px;
    }
}

.download-section {
    background-color: #141414 !important;
    border: 1px solid rgba(255,255,255,0.1);
}

.download-btn {
    background-color: #e50914;
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background-color: #ff0a16;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(229,9,20,0.3);
}

.download-instructions ol {
    padding-left: 20px;
}

.download-instructions li {
    margin-bottom: 8px;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .download-section {
        padding: 15px !important;
    }
    
    .download-instructions h5 {
        font-size: 1.1rem;
    }
    
    .download-instructions li {
        font-size: 0.9rem;
    }
}
</style>

<script src="https://cdn.plyr.io/3.7.8/plyr.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const player = new Plyr('#player', {
        controls: [
            'play-large',
            'play',
            'progress',
            'current-time',
            'duration',
            'mute',
            'volume',
            'settings',
            'pip',
            'fullscreen'
        ],
        settings: ['quality', 'speed'],
        speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 2] },
        keyboard: { focused: true, global: true },
        tooltips: { controls: true, seek: true },
        quality: {
            default: '<?php echo htmlspecialchars($movie['quality']); ?>',
            options: ['<?php echo htmlspecialchars($movie['quality']); ?>']
        }
    });

    const movieId = <?php echo json_encode($movie_id); ?>;
    const storageKey = `movie_position_${movieId}`;
    
    // Check for saved position when page loads
    const savedPosition = localStorage.getItem(storageKey);
    if (savedPosition) {
        // Create and show popup
        const popup = document.createElement('div');
        popup.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 9999;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        `;
        
        const minutes = Math.floor(savedPosition / 60);
        popup.innerHTML = `
            <div style="margin-bottom: 10px;">
                আপনি এই মুভিটি ${minutes} মিনিট পর্যন্ত দেখেছিলেন।
            </div>
            <div style="display: flex; gap: 10px;">
                <button id="resumeBtn" style="background: #e50914; border: none; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                    সেখান থেকে দেখুন
                </button>
                <button id="dismissBtn" style="background: #333; border: none; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                    শুরু থেকে দেখুন
                </button>
            </div>
        `;
        
        document.body.appendChild(popup);
        
        // Handle resume button click with improved seeking
        document.getElementById('resumeBtn').addEventListener('click', () => {
            const seekTo = parseFloat(savedPosition);
            
            // Wait for player to be ready
            player.once('ready', () => {
                player.currentTime = seekTo;
                // Force play after seeking
                player.play().catch(() => {
                    console.log('Playback failed, trying again...');
                    setTimeout(() => player.play(), 1000);
                });
            });

            // Backup timeout in case ready event doesn't fire
            setTimeout(() => {
                if (player.currentTime !== seekTo) {
                    player.currentTime = seekTo;
                    player.play().catch(() => {
                        console.log('Playback failed on timeout');
                    });
                }
            }, 2000);

            popup.remove();
        });
        
        // Handle dismiss button click
        document.getElementById('dismissBtn').addEventListener('click', () => {
            localStorage.removeItem(storageKey);
            popup.remove();
        });
    }
    
    // Save position every 5 seconds while playing
    let saveInterval;
    player.on('play', () => {
        saveInterval = setInterval(() => {
            if (!player.seeking) { // Only save if not currently seeking
                localStorage.setItem(storageKey, player.currentTime.toString());
            }
        }, 5000);
    });
    
    player.on('pause', () => {
        clearInterval(saveInterval);
        if (!player.seeking) { // Only save if not currently seeking
            localStorage.setItem(storageKey, player.currentTime.toString());
        }
    });
    
    // Additional event listeners for better position tracking
    player.on('seeked', () => {
        localStorage.setItem(storageKey, player.currentTime.toString());
    });

    // Clear position when video ends
    player.on('ended', () => {
        localStorage.removeItem(storageKey);
        clearInterval(saveInterval);
    });

    // Add custom controls for 10-second skip
    const controls = player.elements.controls;
    const playButton = controls.querySelector('.plyr__controls button[data-plyr="play"]');
    
    // Create rewind button
    const rewindButton = document.createElement('button');
    rewindButton.type = 'button';
    rewindButton.className = 'plyr__control';
    rewindButton.setAttribute('data-plyr', 'rewind');
    
    // Create forward button
    const forwardButton = document.createElement('button');
    forwardButton.type = 'button';
    forwardButton.className = 'plyr__control';
    forwardButton.setAttribute('data-plyr', 'fast-forward');

    // Insert buttons around play button
    playButton.parentNode.insertBefore(rewindButton, playButton);
    playButton.parentNode.insertBefore(forwardButton, playButton.nextSibling);

    // Add click handlers
    const seekTime = 10; // 10 seconds
    rewindButton.addEventListener('click', () => {
        player.currentTime = Math.max(player.currentTime - seekTime, 0);
    });

    forwardButton.addEventListener('click', () => {
        player.currentTime = Math.min(player.currentTime + seekTime, player.duration);
    });

    // Auto-hide cursor
    const container = document.querySelector('.player-container');
    let timeout;
    
    function hideCursor() {
        container.style.cursor = 'none';
    }
    
    function showCursor() {
        container.style.cursor = 'default';
        clearTimeout(timeout);
        timeout = setTimeout(hideCursor, 3000);
    }

    container.addEventListener('mousemove', showCursor);
    player.on('play', () => {
        timeout = setTimeout(hideCursor, 3000);
    });
    player.on('pause', () => {
        showCursor();
        clearTimeout(timeout);
    });

    // Server button functionality only
    const serverButtons = document.querySelectorAll('.btn-server');
    const videoPlayer = document.querySelector('#player');

    function updateVideoSource(url) {
        const currentTime = videoPlayer.currentTime;
        const wasPlaying = !videoPlayer.paused;
        
        videoPlayer.src = url;
        videoPlayer.load();
        
        videoPlayer.addEventListener('loadedmetadata', function onLoaded() {
            videoPlayer.currentTime = currentTime;
            if (wasPlaying) videoPlayer.play();
            videoPlayer.removeEventListener('loadedmetadata', onLoaded);
        });
    }

    function updateActiveButton(buttons, clickedButton) {
        buttons.forEach(btn => btn.classList.remove('active'));
        clickedButton.classList.add('active');
    }

    serverButtons.forEach(button => {
        button.addEventListener('click', function() {
            updateActiveButton(serverButtons, this);
            updateVideoSource(this.dataset.url);
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>


