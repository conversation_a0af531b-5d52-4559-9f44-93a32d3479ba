<?php
class AdminAuth {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
        if (session_status() === PHP_SESSION_NONE) {
            ini_set('session.gc_maxlifetime', 28800); // 8 hours
            session_set_cookie_params(28800);
            session_start();
        }
    }

    public function checkAdminLogin() {
        if (!isset($_SESSION['admin_id'])) {
            header("Location: /admin/login.php");
            exit();
        }

        // Verify admin still exists and is active
        $stmt = $this->db->prepare("SELECT status FROM admins WHERE id = ? AND status = 'active' LIMIT 1");
        $stmt->execute([$_SESSION['admin_id']]);
        if (!$stmt->fetch()) {
            $this->logout();
            header("Location: /admin/login.php");
            exit();
        }

        // Update last activity
        $_SESSION['last_activity'] = time();
    }

    public function logout() {
        $_SESSION = array();
        session_destroy();
        header("Location: /admin/login.php");
        exit();
    }
}
?> 