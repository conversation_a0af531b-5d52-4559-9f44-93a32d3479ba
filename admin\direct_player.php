<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/tmdb_handler.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$content_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? $_GET['type'] : 'movie';

// Get content details
if ($type === 'movie') {
    $stmt = $db->prepare("SELECT id, title, poster_path, tmdb_id FROM movies WHERE id = ?");
} else {
    $stmt = $db->prepare("SELECT id, title, poster_path, tmdb_id FROM series WHERE id = ?");
}
$stmt->execute([$content_id]);
$content = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$content) {
    die("Content not found");
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_source'])) {
    $video_url = trim($_POST['video_url']);
    $quality = trim($_POST['quality']);
    $subtitle_url = !empty($_POST['subtitle_url']) ? trim($_POST['subtitle_url']) : null;

    try {
        $stmt = $db->prepare("
            INSERT INTO direct_sources (content_type, content_id, video_url, quality, subtitle_url) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$type, $content_id, $video_url, $quality, $subtitle_url]);
        $success_message = "Source added successfully!";
    } catch (PDOException $e) {
        $error_message = "Error adding source: " . $e->getMessage();
    }
}

// Get existing sources
$stmt = $db->prepare("
    SELECT * FROM direct_sources 
    WHERE content_type = ? AND content_id = ? 
    ORDER BY quality DESC
");
$stmt->execute([$type, $content_id]);
$sources = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Player Management - <?php echo htmlspecialchars($content['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.plyr.io/3.7.8/plyr.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #fff;
        }
        .player-wrapper {
            position: relative;
            padding-top: 56.25%;
            background: #000;
        }
        .player-wrapper video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .quality-btn {
            margin: 5px;
            padding: 5px 15px;
            border: 1px solid #444;
            background: transparent;
            color: #fff;
            cursor: pointer;
            border-radius: 3px;
        }
        .quality-btn.active {
            background: #007bff;
        }
        .form-control {
            background-color: #2d2d2d;
            border: 1px solid #444;
            color: #fff;
        }
        .form-control:focus {
            background-color: #2d2d2d;
            border-color: #007bff;
            color: #fff;
        }
        .card {
            background-color: #2d2d2d;
            border: 1px solid #444;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-8">
                <!-- Video Player -->
                <div class="player-wrapper">
                    <video id="player" playsinline controls>
                        <?php if ($sources): ?>
                            <?php foreach ($sources as $source): ?>
                                <source src="<?php echo htmlspecialchars($source['video_url']); ?>" 
                                        type="video/mp4" 
                                        size="<?php echo htmlspecialchars($source['quality']); ?>">
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </video>
                </div>

                <!-- Quality Selector -->
                <?php if ($sources): ?>
                <div class="quality-selector mt-3">
                    <?php foreach ($sources as $source): ?>
                        <button class="quality-btn" data-quality="<?php echo htmlspecialchars($source['quality']); ?>">
                            <?php echo htmlspecialchars($source['quality']); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>

            <div class="col-md-4">
                <!-- Content Info -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h4><?php echo htmlspecialchars($content['title']); ?></h4>
                        <p class="text-muted">ID: <?php echo $content['id']; ?> | TMDB: <?php echo $content['tmdb_id']; ?></p>
                    </div>
                </div>

                <!-- Add New Source Form -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Add New Source</h5>
                        
                        <?php if (isset($success_message)): ?>
                            <div class="alert alert-success"><?php echo $success_message; ?></div>
                        <?php endif; ?>
                        
                        <?php if (isset($error_message)): ?>
                            <div class="alert alert-danger"><?php echo $error_message; ?></div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="mb-3">
                                <label for="video_url" class="form-label">Video URL (MP4)</label>
                                <input type="url" class="form-control" id="video_url" name="video_url" required>
                            </div>
                            <div class="mb-3">
                                <label for="quality" class="form-label">Quality</label>
                                <select class="form-control" id="quality" name="quality" required>
                                    <option value="2160p">2160p (4K)</option>
                                    <option value="1080p">1080p</option>
                                    <option value="720p">720p</option>
                                    <option value="480p">480p</option>
                                    <option value="360p">360p</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="subtitle_url" class="form-label">Subtitle URL (.srt, .vtt) - Optional</label>
                                <input type="url" class="form-control" id="subtitle_url" name="subtitle_url">
                            </div>
                            <button type="submit" name="add_source" class="btn btn-primary">Add Source</button>
                        </form>
                    </div>
                </div>

                <!-- Existing Sources -->
                <?php if ($sources): ?>
                <div class="card mt-4">
                    <div class="card-body">
                        <h5 class="card-title">Existing Sources</h5>
                        <div class="list-group">
                            <?php foreach ($sources as $source): ?>
                            <div class="list-group-item bg-dark text-white">
                                <h6 class="mb-1">Quality: <?php echo htmlspecialchars($source['quality']); ?></h6>
                                <p class="mb-1 text-truncate">URL: <?php echo htmlspecialchars($source['video_url']); ?></p>
                                <?php if ($source['subtitle_url']): ?>
                                <p class="mb-1 text-truncate">Subtitle: <?php echo htmlspecialchars($source['subtitle_url']); ?></p>
                                <?php endif; ?>
                                <form method="POST" class="mt-2" onsubmit="return confirm('Are you sure you want to delete this source?');">
                                    <input type="hidden" name="delete_source" value="<?php echo $source['id']; ?>">
                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                </form>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.plyr.io/3.7.8/plyr.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Plyr
            const player = new Plyr('#player', {
                controls: [
                    'play-large', 'play', 'progress', 'current-time', 'duration',
                    'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
                ],
                settings: ['quality', 'speed', 'loop'],
            });

            // Quality button handling
            const qualityButtons = document.querySelectorAll('.quality-btn');
            qualityButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const quality = button.dataset.quality;
                    const sources = player.media.querySelectorAll('source');
                    sources.forEach(source => {
                        if (source.getAttribute('size') === quality) {
                            player.source = {
                                type: 'video',
                                sources: [{
                                    src: source.src,
                                    type: 'video/mp4',
                                    size: quality
                                }]
                            };
                        }
                    });

                    // Update active button
                    qualityButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>