<?php
class TMDBHandler {
    private $api_key;
    private $base_url = 'https://api.themoviedb.org/3/';

    public function __construct() {
        $this->api_key = '3d36f64b789ec5484c76838f0ba11daf';
    }

    public function makeRequest($endpoint, $params = []) {
        $params['api_key'] = $this->api_key;
        
        // If endpoint is a full URL, use it directly
        $url = strpos($endpoint, 'http') === 0 ? $endpoint : $this->base_url . $endpoint;
        
        // Add parameters to URL
        $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        
        if (curl_errno($ch)) {
            throw new Exception(curl_error($ch));
        }
        
        curl_close($ch);
        
        $data = json_decode($response, true);
        
        if (!$data) {
            throw new Exception("Failed to decode JSON response");
        }
        
        return $data;
    }

    public function getMovieById($id) {
        try {
            return $this->makeRequest("movie/$id", [
                'language' => 'en-US'
            ]);
        } catch (Exception $e) {
            error_log("TMDB API Error: " . $e->getMessage());
            return null;
        }
    }

    public function getTVShowById($id) {
        try {
            $response = $this->makeRequest("tv/$id", [
                'append_to_response' => 'credits,keywords,external_ids,videos,images,seasons'
            ]);

            if (!isset($response['id'])) {
                return false;
            }

            return $response;
        } catch (Exception $e) {
            error_log("TMDB Error (getTVShowById): " . $e->getMessage());
            return false;
        }
    }

    public function getTVSeasonById($series_id, $season_number) {
        try {
            // Log the API request
            error_log("Fetching season data for Series ID: {$series_id}, Season: {$season_number}");
            
            $response = $this->makeRequest("tv/{$series_id}/season/{$season_number}");
            
            // Log the response
            error_log("TMDB Season Response: " . json_encode($response));
            
            if (!$response || !isset($response['episodes'])) {
                error_log("Invalid season response for Series {$series_id}, Season {$season_number}");
                return null;
            }

            return $response;
        } catch (Exception $e) {
            error_log("Error getting season details: " . $e->getMessage());
            return null;
        }
    }

    private function addContentServers($content_id, $content_type) {
        // Get active servers
        $stmt = $this->db->prepare("
            SELECT id, name FROM servers 
            WHERE status = 'active' 
            AND (content_type = ? OR content_type = 'all')
        ");
        $stmt->execute([$content_type]);
        
        while ($server = $stmt->fetch()) {
            // Generate server URL
            $server_url = $this->generateServerUrl($content_id, $server['name'], $content_type);
            
            // Insert into content_servers
            $this->db->prepare("
                INSERT INTO content_servers (content_id, server_id, content_type, url) 
                VALUES (?, ?, ?, ?)
            ")->execute([$content_id, $server['id'], $content_type, $server_url]);
        }
    }

    private function generateServerUrl($tmdb_id, $server_name, $type = 'movie') {
        $content_type = $type === 'movie' ? 'movie' : 'tv';
        
        switch (strtolower($server_name)) {
            case 'vidsrc':
                return "https://vidsrc.xyz/embed/{$content_type}?tmdb=" . $tmdb_id;
            case 'superembed':
                return "https://superembed.stream/{$content_type}/" . $tmdb_id;
            case 'vidsrc.me':
                return "https://vidsrc.me/embed/{$content_type}?tmdb=" . $tmdb_id;
            case 'vidplay':
                return "https://vidplay.site/e/{$content_type}?tmdb=" . $tmdb_id;
            case 'filelions':
                return "https://filelions.to/v/{$content_type}/" . $tmdb_id;
            default:
                return "";
        }
    }

    public function importMovie($db, $tmdb_id) {
        try {
            $movie_data = $this->getMovieById($tmdb_id);
            if (!$movie_data) {
                return ['success' => false, 'message' => 'Movie not found on TMDB'];
            }

            // Generate slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $movie_data['title'])));

            // Check if movie already exists
            $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
            $stmt->execute([$tmdb_id]);
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'Movie already exists'];
            }

            // Insert movie
            $stmt = $db->prepare("
                INSERT INTO movies (
                    tmdb_id, title, original_title, slug, overview,
                    poster_path, backdrop_path, release_date, runtime,
                    rating, genres, language, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");

            $stmt->execute([
                $movie_data['id'],
                $movie_data['title'],
                $movie_data['original_title'],
                $slug,
                $movie_data['overview'],
                $movie_data['poster_path'],
                $movie_data['backdrop_path'],
                $movie_data['release_date'],
                $movie_data['runtime'],
                $movie_data['vote_average'],
                json_encode($movie_data['genres']),
                $movie_data['original_language']
            ]);

            $movie_id = $db->lastInsertId();

            // Add content servers
            $this->addContentServers($movie_id, 'movie');

            return [
                'success' => true, 
                'message' => 'Movie imported successfully',
                'movie_id' => $movie_id
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function importSeries($db, $tmdb_id) {
        try {
            // Get series details with all seasons
            $series_data = $this->makeRequest("tv/{$tmdb_id}");
            if (!$series_data) {
                return ['success' => false, 'message' => 'Series not found on TMDB'];
            }

            $db->beginTransaction();

            // 1. Insert series
            $slug = $this->createSlug($series_data['name']);
            $series_sql = "INSERT INTO series (tmdb_id, title, original_title, slug, overview, poster_path, backdrop_path, first_air_date, last_air_date) 
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $db->prepare($series_sql);
            $stmt->execute([
                $tmdb_id,
                $series_data['name'],
                $series_data['original_name'] ?? $series_data['name'],
                $slug,
                $series_data['overview'],
                $series_data['poster_path'],
                $series_data['backdrop_path'],
                $series_data['first_air_date'],
                $series_data['last_air_date']
            ]);

            $series_id = $db->lastInsertId();

            // 2. Insert seasons
            foreach ($series_data['seasons'] as $season) {
                // Skip season 0 (specials)
                if ($season['season_number'] == 0) {
                    continue;
                }

                // Insert season
                $season_sql = "INSERT INTO seasons (
                    series_id, season_number, name, overview, 
                    poster_path, air_date
                ) VALUES (?, ?, ?, ?, ?, ?)";
                
                $stmt = $db->prepare($season_sql);
                $stmt->execute([
                    $series_id,
                    $season['season_number'],
                    $season['name'],
                    $season['overview'] ?? '',
                    $season['poster_path'],
                    $season['air_date']
                ]);
                
                $season_id = $db->lastInsertId();
                
                // Get detailed season info including episodes
                $season_details = $this->makeRequest("tv/{$tmdb_id}/season/{$season['season_number']}");
                
                if (!$season_details || empty($season_details['episodes'])) {
                    continue;
                }

                // Insert episodes for this season
                foreach ($season_details['episodes'] as $episode) {
                    $episode_sql = "INSERT INTO episodes (
                        series_id, season_id, tmdb_id, episode_number,
                        title, overview, still_path, air_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $stmt = $db->prepare($episode_sql);
                    $stmt->execute([
                        $series_id,
                        $season_id, // Using the correct season_id for each episode
                        $episode['id'],
                        $episode['episode_number'],
                        $episode['name'],
                        $episode['overview'] ?? '',
                        $episode['still_path'] ?? null,
                        $episode['air_date'] ?? null
                    ]);
                }
            }

            // Add content servers
            $this->addContentServers($series_id, 'tv');

            $db->commit();
            return [
                'success' => true, 
                'message' => 'Series imported successfully',
                'series_id' => $series_id
            ];

        } catch (Exception $e) {
            $db->rollBack();
            error_log("Error importing series: " . $e->getMessage());
            return [
                'success' => false, 
                'message' => 'Error importing series: ' . $e->getMessage()
            ];
        }
    }

    public function getSeasonDetails($series_id, $season_number) {
        try {
            return $this->makeRequest("tv/$series_id/season/$season_number", [
                'append_to_response' => 'credits,videos'
            ]);
        } catch (Exception $e) {
            error_log("Error getting season details: " . $e->getMessage());
            return false;
        }
    }

    public function searchTV($query, $page = 1) {
        $url = "https://api.themoviedb.org/3/search/tv";
        $params = [
            'api_key' => $this->api_key,
            'query' => $query,
            'page' => $page,
            'include_adult' => false,
            'language' => 'en-US'
        ];
        
        try {
            $response = $this->makeRequest($url, $params);
            if (!$response || !isset($response['results'])) {
                return [];
            }

            $results = [];
            foreach ($response['results'] as $show) {
                $results[] = [
                    'id' => $show['id'],
                    'title' => $show['name'],
                    'original_title' => $show['original_name'],
                    'overview' => $show['overview'],
                    'poster_path' => $show['poster_path'] ? 'https://image.tmdb.org/t/p/w500' . $show['poster_path'] : null,
                    'backdrop_path' => $show['backdrop_path'] ? 'https://image.tmdb.org/t/p/original' . $show['backdrop_path'] : null,
                    'first_air_date' => $show['first_air_date'] ?? null,
                    'vote_average' => $show['vote_average'] ?? 0,
                    'type' => 'series'
                ];
            }
            
            return [
                'results' => $results,
                'total_pages' => $response['total_pages'],
                'total_results' => $response['total_results'],
                'page' => $response['page']
            ];
        } catch (Exception $e) {
            error_log("TMDB TV Search Error: " . $e->getMessage());
            return [];
        }
    }

    private function getSeriesById($id) {
        return $this->makeRequest("tv/$id", [
            'append_to_response' => 'seasons'
        ]);
    }

    private function createSlug($string) {
        $string = preg_replace('/[^\p{L}\p{N}\s-]/u', '', $string);
        $string = mb_strtolower($string, 'UTF-8');
        $string = preg_replace('/[\s-]+/', '-', $string);
        return trim($string, '-');
    }

    public function searchMovie($query, $page = 1) {
        try {
            return $this->makeRequest('search/movie', [
                'query' => $query,
                'page' => $page,
                'language' => 'en-US'
            ]);
        } catch (Exception $e) {
            error_log("TMDB Movie Search Error: " . $e->getMessage());
            return [];
        }
    }

    public function bulkImportMovies($year, $page = 1) {
        try {
            return $this->makeRequest('discover/movie', [
                'primary_release_year' => $year,
                'page' => $page,
                'sort_by' => 'popularity.desc',
                'language' => 'en-US'
            ]);
        } catch (Exception $e) {
            error_log("TMDB Bulk Import Error: " . $e->getMessage());
            return [];
        }
    }

    public function searchTVShow($query, $page = 1) {
        try {
            $response = $this->makeRequest('search/tv', [
                'query' => $query,
                'page' => $page,
                'include_adult' => false,
                'language' => 'en-US'
            ]);

            if (!isset($response['results'])) {
                return [];
            }

            return [
                'results' => array_map(function($show) {
                    return [
                        'id' => $show['id'],
                        'name' => $show['name'],
                        'first_air_date' => $show['first_air_date'] ?? null,
                        'poster_path' => $show['poster_path'],
                        'overview' => $show['overview']
                    ];
                }, $response['results']),
                'total_pages' => $response['total_pages'],
                'total_results' => $response['total_results'],
                'page' => $response['page']
            ];
        } catch (Exception $e) {
            error_log("TMDB TV Search Error: " . $e->getMessage());
            return [];
        }
    }

    public function searchWithYear($query, $year, $type = 'movie', $page = 1) {
        try {
            $endpoint = $type === 'movie' ? 'search/movie' : 'search/tv';
            $response = $this->makeRequest($endpoint, [
                'query' => $query,
                'page' => $page,
                'year' => $year, // For TV shows, this searches primary_release_year
                'primary_release_year' => $year, // For movies
                'first_air_date_year' => $year, // For TV shows
                'include_adult' => false,
                'language' => 'en-US'
            ]);

            if (!isset($response['results'])) {
                return [];
            }

            // Filter results to match the year
            $results = array_filter($response['results'], function($item) use ($year, $type) {
                $date = $type === 'movie' ? 
                    ($item['release_date'] ?? '') : 
                    ($item['first_air_date'] ?? '');
                
                return substr($date, 0, 4) === $year;
            });

            return array_values($results); // Reset array keys
        } catch (Exception $e) {
            error_log("TMDB Search Error: " . $e->getMessage());
            return [];
        }
    }

    /*
    private function makeRequest($url, $params = []) {
        $url .= '?' . http_build_query($params);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("CURL Error: " . $error);
        }
        
        return json_decode($response, true);
    }
    */
}
