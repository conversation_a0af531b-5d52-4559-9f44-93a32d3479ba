<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/tmdb_handler.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: login.php');
    exit;
}

$page_title = "Advanced Content Import";
require_once 'includes/header.php';

$tmdb = new TMDBHandler();

// Get genres for dropdown
$genres = $tmdb->makeRequest('genre/movie/list')['genres'] ?? [];

// Add this after genres array
$networks = [
    213 => 'Netflix',
    1024 => 'Amazon Prime Video',
    2552 => 'Apple TV+',
    2739 => 'Disney+',
    453 => 'Hulu',
    3186 => 'HBO Max',
    64 => 'HBO',
    4330 => 'Peacock',
    2552 => 'Apple TV+',
    2628 => 'Hotstar',
    2883 => 'Viu',
    3599 => 'VIKI',
    414 => 'Voot',
];

// Add these variables at the top after TMDBHandler initialization
$items_per_page = 100; // Changed from 50 to 100
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Handle search
$search_results = [];
if (isset($_GET['search']) || isset($_GET['year']) || isset($_GET['genre'])) {
    try {
        $params = [
            'page' => $current_page,
            'language' => $_GET['language'] ?? 'en-US'
        ];
        
        $type = $_GET['type'] ?? 'movie';
        
        // Modified search logic
        if (!empty($_GET['search'])) {
            // Extract year from search query if present
            $search_query = trim($_GET['search']);
            $year = null;
            
            // Check for year in search query (e.g., "Avatar 2009")
            if (preg_match('/^(.*?)\s*(\d{4})(?:\s*)?$/', $search_query, $matches)) {
                $search_query = trim($matches[1]); // Get the title part
                $year = $matches[2]; // Get the year part
            }
            
            $params['query'] = $search_query;
            $endpoint = "search/" . $type;
            
            // If year was found in search query, override the year parameter
            if ($year) {
                $params[$type === 'movie' ? 'primary_release_year' : 'first_air_date_year'] = $year;
            }
            // If year was separately selected in the form
            elseif (!empty($_GET['year'])) {
                $params[$type === 'movie' ? 'primary_release_year' : 'first_air_date_year'] = $_GET['year'];
            }
            
            // Make multiple requests and combine results
            $combined_results = [];
            $max_requests = 3;
            
            for ($i = 0; $i < $max_requests; $i++) {
                $params['page'] = $current_page + $i;
                $response = $tmdb->makeRequest($endpoint, $params);
                if (isset($response['results'])) {
                    // Filter results by year if specified
                    if ($year || !empty($_GET['year'])) {
                        $target_year = $year ?? $_GET['year'];
                        $response['results'] = array_filter($response['results'], function($item) use ($type, $target_year) {
                            $release_date = $type === 'movie' ? 
                                ($item['release_date'] ?? '') : 
                                ($item['first_air_date'] ?? '');
                            return substr($release_date, 0, 4) === $target_year;
                        });
                    }
                    $combined_results = array_merge($combined_results, $response['results']);
                    $total_pages = $response['total_pages'] ?? 1;
                }
            }
            
            $search_results = array_values($combined_results); // Reset array keys
        } else {
            // If no search query, use discover endpoint
            $endpoint = "discover/" . $type;
            
            if (!empty($_GET['year'])) {
                $params[$type === 'movie' ? 'primary_release_year' : 'first_air_date_year'] = $_GET['year'];
            }
            
            if (!empty($_GET['genre'])) {
                $params['with_genres'] = $_GET['genre'];
            }
            
            if (!empty($_GET['network'])) {
                $params['with_networks'] = $_GET['network'];
            }
            
            $response = $tmdb->makeRequest($endpoint, $params);
            $search_results = $response['results'] ?? [];
            $total_pages = $response['total_pages'] ?? 1;
        }

        // Sort results by relevance and year
        if (!empty($search_results)) {
            usort($search_results, function($a, $b) use ($type) {
                $a_date = $type === 'movie' ? ($a['release_date'] ?? '') : ($a['first_air_date'] ?? '');
                $b_date = $type === 'movie' ? ($b['release_date'] ?? '') : ($b['first_air_date'] ?? '');
                $a_year = substr($a_date, 0, 4);
                $b_year = substr($b_date, 0, 4);
                
                // If years are different, sort by year descending
                if ($a_year !== $b_year) {
                    return $b_year <=> $a_year;
                }
                
                // If years are same, sort by popularity
                return ($b['popularity'] ?? 0) <=> ($a['popularity'] ?? 0);
            });
        }

    } catch (Exception $e) {
        error_log("Search error: " . $e->getMessage());
        $error = "Search failed: " . $e->getMessage();
    }
}

// Check if content exists in database
function isContentImported($db, $tmdb_id, $type = 'movie') {
    $table = ($type == 'movie') ? 'movies' : 'series';
    $stmt = $db->prepare("SELECT id FROM $table WHERE tmdb_id = ?");
    $stmt->execute([$tmdb_id]);
    return $stmt->fetch() ? true : false;
}

// Filter out imported content if hide_imported is checked
if (isset($_GET['hide_imported']) && !empty($search_results)) {
    $filtered_results = [];
    foreach ($search_results as $item) {
        if (!isContentImported($db, $item['id'], $_GET['type'] ?? 'movie')) {
            $filtered_results[] = $item;
        }
    }
    $search_results = $filtered_results;
    $total_results = count($filtered_results);
}

?>

<div class="container mt-4">
    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <input type="text" name="search" class="form-control" placeholder="Search by title..." value="<?= $_GET['search'] ?? '' ?>">
                </div>
                <div class="col-md-2">
                    <select name="type" class="form-control">
                        <option value="movie" <?= ($_GET['type'] ?? 'movie') == 'movie' ? 'selected' : '' ?>>Movies</option>
                        <option value="tv" <?= ($_GET['type'] ?? '') == 'tv' ? 'selected' : '' ?>>TV Shows</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="language" class="form-control">
                        <option value="">All Languages</option>
                        <option value="en" <?= ($_GET['language'] ?? '') == 'en' ? 'selected' : '' ?>>Hollywood (English)</option>
                        <option value="ko" <?= ($_GET['language'] ?? '') == 'ko' ? 'selected' : '' ?>>Korean (한국어)</option>
                        <option value="hi" <?= ($_GET['language'] ?? '') == 'hi' ? 'selected' : '' ?>>Hindi (हिन्दी)</option>
                        <option value="bn" <?= ($_GET['language'] ?? '') == 'bn' ? 'selected' : '' ?>>Bengali (বাংলা)</option>
                        <option value="te" <?= ($_GET['language'] ?? '') == 'te' ? 'selected' : '' ?>>Telugu (తెలుగు)</option>
                        <option value="ta" <?= ($_GET['language'] ?? '') == 'ta' ? 'selected' : '' ?>>Tamil (தமிழ்)</option>
                        <option value="ml" <?= ($_GET['language'] ?? '') == 'ml' ? 'selected' : '' ?>>Malayalam (മലയാളം)</option>
                        <option value="kn" <?= ($_GET['language'] ?? '') == 'kn' ? 'selected' : '' ?>>Kannada (ಕನ್ನಡ)</option>
                        <option value="ja" <?= ($_GET['language'] ?? '') == 'ja' ? 'selected' : '' ?>>Japanese (日本語)</option>
                        <option value="zh" <?= ($_GET['language'] ?? '') == 'zh' ? 'selected' : '' ?>>Chinese (中文)</option>
                        <option value="es" <?= ($_GET['language'] ?? '') == 'es' ? 'selected' : '' ?>>Spanish (Español)</option>
                        <option value="fr" <?= ($_GET['language'] ?? '') == 'fr' ? 'selected' : '' ?>>French (Français)</option>
                        <option value="de" <?= ($_GET['language'] ?? '') == 'de' ? 'selected' : '' ?>>German (Deutsch)</option>
                        <option value="it" <?= ($_GET['language'] ?? '') == 'it' ? 'selected' : '' ?>>Italian (Italiano)</option>
                        <option value="th" <?= ($_GET['language'] ?? '') == 'th' ? 'selected' : '' ?>>Thai (ไทย)</option>
                        <option value="id" <?= ($_GET['language'] ?? '') == 'id' ? 'selected' : '' ?>>Indonesian (Bahasa)</option>
                        <option value="ar" <?= ($_GET['language'] ?? '') == 'ar' ? 'selected' : '' ?>>Arabic (العربية)</option>
                        <option value="tr" <?= ($_GET['language'] ?? '') == 'tr' ? 'selected' : '' ?>>Turkish (Türkçe)</option>
                        <option value="ru" <?= ($_GET['language'] ?? '') == 'ru' ? 'selected' : '' ?>>Russian (Русский)</option>
                        <option value="pa" <?= ($_GET['language'] ?? '') == 'pa' ? 'selected' : '' ?>>Punjabi (ਪੰਜਾਬੀ)</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="year" class="form-control">
                        <option value="">Select Year</option>
                        <?php for($y = date('Y'); $y >= 1900; $y--): ?>
                            <option value="<?= $y ?>" <?= ($_GET['year'] ?? '') == $y ? 'selected' : '' ?>><?= $y ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="genre" class="form-control">
                        <option value="">Select Genre</option>
                        <?php foreach($genres as $genre): ?>
                            <option value="<?= $genre['id'] ?>" <?= ($_GET['genre'] ?? '') == $genre['id'] ? 'selected' : '' ?>><?= $genre['name'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="network" class="form-control">
                        <option value="">Select Platform</option>
                        <?php foreach($networks as $id => $name): ?>
                            <option value="<?= $id ?>" <?= ($_GET['network'] ?? '') == $id ? 'selected' : '' ?>><?= $name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" name="hide_imported" id="hideImported" value="1" <?= isset($_GET['hide_imported']) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="hideImported">Hide Imported</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text">Import Pages</span>
                        <input type="number" name="start_page" class="form-control" placeholder="Start" min="1" max="1000" value="<?= $_GET['start_page'] ?? 1 ?>">
                        <input type="number" name="end_page" class="form-control" placeholder="End" min="1" max="1000" value="<?= $_GET['end_page'] ?? '' ?>">
                    </div>
                    <small class="text-muted">Max <?= $max_pages ?> pages at once (50 items per page)</small>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add status message -->
    <?php if (!empty($search_results)): ?>
        <div class="alert alert-info">
            Found <?= count($search_results) ?> items from pages <?= $start_page ?> to <?= $end_page ?>
            <?php if (isset($_GET['hide_imported'])): ?>
                (showing only non-imported items)
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($search_results)): ?>
        <!-- Auto Import Button -->
        <button id="autoImportBtn" class="btn btn-success mb-3">Auto Import All</button>
        
        <!-- Results Grid -->
        <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-4" id="results-grid">
            <?php foreach ($search_results as $item): 
                $isImported = isContentImported($db, $item['id'], $_GET['type'] ?? 'movie');
                $poster = $item['poster_path'] ? "https://image.tmdb.org/t/p/w342{$item['poster_path']}" : 'path/to/default-poster.jpg';
            ?>
                <div class="col">
                    <div class="card h-100 <?= $isImported ? 'border-success' : '' ?>">
                        <img src="<?= $poster ?>" class="card-img-top" alt="<?= $item['title'] ?? $item['name'] ?>">
                        <div class="card-body">
                            <h6 class="card-title"><?= $item['title'] ?? $item['name'] ?></h6>
                            <?php if ($isImported): ?>
                                <span class="badge bg-success">Imported</span>
                            <?php else: ?>
                                <button class="btn btn-sm btn-primary import-btn" 
                                        data-id="<?= $item['id'] ?>" 
                                        data-type="<?= $_GET['type'] ?? 'movie' ?>">
                                    Import
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if (isset($total_pages) && $total_pages > 1): ?>
            <div class="d-flex justify-content-center mt-4">
                <button id="loadMoreBtn" class="btn btn-primary" 
                        data-current-page="<?= $current_page ?>" 
                        data-total-pages="<?= $total_pages ?>">
                    Load More
                </button>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Single Import
    document.querySelectorAll('.import-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.dataset.id;
            const type = this.dataset.type;
            importContent(id, type, this);
        });
    });

    // Auto Import
    document.getElementById('autoImportBtn')?.addEventListener('click', function() {
        const buttons = document.querySelectorAll('.import-btn:not(.disabled)');
        autoImportSequentially(Array.from(buttons));
    });

    // Load More
    document.getElementById('loadMoreBtn')?.addEventListener('click', loadMoreResults);
});

function importContent(id, type, button) {
    button.disabled = true;
    button.innerHTML = 'Importing...';

    // Debug log
    console.log('Importing:', { id, type });

    fetch('ajax/import_content.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({ 
            id: id,
            type: type
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('Import response:', data);
        if (data.success) {
            button.parentElement.innerHTML = '<span class="badge bg-success">Imported</span>';
        } else {
            button.innerHTML = 'Failed: ' + (data.message || 'Unknown error');
            button.classList.add('btn-danger');
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Import error:', error);
        button.innerHTML = 'Error: ' + error.message;
        button.classList.add('btn-danger');
        button.disabled = false;
    });
}

function autoImportSequentially(buttons) {
    const totalButtons = buttons.length;
    let processed = 0;
    
    if (buttons.length === 0) return;

    // Create progress bar if not exists
    let progressBar = document.getElementById('importProgress');
    if (!progressBar) {
        progressBar = document.createElement('div');
        progressBar.id = 'importProgress';
        progressBar.className = 'progress mt-3';
        progressBar.innerHTML = '<div class="progress-bar" role="progressbar" style="width: 0%"></div>';
        document.querySelector('.container').insertBefore(progressBar, document.getElementById('results-grid'));
    }

    const button = buttons.shift();
    importContent(button.dataset.id, button.dataset.type, button);
    
    processed++;
    const progress = (processed / totalButtons) * 100;
    progressBar.querySelector('.progress-bar').style.width = progress + '%';
    progressBar.querySelector('.progress-bar').textContent = `Importing ${processed}/${totalButtons}`;

    setTimeout(() => {
        autoImportSequentially(buttons);
    }, 2000); // 2 second delay between imports
}

function loadMoreResults() {
    const btn = document.getElementById('loadMoreBtn');
    const currentPage = parseInt(btn.dataset.currentPage);
    const totalPages = parseInt(btn.dataset.totalPages);

    if (currentPage >= totalPages) {
        btn.disabled = true;
        return;
    }

    const nextPage = currentPage + 1;
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('page', nextPage);
    
    fetch(currentUrl)
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newItems = doc.querySelectorAll('#results-grid .col');
            
            const resultsGrid = document.getElementById('results-grid');
            newItems.forEach(item => resultsGrid.appendChild(item));
            
            // Add event listeners to new import buttons
            const newButtons = Array.from(newItems).map(item => 
                item.querySelector('.import-btn')
            ).filter(btn => btn !== null);
            
            newButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const type = this.dataset.type;
                    importContent(id, type, this);
                });
            });
            
            btn.dataset.currentPage = nextPage;
            if (nextPage >= totalPages) {
                btn.disabled = true;
            }
        });
}
</script>

<style>
.card-img-top {
    height: 300px;
    object-fit: cover;
}
.badge {
    font-size: 0.8rem;
}
.form-control {
    background-color: #2b3b52;
    border-color: #374b66;
    color: #fff;
}
.form-control:focus {
    background-color: #2b3b52;
    border-color: #4a669d;
    color: #fff;
}
.form-control option {
    background-color: #2b3b52;
    color: #fff;
}

