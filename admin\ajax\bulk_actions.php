<?php
session_start();
require_once '../../includes/db.php';

// Check for admin authentication
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['action']) || !isset($input['ids']) || !is_array($input['ids'])) {
    http_response_code(400);
    die(json_encode(['success' => false, 'message' => 'Invalid request']));
}

try {
    // Start transaction
    $db->beginTransaction();

    switch ($input['action']) {
        case 'delete':
            if (empty($input['ids'])) {
                throw new Exception('No channels selected');
            }

            $placeholders = str_repeat('?,', count($input['ids']) - 1) . '?';
            $stmt = $db->prepare("DELETE FROM live_channels WHERE id IN ($placeholders)");
            
            if (!$stmt->execute($input['ids'])) {
                throw new Exception('Failed to delete channels');
            }
            break;

        case 'activate':
            $placeholders = str_repeat('?,', count($input['ids']) - 1) . '?';
            $stmt = $db->prepare("UPDATE live_channels SET status = 'active' WHERE id IN ($placeholders)");
            
            if (!$stmt->execute($input['ids'])) {
                throw new Exception('Failed to activate channels');
            }
            break;

        case 'deactivate':
            $placeholders = str_repeat('?,', count($input['ids']) - 1) . '?';
            $stmt = $db->prepare("UPDATE live_channels SET status = 'inactive' WHERE id IN ($placeholders)");
            
            if (!$stmt->execute($input['ids'])) {
                throw new Exception('Failed to deactivate channels');
            }
            break;

        default:
            throw new Exception('Invalid action');
    }

    // Commit transaction
    $db->commit();
    
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    // Rollback transaction
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    // Log the error
    error_log('Bulk action error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
