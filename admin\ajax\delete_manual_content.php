<?php
session_start();
require_once '../../includes/init.php';
require_once '../../includes/db.php';
require_once '../../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

$data = json_decode(file_get_contents('php://input'), true);
$type = $data['type'] ?? '';
$id = $data['id'] ?? 0;

try {
    $db->beginTransaction();

    if ($type === 'movies') {
        // Delete movie sources
        $stmt = $db->prepare("DELETE FROM direct_sources WHERE content_type = 'movie' AND content_id = ?");
        $stmt->execute([$id]);
        
        // Delete movie
        $stmt = $db->prepare("DELETE FROM manual_movies WHERE id = ?");
        $stmt->execute([$id]);
    } else {
        // Delete series sources
        $stmt = $db->prepare("DELETE FROM direct_sources WHERE content_type = 'series' AND content_id = ?");
        $stmt->execute([$id]);
        
        // Delete seasons
        $stmt = $db->prepare("DELETE FROM seasons WHERE series_id = ?");
        $stmt->execute([$id]);
        
        // Delete series
        $stmt = $db->prepare("DELETE FROM manual_series WHERE id = ?");
        $stmt->execute([$id]);
    }

    $db->commit();
    echo json_encode(['success' => true]);
} catch (Exception $e) {
    $db->rollBack();
    error_log($e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}