<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get date range
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Fetch statistics
$stats = [
    'total_users' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'active_users' => $db->query("SELECT COUNT(*) FROM users WHERE status = 'active'")->fetchColumn(),
    'premium_users' => $db->query("SELECT COUNT(*) FROM user_subscriptions WHERE end_date > NOW()")->fetchColumn(),
    'total_movies' => $db->query("SELECT COUNT(*) FROM movies")->fetchColumn(),
    'total_series' => $db->query("SELECT COUNT(*) FROM series")->fetchColumn(),
    'total_episodes' => $db->query("SELECT COUNT(*) FROM episodes")->fetchColumn(),
];

// Get recent registrations
$recent_users = $db->query("
    SELECT id, username, email, created_at, status 
    FROM users 
    ORDER BY created_at DESC 
    LIMIT 10
")->fetchAll(PDO::FETCH_ASSOC);

// Get subscription stats
$stmt = $db->prepare("
    SELECT 
        DATE(p.created_at) as date,
        COUNT(*) as count,
        SUM(p.amount) as revenue
    FROM payments p
    WHERE p.status = 'completed' 
    AND p.created_at BETWEEN ? AND ?
    GROUP BY DATE(p.created_at)
    ORDER BY date DESC
");
$stmt->execute([$start_date, $end_date]);
$subscription_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Reports & Analytics";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Reports & Analytics</h2>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Start Date</label>
                            <input type="date" name="start_date" class="form-control" value="<?= $start_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">End Date</label>
                            <input type="date" name="end_date" class="form-control" value="<?= $end_date ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">Filter</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h6>Total Users</h6>
                            <h3><?= number_format($stats['total_users']) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h6>Active Users</h6>
                            <h3><?= number_format($stats['active_users']) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h6>Premium Users</h6>
                            <h3><?= number_format($stats['premium_users']) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <h6>Total Movies</h6>
                            <h3><?= number_format($stats['total_movies']) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <h6>Total Series</h6>
                            <h3><?= number_format($stats['total_series']) ?></h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <h6>Total Episodes</h6>
                            <h3><?= number_format($stats['total_episodes']) ?></h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Subscription Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>New Subscriptions</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subscription_stats as $stat): ?>
                                <tr>
                                    <td><?= date('M d, Y', strtotime($stat['date'])) ?></td>
                                    <td><?= number_format($stat['count']) ?></td>
                                    <td>৳<?= number_format($stat['revenue'], 2) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Users -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent User Registrations</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Registration Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_users as $user): ?>
                                <tr>
                                    <td><?= htmlspecialchars($user['username']) ?></td>
                                    <td><?= htmlspecialchars($user['email']) ?></td>
                                    <td><?= date('M d, Y H:i', strtotime($user['created_at'])) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'danger' ?>">
                                            <?= ucfirst($user['status']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Add any JavaScript for interactive charts here if needed
</script>

<?php include 'includes/footer.php'; ?>
