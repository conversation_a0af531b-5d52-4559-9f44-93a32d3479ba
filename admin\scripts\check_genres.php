<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/auth.php';

// Force admin login
$auth = new Auth($db);
$auth->adminLogin('<EMAIL>', 'password123');

// Initialize report arrays
$missing_genres = [];
$movies_without_genres = [];
$total_movies = 0;
$processed_movies = 0;

echo "Starting genre check...\n";

// Get all movies
$movie_query = "SELECT id, title, tmdb_id FROM movies WHERE status = 'active'";
$movies = $db->query($movie_query)->fetchAll(PDO::FETCH_ASSOC);
$total_movies = count($movies);

// Check each movie
foreach ($movies as $movie) {
    $processed_movies++;
    
    // Get genres for this movie
    $genre_query = "SELECT COUNT(*) as genre_count FROM movie_genres WHERE movie_id = ?";
    $stmt = $db->prepare($genre_query);
    $stmt->execute([$movie['id']]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['genre_count'] == 0) {
        // Store movies without any genres
        $movies_without_genres[] = [
            'id' => $movie['id'],
            'title' => $movie['title'],
            'tmdb_id' => $movie['tmdb_id']
        ];
        
        // If TMDB ID exists, we can try to fetch genres
        if ($movie['tmdb_id']) {
            require_once '../../includes/tmdb_handler.php';
            $tmdb = new TMDBHandler();
            
            try {
                $movie_data = $tmdb->makeRequest("movie/" . $movie['tmdb_id']);
                
                if (isset($movie_data['genres']) && !empty($movie_data['genres'])) {
                    echo "Found genres for '{$movie['title']}' from TMDB. Adding...\n";
                    
                    // Begin transaction
                    $db->beginTransaction();
                    
                    try {
                        // Insert each genre
                        foreach ($movie_data['genres'] as $genre) {
                            // Check if genre exists in our database
                            $check_genre = $db->prepare("SELECT id FROM genres WHERE tmdb_id = ?");
                            $check_genre->execute([$genre['id']]);
                            $local_genre = $check_genre->fetch(PDO::FETCH_ASSOC);
                            
                            if ($local_genre) {
                                // Add genre to movie
                                $insert = $db->prepare("INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (?, ?)");
                                $insert->execute([$movie['id'], $local_genre['id']]);
                            }
                        }
                        
                        $db->commit();
                        echo "Successfully added genres for '{$movie['title']}'\n";
                        
                    } catch (Exception $e) {
                        $db->rollBack();
                        echo "Error adding genres for '{$movie['title']}': " . $e->getMessage() . "\n";
                    }
                }
            } catch (Exception $e) {
                echo "Error fetching TMDB data for '{$movie['title']}': " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Show progress
    if ($processed_movies % 10 == 0) {
        echo "Processed $processed_movies of $total_movies movies...\n";
    }
}

// Generate report
echo "\n=== Genre Check Report ===\n";
echo "Total movies checked: $total_movies\n";
echo "Movies without genres: " . count($movies_without_genres) . "\n\n";

if (!empty($movies_without_genres)) {
    echo "Movies missing genres:\n";
    echo "ID\tTitle\tTMDB ID\n";
    echo str_repeat("-", 50) . "\n";
    
    foreach ($movies_without_genres as $movie) {
        echo "{$movie['id']}\t{$movie['title']}\t{$movie['tmdb_id']}\n";
    }
}

// Save report to file
$report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'total_movies' => $total_movies,
    'movies_without_genres' => $movies_without_genres
];

$report_file = __DIR__ . '/genre_check_report_' . date('Y-m-d_H-i-s') . '.json';
file_put_contents($report_file, json_encode($report, JSON_PRETTY_PRINT));

echo "\nReport saved to: $report_file\n";

// Optional: Add automatic fix suggestion
if (!empty($movies_without_genres)) {
    echo "\nTo automatically fetch missing genres from TMDB, run:\n";
    echo "php fetch_missing_genres.php\n";
}
