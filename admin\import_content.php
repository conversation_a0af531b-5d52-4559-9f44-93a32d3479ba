<?php
session_start();
ini_set('max_execution_time', 0); // Remove time limit
ini_set('memory_limit', '256M'); // Increase memory limit
set_time_limit(0); // Remove time limit (alternative method)

require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';

// Change the session check to match admin login
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$tmdb = new TMDBHandler();

// Read JSON files
$movies_json = file_get_contents('../data/movie.json');
$series_json = file_get_contents('../data/series.json');

$movies = json_decode($movies_json, true) ?? [];
$series = json_decode($series_json, true) ?? [];

function importMovie($tmdb, $db, $movie_data) {
    try {
        // Get detailed movie info from TMDB
        $tmdb_details = $tmdb->getMovieById($movie_data['tmdb']);
        
        if (!$tmdb_details) {
            return "Failed to fetch TMDB data for: " . $movie_data['title'];
        }

        // Check if movie already exists
        $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
        $stmt->execute([$movie_data['tmdb']]);
        if ($stmt->fetch()) {
            return "Movie already exists: " . $movie_data['title'];
        }

        // Generate slug
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $tmdb_details['title'])));
        
        // Insert movie
        $sql = "INSERT INTO movies (
            tmdb_id, 
            imdb_id,
            title, 
            original_title, 
            slug, 
            overview, 
            poster_path, 
            backdrop_path, 
            release_date,
            runtime,
            rating,
            status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $tmdb_details['id'],
            $movie_data['imdb'],
            $tmdb_details['title'],
            $tmdb_details['original_title'] ?? $tmdb_details['title'],
            $slug,
            $tmdb_details['overview'],
            $tmdb_details['poster_path'],
            $tmdb_details['backdrop_path'],
            $tmdb_details['release_date'],
            $tmdb_details['runtime'] ?? 0,
            $tmdb_details['vote_average'] ?? 0
        ]);

        $movie_id = $db->lastInsertId();

        // Add default servers
        $server_sql = "INSERT INTO movie_servers (movie_id, server_id, tmdb_id) VALUES (?, ?, ?)";
        $server_stmt = $db->prepare($server_sql);
        // Add all available servers from the servers table
        $servers = $db->query("SELECT id FROM servers WHERE status = 'active'")->fetchAll(PDO::FETCH_COLUMN);
        foreach ($servers as $server_id) {
            $server_stmt->execute([$movie_id, $server_id, $tmdb_details['id']]);
        }

        return "Successfully imported: " . $movie_data['title'];
    } catch (Exception $e) {
        return "Error importing " . $movie_data['title'] . ": " . $e->getMessage();
    }
}

function importSeries($tmdb, $db, $series_data) {
    try {
        // Get detailed series info from TMDB
        $tmdb_details = $tmdb->getTVShowById($series_data['tmdb']);
        
        if (!$tmdb_details) {
            return "Failed to fetch TMDB data for: " . $series_data['title'];
        }

        // Check if series already exists
        $stmt = $db->prepare("SELECT id FROM series WHERE tmdb_id = ?");
        $stmt->execute([$series_data['tmdb']]);
        if ($stmt->fetch()) {
            return "Series already exists: " . $series_data['title'];
        }

        // Generate slug
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $tmdb_details['name'])));
        
        // Insert series
        $sql = "INSERT INTO series (
            tmdb_id,
            imdb_id, 
            title, 
            original_title, 
            slug, 
            overview, 
            poster_path, 
            backdrop_path, 
            first_air_date,
            last_air_date,
            status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $tmdb_details['id'],
            $series_data['imdb'],
            $tmdb_details['name'],
            $tmdb_details['original_name'] ?? $tmdb_details['name'],
            $slug,
            $tmdb_details['overview'],
            $tmdb_details['poster_path'],
            $tmdb_details['backdrop_path'],
            $tmdb_details['first_air_date'],
            $tmdb_details['last_air_date']
        ]);

        $series_id = $db->lastInsertId();

        // Import seasons and episodes
        if (isset($tmdb_details['seasons'])) {
            $season_sql = "INSERT INTO seasons (
                series_id, 
                tmdb_id, 
                season_number, 
                name, 
                overview, 
                poster_path, 
                air_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $season_stmt = $db->prepare($season_sql);
            
            // Prepare episode insert statement
            $episode_sql = "INSERT INTO episodes (
                series_id,
                season_id,
                tmdb_id,
                episode_number,
                name,
                overview,
                still_path,
                air_date,
                status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')";
            
            $episode_stmt = $db->prepare($episode_sql);
            
            foreach ($tmdb_details['seasons'] as $season) {
                // Skip season 0 (usually specials)
                if ($season['season_number'] == 0) continue;
                
                // Insert season
                $season_stmt->execute([
                    $series_id,
                    $season['id'],
                    $season['season_number'],
                    $season['name'],
                    $season['overview'],
                    $season['poster_path'],
                    $season['air_date']
                ]);
                
                $season_id = $db->lastInsertId();
                
                // Get and insert episodes for this season
                $season_details = $tmdb->getTVSeasonById($tmdb_details['id'], $season['season_number']);
                
                if ($season_details && isset($season_details['episodes'])) {
                    foreach ($season_details['episodes'] as $episode) {
                        $episode_stmt->execute([
                            $series_id,
                            $season_id,
                            $episode['id'],
                            $episode['episode_number'],
                            $episode['name'],
                            $episode['overview'],
                            $episode['still_path'],
                            $episode['air_date']
                        ]);
                        
                        $episode_id = $db->lastInsertId();
                        
                        // Add default servers for episode
                        $server_sql = "INSERT INTO episode_servers (episode_id, server_id, tmdb_id) 
                                     SELECT ?, id, ? FROM servers WHERE status = 'active'";
                        $db->prepare($server_sql)->execute([$episode_id, $episode['id']]);
                    }
                }
            }
        }

        return "Successfully imported: " . $series_data['title'] . " with all seasons and episodes";
    } catch (Exception $e) {
        return "Error importing " . $series_data['title'] . ": " . $e->getMessage();
    }
}

// Handle import request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $results = [];
    $import_type = $_POST['import_type'] ?? '';
    $start = (int)($_POST['start'] ?? 0);
    $limit = (int)($_POST['limit'] ?? 10);
    
    if ($import_type === 'movies') {
        $items = array_slice($movies, $start, $limit);
        foreach ($items as $movie) {
            $results[] = importMovie($tmdb, $db, $movie);
        }
    } elseif ($import_type === 'series') {
        $items = array_slice($series, $start, $limit);
        foreach ($items as $series_item) {
            $results[] = importSeries($tmdb, $db, $series_item);
        }
    }
    
    $_SESSION['import_results'] = $results;
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Import Content</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Import Content</h2>
        
        <?php if (isset($_SESSION['import_results'])): ?>
            <div class="alert alert-info">
                <h4>Import Results:</h4>
                <ul>
                    <?php 
                    foreach ($_SESSION['import_results'] as $result) {
                        echo "<li>" . htmlspecialchars($result) . "</li>";
                    }
                    unset($_SESSION['import_results']);
                    ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="card mb-4">
            <div class="card-body">
                <h5>Available Content:</h5>
                <p>Movies: <?php echo count($movies); ?></p>
                <p>Series: <?php echo count($series); ?></p>
            </div>
        </div>

        <form method="post" class="mt-4">
            <div class="mb-3">
                <label class="form-label">Import Type</label>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="import_type" id="type_movies" value="movies" checked>
                    <label class="form-check-label" for="type_movies">Movies</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="import_type" id="type_series" value="series">
                    <label class="form-check-label" for="type_series">Series</label>
                </div>
            </div>

            <div class="mb-3">
                <label for="start" class="form-label">Start From Index</label>
                <input type="number" class="form-control" id="start" name="start" value="0" min="0">
            </div>

            <div class="mb-3">
                <label for="limit" class="form-label">Number of Items to Import</label>
                <input type="number" class="form-control" id="limit" name="limit" value="10" min="1" max="50">
            </div>

            <button type="submit" class="btn btn-primary">Import</button>
        </form>
    </div>
</body>
</html>
