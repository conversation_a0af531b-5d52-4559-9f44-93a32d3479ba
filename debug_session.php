<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to catch any errors
ob_start();

// First include all required files and do authentication
require_once 'includes/init.php';

try {
    // Verify if Auth class exists and is initialized
    if (!isset($auth) || !is_object($auth)) {
        throw new Exception('Auth object not properly initialized');
    }

    // Check if user is logged in
    if (!$auth->isLoggedIn()) {
        throw new Exception('User not logged in');
    }

    // Get any PHP errors that occurred during initialization
    $errors = ob_get_clean();
    ob_start();

    // Get debug info with error checking
    $debug = $auth->getDebugInfo();
    if (!is_array($debug)) {
        throw new Exception('Debug info not returned properly');
    }

    // Calculate remaining session time - Using 30 days timeout
    $session_timeout = 30 * 24 * 60 * 60; // 30 days in seconds
    $last_activity = isset($_SESSION['last_activity']) ? $_SESSION['last_activity'] : 0;
    $time_elapsed = time() - $last_activity;
    $time_remaining = $session_timeout - $time_elapsed;

    // Format for display with error checking
    $hours = floor($time_remaining / 3600);
    $minutes = floor(($time_remaining % 3600) / 60);
    $seconds = $time_remaining % 60;
    
    $debug['time_remaining'] = sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);
    $debug['last_activity_formatted'] = date('Y-m-d H:i:s', $last_activity);

    // Verify session data
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username']) || !isset($_SESSION['role'])) {
        throw new Exception('Critical session data missing');
    }

    // HTML output
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Session Debug Info</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .debug-container { max-width: 800px; margin: 0 auto; }
            .debug-section { 
                background: #f5f5f5; 
                padding: 15px; 
                margin-bottom: 15px; 
                border-radius: 5px;
            }
            .success { color: green; }
            .error { color: red; }
            .warning { color: orange; }
            .error-log {
                background: #ffe6e6;
                padding: 10px;
                border-left: 4px solid red;
                margin: 10px 0;
            }
            .debug-log {
                background: #e6f3ff;
                padding: 10px;
                border-left: 4px solid blue;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="debug-container">
            <?php
            // Display any PHP errors or warnings
            if (!empty($errors)) {
                $hasPhpErrors = strpos($errors, 'PHP') !== false || 
                                strpos($errors, 'Warning') !== false || 
                                strpos($errors, 'Error') !== false || 
                                strpos($errors, 'Notice') !== false;
                
                if ($hasPhpErrors) {
                    echo '<div class="error-log"><h3>PHP Errors/Warnings:</h3><pre>' . htmlspecialchars($errors) . '</pre></div>';
                }
            }
            ?>

            <div class="debug-log">
                <h3>Debug Information:</h3>
                <pre><?php
                    echo "PHP Version: " . PHP_VERSION . "\n";
                    echo "Session Status: " . session_status() . "\n";
                    echo "Session ID: " . session_id() . "\n";
                    echo "Session Save Path: " . session_save_path() . "\n";
                ?></pre>
            </div>

            <h1>Session Debug Information</h1>
            
            <div class="debug-section">
                <h2>User Information</h2>
                <p><strong>Login Status:</strong> <span class="success">Logged In</span></p>
                <p><strong>User ID:</strong> <?php echo htmlspecialchars($_SESSION['user_id'] ?? 'Not set'); ?></p>
                <p><strong>Username:</strong> <?php echo htmlspecialchars($_SESSION['username'] ?? 'Not set'); ?></p>
                <p><strong>Role:</strong> <?php echo htmlspecialchars($_SESSION['role'] ?? 'Not set'); ?></p>
            </div>

            <div class="debug-section">
                <h2>Session Security</h2>
                <p><strong>Fingerprint Match:</strong> 
                    <?php if(isset($debug['current_fingerprint']) && isset($debug['session_fingerprint'])): ?>
                        <?php if($debug['current_fingerprint'] === $debug['session_fingerprint']): ?>
                            <span class="success">✓ Valid</span>
                        <?php else: ?>
                            <span class="error">✗ Invalid</span>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="error">Fingerprint data missing</span>
                    <?php endif; ?>
                </p>
                <p><strong>Current Fingerprint:</strong> <?php echo htmlspecialchars($debug['current_fingerprint'] ?? 'Not set'); ?></p>
                <p><strong>Session Fingerprint:</strong> <?php echo htmlspecialchars($debug['session_fingerprint'] ?? 'Not set'); ?></p>
            </div>

            <div class="debug-section">
                <h2>Session Timing</h2>
                <p><strong>Last Activity:</strong> <?php echo $debug['last_activity_formatted']; ?></p>
                <p><strong>Session Expires In:</strong> <?php echo $debug['time_remaining']; ?> (mm:ss)</p>
            </div>

            <div class="debug-section">
                <h2>Session Variables</h2>
                <pre><?php print_r($_SESSION); ?></pre>
            </div>

            <div class="debug-section">
                <h2>Server Information</h2>
                <pre><?php print_r([
                    'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'] ?? 'Not set',
                    'REQUEST_TIME' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME'] ?? time()),
                    'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? 'Not set',
                    'SCRIPT_FILENAME' => $_SERVER['SCRIPT_FILENAME'] ?? 'Not set',
                ]); ?></pre>
            </div>
        </div>

        <script>
        // Auto refresh the page every 30 seconds
        setInterval(() => {
            window.location.reload();
        }, 30000);
        </script>
    </body>
    </html>
    <?php
} catch (Exception $e) {
    // Display any caught exceptions
    echo '<div style="color: red; padding: 20px; border: 1px solid red; margin: 20px;">';
    echo '<h2>Error Occurred:</h2>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<h3>Stack Trace:</h3>';
    echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    echo '</div>';
}

// End output buffering and flush
ob_end_flush();
