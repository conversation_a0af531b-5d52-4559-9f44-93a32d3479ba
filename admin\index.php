<?php
require_once '../includes/db.php';
require_once 'includes/auth.php';

// Initialize admin auth
$adminAuth = new AdminAuth($db);
$adminAuth->checkAdminLogin();

// Get admin info
$stmt = $db->prepare("SELECT username, last_login FROM admins WHERE id = ?");
$stmt->execute([$_SESSION['admin_id']]);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

// Get dashboard stats
$stats = [
    'total_users' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'total_movies' => $db->query("SELECT COUNT(*) FROM movies")->fetchColumn(),
    'total_series' => $db->query("SELECT COUNT(*) FROM series")->fetchColumn(),
    'active_subscriptions' => $db->query("SELECT COUNT(*) FROM subscriptions WHERE status = 'active'")->fetchColumn()
];

// Set page title
$page_title = "Admin Dashboard";
include 'includes/header.php';
?>

<!-- Add custom CSS -->
<style>
body {
    background-color: #141414;
    color: #ffffff;
    padding-top: 50px;
    padding-left: 0; /* Remove left padding for full width */
}

.admin-dashboard {
    padding: 20px;
    margin-top: 0;
    min-height: calc(100vh - 50px);
    width: 100%;
}

.container-fluid {
    padding: 20px;
    margin: 0;
    max-width: 100%;
}

/* Card Styles */
.card {
    background: rgba(32, 32, 32, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.card-header {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    font-weight: bold;
    font-size: 1.1em;
    color: #ffffff !important;  /* Card header text color */
}

/* Stats Cards */
.stats-card {
    text-align: center;
    padding: 20px;
}

.stats-card i {
    font-size: 2.5em;
    margin-bottom: 15px;
    color: #007bff;
}

.stats-card h3 {
    font-size: 2em;
    margin: 10px 0;
    font-weight: bold;
}

.stats-card p {
    color: #888;
    margin: 0;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.action-btn {
    padding: 15px 25px;
    border-radius: 8px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.action-btn i {
    margin-right: 10px;
    font-size: 1.2em;
}

/* Latest Content Table */
.table {
    color: #fff !important;  /* Force table text color */
    background: rgba(32, 32, 32, 0.95);
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background: rgba(0, 0, 0, 0.2);
    border: none;
    padding: 15px;
    color: #ffffff !important;  /* Table header text color */
}

.table td {
    border-color: rgba(255, 255, 255, 0.1);
    padding: 12px 15px;
    vertical-align: middle;
    color:rgb(19, 17, 17) !important;  /* Table cell text color */
}

/* Status Badges */
.badge {
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

/* Chart Container */
.chart-container {
    background: rgba(32, 32, 32, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

/* Add these color overrides to your existing styles */
:root {
    --bs-heading-color: #ffffff !important;
    --bs-table-color: #ffffff !important;  /* Table text color */
}

/* Card header text color */
.card-header {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    font-weight: bold;
    font-size: 1.1em;
    color: #ffffff !important;  /* Card header text color */
}

/* Table styles */
.table {
    color: #fff !important;  /* Force table text color */
}

.table th {
    background: rgba(0, 0, 0, 0.2);
    border: none;
    padding: 15px;
    color: #ffffff !important;  /* Table header text color */
}

.table td {
    border-color: rgba(255, 255, 255, 0.1);
    padding: 12px 15px;
    vertical-align: middle;
    color:rgb(27, 25, 25) !important;  /* Table cell text color */
}

/* Additional text color overrides */
.card-title, 
.page-title,
h1, h2, h3, h4, h5, h6 {
    color: #ffffff !important;
}

.text-body {
    color: #e5e5e5 !important;
}

.text-muted {
    color: #aaaaaa !important;
}

</style>

<!-- Dashboard Content -->
<div class="admin-dashboard">
    <div class="container-fluid">
        <!-- Stats Row -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card stats-card">
                    <i class="fas fa-film"></i>
                    <h3><?php echo number_format($stats['movies']); ?></h3>
                    <p>Total Movies</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <i class="fas fa-tv"></i>
                    <h3><?php echo number_format($stats['series']); ?></h3>
                    <p>Total Series</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <i class="fas fa-users"></i>
                    <h3><?php echo number_format($stats['users']); ?></h3>
                    <p>Active Users</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <i class="fas fa-credit-card"></i>
                    <h3><?php echo number_format($stats['active_subscriptions']); ?></h3>
                    <p>Active Subscriptions</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <i class="fas fa-clock"></i>
                    <h3><?php echo number_format($stats['pending_payments']); ?></h3>
                    <p>Pending Payments</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <i class="fas fa-dollar-sign"></i>
                    <h3>$<?php echo number_format($stats['monthly_revenue'], 2); ?></h3>
                    <p>Monthly Revenue</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Existing quick actions -->
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <a href="add_movie.php" class="btn btn-primary">
                                        <i class="fas fa-plus-circle"></i> Add Movie
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <a href="add_series.php" class="btn btn-info">
                                        <i class="fas fa-plus-circle"></i> Add Series
                                    </a>
                                </div>
                            </div>
                            
                            <!-- New Import Section -->
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-3">
                                            <i class="fas fa-file-import"></i> Content Import Status
                                        </h6>
                                        
                                        <?php
                                        // Get import statistics
                                        $movie_count = $db->query("SELECT COUNT(*) FROM movies")->fetchColumn();
                                        $series_count = $db->query("SELECT COUNT(*) FROM series")->fetchColumn();
                                        
                                        // Get last import time
                                        $last_import = $db->query("
                                            SELECT created_at 
                                            FROM (
                                                SELECT created_at FROM movies
                                                UNION ALL
                                                SELECT created_at FROM series
                                            ) as imports 
                                            ORDER BY created_at DESC 
                                            LIMIT 1
                                        ")->fetchColumn();
                                        
                                        $last_import_time = $last_import ? date('d M Y H:i', strtotime($last_import)) : 'Never';
                                        ?>
                                        
                                        <div class="row g-2 mb-3">
                                            <div class="col-6">
                                                <div class="border rounded p-2 text-center bg-white">
                                                    <div class="small text-muted">Movies</div>
                                                    <div class="h5 mb-0"><?= number_format($movie_count) ?></div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="border rounded p-2 text-center bg-white">
                                                    <div class="small text-muted">Series</div>
                                                    <div class="h5 mb-0"><?= number_format($series_count) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex align-items-center justify-content-between mb-3">
                                            <small class="text-muted">
                                                Last Import: <?= $last_import_time ?>
                                            </small>
                                            <button type="button" 
                                                    class="btn btn-sm btn-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#importModal">
                                                <i class="fas fa-file-import"></i> Import Now
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="movies.php" class="btn btn-primary action-btn">
                                <i class="fas fa-film"></i>Add Movie
                            </a>
                            <a href="series.php" class="btn btn-success action-btn">
                                <i class="fas fa-tv"></i>Add Series
                            </a>
                            <a href="bulk_import_with_servers.php" class="btn btn-info action-btn text-white">
                                <i class="fas fa-cloud-download-alt"></i>Bulk Import
                            </a>
                            <a href="live-channels.php" class="btn btn-danger action-btn">
                                <i class="fas fa-broadcast-tower"></i>Live TV
                            </a>
                            <a href="subscriptions.php" class="btn btn-warning action-btn text-white">
                                <i class="fas fa-crown"></i>Subscriptions
                            </a>
                            <a href="payments.php" class="btn btn-secondary action-btn">
                                <i class="fas fa-money-bill-wave"></i>Payments
                            </a>
                            <a href="users.php" class="btn btn-primary action-btn">
                                <i class="fas fa-users"></i>Users
                            </a>
                            <a href="servers.php" class="btn btn-dark action-btn">
                                <i class="fas fa-server"></i>Servers
                            </a>
                            <a href="categories.php" class="btn btn-success action-btn">
                                <i class="fas fa-tags"></i>Categories
                            </a>
                            <a href="reports.php" class="btn btn-danger action-btn">
                                <i class="fas fa-chart-bar"></i>Reports
                            </a>
                            <a href="settings.php" class="btn btn-warning action-btn text-white">
                                <i class="fas fa-cog"></i>Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Content -->
        <div class="row">
            <!-- Latest Movies -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <i class="fas fa-film me-2"></i>Latest Movies
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Added Date</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($latest_movies as $movie): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($movie['title']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($movie['created_at'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $movie['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($movie['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="edit_movie.php?id=<?php echo $movie['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Latest Users -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header">
                        <i class="fas fa-users me-2"></i>Latest Users
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Join Date</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($latest_users as $user): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($user['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="edit_user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Charts -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line me-2"></i>User Growth
                    </div>
                    <div class="card-body">
                        <canvas id="userGrowthChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar me-2"></i>Revenue Overview
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sample data for charts - Replace with real data
const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
const revenueCtx = document.getElementById('revenueChart').getContext('2d');

// User Growth Chart
new Chart(userGrowthCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'New Users',
            data: [65, 59, 80, 81, 56, 55],
            borderColor: '#007bff',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: {
                    color: '#fff'
                }
            }
        },
        scales: {
            y: {
                ticks: {
                    color: '#fff'
                }
            },
            x: {
                ticks: {
                    color: '#fff'
                }
            }
        }
    }
});

// Revenue Chart
new Chart(revenueCtx, {
    type: 'bar',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Revenue ($)',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            backgroundColor: '#28a745'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: {
                    color: '#fff'
                }
            }
        },
        scales: {
            y: {
                ticks: {
                    color: '#fff'
                }
            },
            x: {
                ticks: {
                    color: '#fff'
                }
            }
        }
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quick Import</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-grid gap-2">
                    <a href="auto_import_from_json.php?type=movies" class="btn btn-lg btn-outline-primary">
                        <i class="fas fa-film me-2"></i> Import Movies
                    </a>
                    <a href="auto_import_from_json.php?type=series" class="btn btn-lg btn-outline-info">
                        <i class="fas fa-tv me-2"></i> Import Series
                    </a>
                    <a href="auto_import_from_json.php" class="btn btn-lg btn-outline-dark">
                        <i class="fas fa-cogs me-2"></i> Advanced Import
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

