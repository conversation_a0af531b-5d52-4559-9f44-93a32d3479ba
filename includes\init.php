<?php
if (session_status() === PHP_SESSION_NONE) {
    // Increase session lifetime to 30 days
    ini_set('session.cookie_lifetime', 30 * 24 * 60 * 60); // 30 days
    ini_set('session.gc_maxlifetime', 30 * 24 * 60 * 60); // 30 days
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_httponly', 1);
    
    // Remove secure flag if not using HTTPS
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    
    session_start();
}

require_once __DIR__ . '/db.php';
require_once __DIR__ . '/auth.php';

$auth = new Auth($db);

// Check remember token if user is not logged in
if (!isset($_SESSION['user_id'])) {
    $auth->checkRememberToken();
}

// Add session refresh logic
if (isset($_SESSION['user_id'])) {
    $session_lifetime = 30 * 24 * 60 * 60; // 30 days in seconds
    $current_time = time();
    
    if (!isset($_SESSION['last_activity']) || 
        ($current_time - $_SESSION['last_activity']) > 60 * 60) { // Refresh every hour
        session_regenerate_id(true);
        $_SESSION['last_activity'] = $current_time;
    }
}
