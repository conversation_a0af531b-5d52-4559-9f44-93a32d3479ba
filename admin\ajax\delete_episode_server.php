<?php
session_start();
require_once '../../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

$data = json_decode(file_get_contents('php://input'), true);
$server_id = isset($data['server_id']) ? (int)$data['server_id'] : 0;

if (!$server_id) {
    die(json_encode(['success' => false, 'message' => 'Invalid server ID']));
}

try {
    $stmt = $db->prepare("DELETE FROM episode_servers WHERE id = ?");
    $success = $stmt->execute([$server_id]);
    
    echo json_encode(['success' => $success]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}