<?php
class ServerManager {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function addServerPattern($name, $movie_pattern, $series_pattern) {
        try {
            // Check if server name already exists
            $check = $this->db->prepare("SELECT id FROM server_patterns WHERE name = ?");
            $check->execute([$name]);
            
            if ($check->rowCount() > 0) {
                return [
                    'success' => false,
                    'message' => "A server with this name already exists!"
                ];
            }

            $stmt = $this->db->prepare("
                INSERT INTO server_patterns (name, movie_pattern, series_pattern) 
                VALUES (?, ?, ?)
            ");
            
            $success = $stmt->execute([$name, $movie_pattern, $series_pattern]);
            
            return [
                'success' => $success,
                'message' => $success ? "Server added successfully" : "Failed to add server"
            ];
        } catch (PDOException $e) {
            error_log("Error in addServerPattern: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "Database error occurred"
            ];
        }
    }

    public function deleteMultiplePatterns($pattern_ids) {
        try {
            $ids = array_map('intval', $pattern_ids);
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            
            $stmt = $this->db->prepare("
                DELETE FROM server_patterns 
                WHERE id IN ($placeholders)
            ");
            
            $success = $stmt->execute($ids);
            
            return [
                'success' => $success,
                'message' => $success ? "Patterns deleted successfully" : "Failed to delete patterns"
            ];
        } catch (PDOException $e) {
            error_log("Error in deleteMultiplePatterns: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "Database error occurred"
            ];
        }
    }

    public function getAllPatterns() {
        try {
            return $this->db->query("
                SELECT * FROM server_patterns 
                ORDER BY id DESC
            ")->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in getAllPatterns: " . $e->getMessage());
            return [];
        }
    }

    public function deletePattern($pattern_id) {
        $sql = "DELETE FROM server_patterns WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$pattern_id]);
    }

    public function addServer($name, $base_url, $pattern_id, $type = 'movie', $priority = 1) {
        try {
            $sql = "INSERT INTO servers (name, base_url, pattern_id, type, priority, status) 
                    VALUES (?, ?, ?, ?, ?, 'active')";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$name, $base_url, $pattern_id, $type, $priority]);
        } catch (PDOException $e) {
            error_log("Error in addServer: " . $e->getMessage());
            return false;
        }
    }

    public function generateMovieUrl($server, $tmdb_id) {
        try {
            // Get pattern
            $stmt = $this->db->prepare("
                SELECT sp.movie_pattern 
                FROM server_patterns sp 
                WHERE sp.id = ?
            ");
            $stmt->execute([$server['pattern_id']]);
            $pattern = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pattern) {
                error_log("No pattern found for server ID: " . $server['pattern_id']);
                return false;
            }

            // Debug
            error_log("Server base URL: " . $server['base_url']);
            error_log("Movie pattern: " . $pattern['movie_pattern']);
            
            // Build URL
            $url = rtrim($server['base_url'], '/') . '/' . ltrim($pattern['movie_pattern'], '/');
            $url = str_replace('{tmdb_id}', $tmdb_id, $url);
            
            error_log("Generated URL: " . $url);
            return $url;
        } catch (PDOException $e) {
            error_log("Error in generateMovieUrl: " . $e->getMessage());
            return false;
        }
    }

    public function generateTVUrl($server, $tmdb_id, $season, $episode) {
        try {
            // Get pattern
            $stmt = $this->db->prepare("
                SELECT sp.series_pattern 
                FROM server_patterns sp 
                WHERE sp.id = ?
            ");
            $stmt->execute([$server['pattern_id']]);
            $pattern = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$pattern) {
                error_log("No pattern found for server ID: " . $server['pattern_id']);
                return false;
            }

            // Debug log
            error_log("TMDB ID: " . $tmdb_id);
            error_log("Server base URL: " . $server['base_url']);
            error_log("Series pattern: " . $pattern['series_pattern']);
            
            // Build URL with proper tmdb_id replacement
            $url = rtrim($server['base_url'], '/');
            
            // Check if pattern starts with slash
            if (!str_starts_with($pattern['series_pattern'], '/')) {
                $url .= '/';
            }
            
            $url .= $pattern['series_pattern'];
            
            // Replace all placeholders
            $replacements = [
                '{tmdb_id}' => $tmdb_id,
                '{season_number}' => $season,
                '{episode_number}' => $episode
            ];
            
            $url = str_replace(array_keys($replacements), array_values($replacements), $url);
            
            error_log("Generated TV URL: " . $url);
            return $url;
        } catch (PDOException $e) {
            error_log("Error in generateTVUrl: " . $e->getMessage());
            return false;
        }
    }

    public function addMovieServer($movie_id, $server_id, $tmdb_id) {
        try {
            // First check if server already exists for this movie
            $check_sql = "SELECT id FROM movie_servers WHERE movie_id = ? AND server_id = ?";
            $check_stmt = $this->db->prepare($check_sql);
            $check_stmt->execute([$movie_id, $server_id]);
            
            if (!$check_stmt->fetch()) {
                // If not exists, then insert
                $sql = "INSERT INTO movie_servers (movie_id, server_id, embed_id, status) VALUES (?, ?, ?, 'active')";
                $stmt = $this->db->prepare($sql);
                return $stmt->execute([$movie_id, $server_id, $tmdb_id]);
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error adding movie server: " . $e->getMessage());
            return false;
        }
    }

    public function getActiveServers() {
        try {
            $stmt = $this->db->prepare("
                SELECT s.*, sp.movie_pattern, sp.series_pattern 
                FROM servers s
                JOIN server_patterns sp ON s.pattern_id = sp.id
                WHERE s.status = 'active'
                ORDER BY s.priority ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting active servers: " . $e->getMessage());
            return [];
        }
    }

    public function getMovieServers($movie_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    ms.id as movie_server_id,
                    s.id as server_id,
                    s.name as server_name,
                    s.base_url,
                    s.pattern_id,
                    m.tmdb_id,
                    sp.movie_pattern
                FROM movie_servers ms
                JOIN servers s ON ms.server_id = s.id
                JOIN movies m ON ms.movie_id = m.id
                JOIN server_patterns sp ON s.pattern_id = sp.id
                WHERE ms.movie_id = ? 
                AND ms.status = 'active'
                AND s.status = 'active'
                ORDER BY s.priority ASC
            ");
            
            $stmt->execute([$movie_id]);
            $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Generate URLs dynamically
            foreach ($servers as &$server) {
                $url = $server['base_url'] . $server['movie_pattern'];
                $server['url'] = str_replace('{tmdb_id}', $server['tmdb_id'], $url);
            }
            
            return $servers;
        } catch (PDOException $e) {
            error_log("Error in getMovieServers: " . $e->getMessage());
            return [];
        }
    }

    public function getEpisodeServers($episode_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    s.id as server_id,
                    s.name as server_name,
                    s.base_url,
                    s.pattern_id,
                    series.tmdb_id,  -- Changed from e.tmdb_id to series.tmdb_id
                    e.season_number,
                    e.episode_number,
                    sp.series_pattern
                FROM episode_servers es
                JOIN servers s ON es.server_id = s.id
                JOIN episodes e ON es.episode_id = e.id
                JOIN series ON e.series_id = series.id  -- Added JOIN with series table
                JOIN server_patterns sp ON s.pattern_id = sp.id
                WHERE es.episode_id = ? 
                AND es.status = 'active'
                AND s.status = 'active'
                ORDER BY s.priority ASC
            ");
            
            $stmt->execute([$episode_id]);
            $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Generate URLs dynamically
            foreach ($servers as &$server) {
                // Debug log
                error_log("Generating URL for server: " . $server['server_name']);
                error_log("TMDB ID: " . $server['tmdb_id']);
                error_log("Season: " . $server['season_number']);
                error_log("Episode: " . $server['episode_number']);
                
                $server['url'] = $this->generateTVUrl(
                    $server,
                    $server['tmdb_id'],
                    $server['season_number'],
                    $server['episode_number']
                );
                
                error_log("Generated URL: " . $server['url']);
            }
            
            return $servers;
        } catch (PDOException $e) {
            error_log("Error in getEpisodeServers: " . $e->getMessage());
            return [];
        }
    }

    public function updateServerStatus($server_id, $status) {
        $db = $this->db;
        $db->beginTransaction();
        try {
            // Update server status
            $stmt = $db->prepare("UPDATE servers SET status = ? WHERE id = ?");
            $stmt->execute([$status, $server_id]);
            
            if ($status === 'inactive') {
                // Remove this server from all movies and series
                $stmt = $db->prepare("DELETE FROM content_servers WHERE server_id = ?");
                $stmt->execute([$server_id]);
            }
            
            $db->commit();
            return true;
        } catch (Exception $e) {
            $db->rollBack();
            error_log("Server status update error: " . $e->getMessage());
            return false;
        }
    }

    public function saveUserPreference($user_id, $content_type, $content_id, $server_id) {
        try {
            $sql = "INSERT INTO user_preferences (user_id, content_type, content_id, server_id) 
                    VALUES (?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE server_id = ?, last_used = CURRENT_TIMESTAMP";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([$user_id, $content_type, $content_id, $server_id, $server_id]);
        } catch (PDOException $e) {
            error_log("Error saving user preference: " . $e->getMessage());
            return false;
        }
    }

    public function getUserPreferredServer($user_id, $content_type, $content_id) {
        try {
            $sql = "SELECT server_id FROM user_preferences 
                    WHERE user_id = ? AND content_type = ? AND content_id = ?
                    ORDER BY last_used DESC LIMIT 1";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id, $content_type, $content_id]);
            
            if ($result = $stmt->fetch(PDO::FETCH_ASSOC)) {
                return $result['server_id'];
            }
            return null;
        } catch (PDOException $e) {
            error_log("Error getting user preference: " . $e->getMessage());
            return null;
        }
    }
}
