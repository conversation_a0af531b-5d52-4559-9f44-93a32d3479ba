<?php
session_start();
require_once '../../includes/init.php';
require_once '../../includes/db.php';
require_once '../../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    die(json_encode(['error' => 'Unauthorized']));
}

$type = $_GET['type'] ?? '';

try {
    if ($type === 'movies') {
        $stmt = $db->prepare("
            SELECT id, title, release_date, status, created_at 
            FROM manual_movies 
            ORDER BY created_at DESC
        ");
    } else {
        $stmt = $db->prepare("
            SELECT 
                ms.id, 
                ms.title, 
                ms.status, 
                ms.created_at,
                COUNT(s.id) as season_count
            FROM manual_series ms
            LEFT JOIN seasons s ON s.series_id = ms.id
            GROUP BY ms.id
            ORDER BY ms.created_at DESC
        ");
    }
    
    $stmt->execute();
    $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($content);
} catch (Exception $e) {
    error_log($e->getMessage());
    echo json_encode(['error' => 'Database error']);
}