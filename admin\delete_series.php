<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$series_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$series_id) {
    header('Location: series.php');
    exit;
}

try {
    $db->beginTransaction();

    // First delete all episode servers
    $stmt = $db->prepare("
        DELETE es FROM episode_servers es
        INNER JOIN episodes e ON es.episode_id = e.id
        WHERE e.series_id = ?
    ");
    $stmt->execute([$series_id]);

    // Then delete all episodes
    $stmt = $db->prepare("DELETE FROM episodes WHERE series_id = ?");
    $stmt->execute([$series_id]);

    // Finally delete the series
    $stmt = $db->prepare("DELETE FROM series WHERE id = ?");
    $stmt->execute([$series_id]);

    $db->commit();
    
    $_SESSION['success'] = "Series deleted successfully";
    header('Location: series.php');
    exit;

} catch (Exception $e) {
    $db->rollBack();
    error_log("Error deleting series: " . $e->getMessage());
    $_SESSION['error'] = "Failed to delete series";
    header('Location: series.php');
    exit;
}