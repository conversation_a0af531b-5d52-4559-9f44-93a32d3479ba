<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/tmdb_handler.php';

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

if (!isset($_SESSION['admin_id'])) {
    echo "data: " . json_encode(['error' => true, 'message' => 'Unauthorized']) . "\n\n";
    exit;
}

function sendSSE($data) {
    echo "data: " . json_encode($data) . "\n\n";
    ob_flush();
    flush();
}

// Read JSON file
$series_json = file_get_contents('../../data/series.json');
$series_list = json_decode($series_json, true) ?? [];
$total_series = count($series_list);

$success = [];
$skipped = [];
$failed = [];

$tmdb = new TMDBHandler();

foreach ($series_list as $index => $series) {
    try {
        // Calculate progress
        $progress = round(($index + 1) / $total_series * 100);
        
        // Send progress update
        sendSSE([
            'progress' => $progress,
            'status' => 'Importing ' . $series['title'] . ' (' . ($index + 1) . '/' . $total_series . ')'
        ]);

        // Your existing import logic here
        // ...

        $success[] = [
            'title' => $series['title'],
            'tmdb_id' => $series['tmdb_id'],
            'seasons_count' => count($series['seasons'] ?? [])
        ];

        // Add small delay to prevent overwhelming the browser
        usleep(100000); // 0.1 second delay

    } catch (Exception $e) {
        $failed[] = [
            'title' => $series['title'],
            'reason' => $e->getMessage()
        ];
    }
}

// Send final results
sendSSE([
    'complete' => true,
    'message' => 'Import completed successfully!',
    'details' => [
        'success' => $success,
        'skipped' => $skipped,
        'failed' => $failed
    ]
]);
