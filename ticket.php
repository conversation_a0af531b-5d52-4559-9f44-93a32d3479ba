<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';

if (!isset($_SESSION['user_id']) || !isset($_GET['id'])) {
    header('Location: support.php');
    exit;
}

$ticket_id = (int)$_GET['id'];

// Get ticket details
$stmt = $db->prepare("
    SELECT t.*, u.username 
    FROM support_tickets t 
    JOIN users u ON t.user_id = u.id 
    WHERE t.id = ? AND t.user_id = ?
");
$stmt->execute([$ticket_id, $_SESSION['user_id']]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$ticket) {
    header('Location: support.php');
    exit;
}

// Get messages
$stmt = $db->prepare("
    SELECT m.*, u.username 
    FROM support_messages m 
    JOIN users u ON m.sender_id = u.id 
    WHERE m.ticket_id = ? 
    ORDER BY m.created_at ASC
");
$stmt->execute([$ticket_id]);
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle new message
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message'])) {
    $message = trim($_POST['message']);
    $stmt = $db->prepare("INSERT INTO support_messages (ticket_id, sender_id, message) VALUES (?, ?, ?)");
    $stmt->execute([$ticket_id, $_SESSION['user_id'], $message]);
    header("Location: ticket.php?id=$ticket_id");
    exit;
}

$page_title = 'Ticket Details';
require_once 'includes/header.php';
?>

<div class="container my-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3><?= htmlspecialchars($ticket['subject']) ?></h3>
            <span class="badge bg-<?= $ticket['status'] === 'open' ? 'success' : 'secondary' ?>">
                <?= ucfirst($ticket['status']) ?>
            </span>
        </div>
        <div class="card-body">
            <!-- Messages -->
            <div class="messages-container mb-4" style="max-height: 400px; overflow-y: auto;">
                <?php foreach ($messages as $message): ?>
                    <div class="message mb-3 <?= $message['is_admin'] ? 'text-end' : '' ?>">
                        <div class="message-content <?= $message['is_admin'] ? 'bg-light' : 'bg-primary text-white' ?> d-inline-block p-3 rounded">
                            <p class="mb-1"><?= nl2br(htmlspecialchars($message['message'])) ?></p>
                            <small class="text-muted">
                                <?= $message['username'] ?> - 
                                <?= date('M d, Y H:i', strtotime($message['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Reply Form -->
            <?php if ($ticket['status'] === 'open'): ?>
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">আপনার মেসেজ</label>
                        <textarea name="message" class="form-control" rows="3" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">পাঠিয়ে দিন</button>
                </form>
            <?php else: ?>
                <div class="alert alert-info">This ticket is closed.</div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>