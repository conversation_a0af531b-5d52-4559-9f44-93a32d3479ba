<?php
require_once '../includes/db.php';

try {
    // Check if admin user already exists
    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    if (!$stmt->fetch()) {
        // Create admin user
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password, role, status) 
            VALUES (?, ?, ?, 'admin', 'active')
        ");
        
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt->execute(['admin', '<EMAIL>', $password]);
        
        echo "Admin user created successfully";
    } else {
        echo "Admin user already exists";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
