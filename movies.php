<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';

// Pagination settings
$items_per_page = 24;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $items_per_page;

// Get total movies count
$total_query = "SELECT COUNT(*) as total FROM movies WHERE status = 'active'";
$total_result = $db->query($total_query);
$total_movies = $total_result->fetch(PDO::FETCH_ASSOC)['total'];
$total_pages = ceil($total_movies / $items_per_page);

// Get movies with proper image paths
$movies_query = "SELECT *, 
                CASE 
                    WHEN poster_path LIKE 'http%' THEN poster_path 
                    WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END as poster_path
                FROM movies 
                WHERE status = 'active' 
                ORDER BY release_date DESC 
                LIMIT :offset, :limit";

$stmt = $db->prepare($movies_query);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
$stmt->execute();
$movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Movies";
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <h1 class="mb-4">Movies</h1>

    <div class="row g-4">
        <?php foreach ($movies as $movie): ?>
        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
            <div class="movie-card">
                <a href="movie.php?id=<?php echo $movie['id']; ?>">
                    <img src="<?php echo htmlspecialchars($movie['poster_path']); ?>" 
                         class="movie-poster" 
                         alt="<?php echo htmlspecialchars($movie['title']); ?>"
                         loading="lazy">
                    <div class="movie-info">
                        <h5 class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></h5>
                        <?php if($movie['release_date']): ?>
                            <div class="movie-year"><?php echo date('Y', strtotime($movie['release_date'])); ?></div>
                        <?php endif; ?>
                        <?php if($movie['rating']): ?>
                            <div class="movie-rating">
                                <i class="fas fa-star"></i>
                                <?php echo number_format($movie['rating'], 1); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </a>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <?php if ($total_pages > 1): ?>
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($current_page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=1" title="First page">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $current_page - 1; ?>">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            <?php endif; ?>
            
            <?php
            $range = 2;
            $start = max(1, $current_page - $range);
            $end = min($total_pages, $current_page + $range);
            
            if ($start > 1) {
                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            
            for ($i = $start; $i <= $end; $i++): ?>
                <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                </li>
            <?php endfor;
            
            if ($end < $total_pages) {
                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            ?>
            
            <?php if ($current_page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $current_page + 1; ?>">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $total_pages; ?>" title="Last page">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<style>
.movie-card {
    position: relative;
    transition: transform 0.2s;
    margin-bottom: 1rem;
}

.movie-card:hover {
    transform: translateY(-5px);
}

.movie-poster {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.movie-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    border-radius: 0 0 8px 8px;
}

.movie-title {
    color: white;
    font-size: 14px;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.movie-year {
    color: #aaa;
    font-size: 12px;
}

.movie-rating {
    color: #ffd700;
    font-size: 12px;
}

.movie-rating i {
    margin-right: 2px;
}

.pagination .page-link {
    background-color: #222;
    border-color: #444;
    color: #fff;
    padding: 0.5rem 0.75rem;
    min-width: 38px;
    text-align: center;
}

.pagination .page-link:focus {
    box-shadow: none;
    outline: none;
}

.pagination .page-item.active .page-link {
    background-color: #e50914;
    border-color: #e50914;
}

.pagination .page-link:hover {
    background-color: #444;
    border-color: #666;
    color: #fff;
}

.pagination .disabled .page-link {
    background-color: transparent;
    border-color: #444;
    color: #666;
}

.pagination .fas {
    font-size: 12px;
}

@media (max-width: 768px) {
    .movie-title {
        font-size: 12px;
    }
    
    .movie-year, .movie-rating {
        font-size: 11px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
