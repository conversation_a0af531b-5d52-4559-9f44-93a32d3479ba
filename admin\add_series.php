<?php
require_once "includes/auth.php";
require_once "../config/database.php";
include "includes/header.php";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tmdb_id = mysqli_real_escape_string($conn, $_POST['tmdb_id']);
    $title = mysqli_real_escape_string($conn, $_POST['title']);
    $original_title = mysqli_real_escape_string($conn, $_POST['original_title']);
    $overview = mysqli_real_escape_string($conn, $_POST['overview']);
    $poster_path = mysqli_real_escape_string($conn, $_POST['poster_path']);
    $backdrop_path = mysqli_real_escape_string($conn, $_POST['backdrop_path']);
    $first_air_date = mysqli_real_escape_string($conn, $_POST['first_air_date']);
    $last_air_date = mysqli_real_escape_string($conn, $_POST['last_air_date']);
    $status = mysqli_real_escape_string($conn, $_POST['status']);
    $featured = isset($_POST['featured']) ? 1 : 0;

    // Create slug from title
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));

    // Insert series
    $query = "INSERT INTO series (tmdb_id, title, original_title, slug, overview, poster_path, backdrop_path, 
              first_air_date, last_air_date, status, featured) 
              VALUES ('$tmdb_id', '$title', '$original_title', '$slug', '$overview', '$poster_path', 
              '$backdrop_path', '$first_air_date', '$last_air_date', '$status', $featured)";

    if (mysqli_query($conn, $query)) {
        $_SESSION['success'] = "Series added successfully!";
        header("Location: manage_series.php");
        exit();
    } else {
        $error = "Error: " . mysqli_error($conn);
    }
}
?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">Add New Series</h1>
                <a href="series.php" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left"></i> Back to Series
                </a>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card bg-dark">
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="tmdb_id" class="form-label">TMDB ID</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="tmdb_id" name="tmdb_id" required>
                                        <button type="button" class="btn btn-primary" onclick="fetchTMDBData()">
                                            <i class="fas fa-download"></i> Fetch Data
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>

                                <div class="mb-3">
                                    <label for="original_title" class="form-label">Original Title</label>
                                    <input type="text" class="form-control" id="original_title" name="original_title">
                                </div>

                                <div class="mb-3">
                                    <label for="overview" class="form-label">Overview</label>
                                    <textarea class="form-control" id="overview" name="overview" rows="4" required></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="poster_path" class="form-label">Poster Path</label>
                                            <input type="text" class="form-control" id="poster_path" name="poster_path" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="backdrop_path" class="form-label">Backdrop Path</label>
                                            <input type="text" class="form-control" id="backdrop_path" name="backdrop_path" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="first_air_date" class="form-label">First Air Date</label>
                                            <input type="date" class="form-control" id="first_air_date" name="first_air_date" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="last_air_date" class="form-label">Last Air Date</label>
                                            <input type="date" class="form-control" id="last_air_date" name="last_air_date">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="active">Active</option>
                                                <option value="inactive">Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input type="checkbox" class="form-check-input" id="featured" name="featured">
                                                <label class="form-check-label" for="featured">Featured Series</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-end mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Add Series
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card bg-dark">
                        <div class="card-body">
                            <h5 class="card-title mb-3">Preview</h5>
                            <div class="text-center">
                                <img id="poster_preview" src="https://via.placeholder.com/300x450" class="img-fluid rounded mb-3" alt="Poster Preview">
                                <img id="backdrop_preview" src="https://via.placeholder.com/500x281" class="img-fluid rounded" alt="Backdrop Preview">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control, .form-select {
    background-color: #2b2b2b;
    border-color: #404040;
    color: #fff;
}

.form-control:focus, .form-select:focus {
    background-color: #2b2b2b;
    border-color: #0d6efd;
    color: #fff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control::placeholder {
    color: #6c757d;
}

.form-label {
    color: #e6e6e6;
}

.card {
    border: 1px solid #404040;
}

.card-title {
    color: #e6e6e6;
}
</style>

<script>
const TMDB_API_KEY = '2c3edc10a403865e6451f07e71404109';

async function fetchTMDBData() {
    const tmdbId = document.getElementById('tmdb_id').value;
    if (!tmdbId) {
        alert('Please enter TMDB ID');
        return;
    }

    try {
        const response = await fetch(`https://api.themoviedb.org/3/tv/${tmdbId}?api_key=${TMDB_API_KEY}`);
        const data = await response.json();

        if (data.success === false) {
            alert('Series not found or error occurred');
            return;
        }

        // Fill form fields
        document.getElementById('title').value = data.name;
        document.getElementById('original_title').value = data.original_name;
        document.getElementById('overview').value = data.overview;
        document.getElementById('poster_path').value = data.poster_path;
        document.getElementById('backdrop_path').value = data.backdrop_path;
        document.getElementById('first_air_date').value = data.first_air_date;
        document.getElementById('last_air_date').value = data.last_air_date || '';

        // Update previews
        document.getElementById('poster_preview').src = `https://image.tmdb.org/t/p/w500${data.poster_path}`;
        document.getElementById('backdrop_preview').src = `https://image.tmdb.org/t/p/w500${data.backdrop_path}`;
    } catch (error) {
        alert('Error fetching TMDB data');
        console.error(error);
    }
}

// Preview image when paths change
document.getElementById('poster_path').addEventListener('input', function() {
    const path = this.value;
    const previewUrl = path.startsWith('http') ? path : `https://image.tmdb.org/t/p/w500${path}`;
    document.getElementById('poster_preview').src = previewUrl;
});

document.getElementById('backdrop_path').addEventListener('input', function() {
    const path = this.value;
    const previewUrl = path.startsWith('http') ? path : `https://image.tmdb.org/t/p/w500${path}`;
    document.getElementById('backdrop_preview').src = previewUrl;
});
</script>

<?php include "includes/footer.php"; ?> 