<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    die(json_encode(['error' => 'Unauthorized']));
}

header('Content-Type: application/json');

try {
    // Initialize TMDB handler
    $tmdb = new TMDBHandler();
    
    // Read movie.json file
    $json_file = '../../data/movie.json';
    if (!file_exists($json_file)) {
        throw new Exception('movie.json file not found');
    }
    
    $movies_json = file_get_contents($json_file);
    $movies = json_decode($movies_json, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON format: ' . json_last_error_msg());
    }

    $db->beginTransaction();
    
    $results = [
        'success' => [],
        'skipped' => [],
        'failed' => []
    ];

    foreach ($movies as $movie) {
        try {
            if (empty($movie['tmdb'])) {
                $results['skipped'][] = ['title' => $movie['title'], 'reason' => 'No TMDB ID'];
                continue;
            }

            // Check if movie already exists
            $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
            $stmt->execute([$movie['tmdb']]);
            if ($stmt->fetch()) {
                $results['skipped'][] = ['title' => $movie['title'], 'reason' => 'Already exists'];
                continue;
            }

            // Get movie details from TMDB
            $tmdb_details = $tmdb->getMovieById($movie['tmdb']);
            
            if (!$tmdb_details) {
                $results['failed'][] = ['title' => $movie['title'], 'reason' => 'Failed to fetch TMDB data'];
                continue;
            }

            // Generate slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $tmdb_details['title'])));

            // Insert movie
            $sql = "INSERT INTO movies (
                tmdb_id,
                title,
                original_title,
                slug,
                overview,
                poster_path,
                backdrop_path,
                release_date,
                runtime,
                rating,
                status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')";

            $stmt = $db->prepare($sql);
            $stmt->execute([
                $tmdb_details['id'],
                $tmdb_details['title'],
                $tmdb_details['original_title'] ?? $tmdb_details['title'],
                $slug,
                $tmdb_details['overview'],
                $tmdb_details['poster_path'],
                $tmdb_details['backdrop_path'],
                $tmdb_details['release_date'],
                $tmdb_details['runtime'] ?? 0,
                $tmdb_details['vote_average']
            ]);

            $movie_id = $db->lastInsertId();

            // Add default server
            $server_sql = "INSERT INTO movie_servers (movie_id, server_id, status) VALUES (?, 1, 'active')";
            $stmt = $db->prepare($server_sql);
            $stmt->execute([$movie_id]);

            $results['success'][] = [
                'title' => $tmdb_details['title'],
                'tmdb_id' => $tmdb_details['id']
            ];

            // Add small delay to prevent TMDB API rate limiting
            usleep(500000); // 0.5 second delay

        } catch (Exception $e) {
            $results['failed'][] = [
                'title' => $movie['title'] ?? 'Unknown',
                'reason' => $e->getMessage()
            ];
        }
    }

    $db->commit();

    echo json_encode([
        'success' => true,
        'message' => sprintf(
            'Import completed. Success: %d, Skipped: %d, Failed: %d',
            count($results['success']),
            count($results['skipped']),
            count($results['failed'])
        ),
        'details' => $results
    ], JSON_PRETTY_PRINT);

} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
}