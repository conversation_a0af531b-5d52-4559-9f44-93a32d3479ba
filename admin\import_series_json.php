<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = "Import Series from JSON";
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6>Import TV Series from JSON</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <ul>
                            <li>This tool will import TV series from series.json file using TMDB IDs</li>
                            <li>Special episodes (season 0) will be skipped</li>
                            <li>Each episode will be assigned to the default server</li>
                            <li>Import may take several minutes depending on the number of series</li>
                        </ul>
                    </div>
                    
                    <button id="startImport" class="btn btn-primary">
                        Start Import
                    </button>

                    <div id="progress" class="mt-4 d-none">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="status" class="mt-2"></div>
                    </div>

                    <div id="results" class="mt-4">
                        <div id="successList"></div>
                        <div id="skippedList"></div>
                        <div id="failedList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('startImport').addEventListener('click', async function() {
    const button = this;
    const progress = document.getElementById('progress');
    const progressBar = progress.querySelector('.progress-bar');
    const status = document.getElementById('status');
    const results = document.getElementById('results');

    button.disabled = true;
    progress.classList.remove('d-none');
    status.textContent = 'Importing series...';

    try {
        const eventSource = new EventSource('scripts/import_series_from_json.php');

        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.error) {
                throw new Error(data.message);
            }
            
            if (data.progress) {
                progressBar.style.width = `${data.progress}%`;
                progressBar.textContent = `${data.progress}%`;
                status.textContent = data.status || 'Importing...';
            }
            
            if (data.complete) {
                eventSource.close();
                
                // Display final results
                status.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                
                let html = '';
                if (data.details.success.length > 0) {
                    html += '<h6 class="text-success">Successfully Imported:</h6>';
                    html += '<ul>';
                    data.details.success.forEach(series => {
                        html += `<li>${series.title} (TMDB: ${series.tmdb_id}) - ${series.seasons_count} seasons</li>`;
                    });
                    html += '</ul>';
                }

                if (data.details.skipped.length > 0) {
                    html += '<h6 class="text-warning">Skipped:</h6>';
                    html += '<ul>';
                    data.details.skipped.forEach(series => {
                        html += `<li>${series.title} - ${series.reason}</li>`;
                    });
                    html += '</ul>';
                }

                if (data.details.failed.length > 0) {
                    html += '<h6 class="text-danger">Failed:</h6>';
                    html += '<ul>';
                    data.details.failed.forEach(series => {
                        html += `<li>${series.title} - ${series.reason}</li>`;
                    });
                    html += '</ul>';
                }

                results.innerHTML = html;
            }
        };

        eventSource.onerror = function(error) {
            eventSource.close();
            throw new Error('EventSource failed: ' + error.type);
        };

    } catch (error) {
        status.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
    } finally {
        button.disabled = false;
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
