<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';

$auth = new Auth($db);
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$page_title = 'Subscription Plans';
require_once 'includes/header.php';

// Get all active subscription plans
$stmt = $db->prepare("SELECT * FROM subscriptions WHERE status = 'active' ORDER BY price ASC");
$stmt->execute();
$plans = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user's current subscription
$stmt = $db->prepare("
    SELECT us.*, s.name as plan_name, s.features 
    FROM user_subscriptions us 
    JOIN subscriptions s ON us.subscription_id = s.id 
    WHERE us.user_id = ? AND us.end_date > NOW() 
    ORDER BY us.end_date DESC LIMIT 1
");
$stmt->execute([$_SESSION['user_id']]);
$current_subscription = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<div class="container py-5">
    <!-- Current Subscription Status -->
    <?php if ($current_subscription): ?>
    <div class="current-plan-box mb-5">
        <div class="card border-primary">
            <div class="card-body">
                <h3 class="card-title text-primary">
                    <i class="fas fa-crown me-2"></i>Current Plan: <?php echo htmlspecialchars($current_subscription['plan_name']); ?>
                </h3>
                <p class="card-text">
                    Valid until: <?php echo date('F j, Y', strtotime($current_subscription['end_date'])); ?>
                </p>
                <div class="progress" style="height: 20px;">
                    <?php
                    $total_days = (strtotime($current_subscription['end_date']) - strtotime($current_subscription['start_date'])) / (60 * 60 * 24);
                    $days_left = (strtotime($current_subscription['end_date']) - time()) / (60 * 60 * 24);
                    $percentage = ($days_left / $total_days) * 100;
                    ?>
                    <div class="progress-bar bg-primary" role="progressbar" 
                         style="width: <?php echo $percentage; ?>%" 
                         aria-valuenow="<?php echo $percentage; ?>" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        <?php echo round($days_left); ?> days left
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Subscription Plans -->
    <h2 class="text-center mb-5">Choose Your Plan</h2>
    <div class="row row-cols-1 row-cols-md-3 g-4 justify-content-center">
        <?php foreach ($plans as $plan): ?>
        <div class="col">
            <div class="card h-100 <?php echo $plan['slug'] === 'premium-plan' ? 'border-primary' : ''; ?>">
                <?php if ($plan['slug'] === 'premium-plan'): ?>
                <div class="ribbon-wrapper">
                    <div class="ribbon">POPULAR</div>
                </div>
                <?php endif; ?>
                
                <div class="card-body">
                    <div class="plan-icon mb-4">
                        <?php
                        $iconClass = 'fa-gem';
                        if ($plan['slug'] === 'basic-plan') $iconClass = 'fa-star';
                        if ($plan['slug'] === 'standard-plan') $iconClass = 'fa-crown';
                        ?>
                        <i class="fas <?php echo $iconClass; ?>"></i>
                    </div>
                    
                    <h3 class="card-title text-center mb-3"><?php echo htmlspecialchars($plan['name']); ?></h3>
                    
                    <div class="price-wrapper text-center mb-4">
                        <h4 class="price-tag">
                            <span class="currency">৳</span>
                            <span class="amount"><?php echo number_format($plan['price'], 0); ?></span>
                            <span class="duration">/<?php echo $plan['duration_days']; ?> days</span>
                        </h4>
                    </div>
                    
                    <div class="divider">
                        <span class="divider-text">Features</span>
                    </div>
                    
                    <div class="features-list">
                        <?php 
                        $features = json_decode($plan['features'], true);
                        foreach ($features as $feature):
                        ?>
                        <div class="feature-item">
                            <i class="fas fa-check-circle me-2"></i>
                            <span><?php echo htmlspecialchars($feature); ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="card-footer text-center bg-transparent">
                    <a href="checkout.php?plan=<?php echo $plan['id']; ?>" 
                       class="btn btn-lg <?php echo $plan['slug'] === 'premium-plan' ? 'btn-primary' : 'btn-outline-primary'; ?> subscribe-btn">
                        <span>Subscribe Now</span>
                        <i class="fas fa-arrow-right ms-2"></i>
                    </a>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<style>
/* Body Background */
body {
    background: linear-gradient(135deg, #1f1f1f 0%, #2d2d2d 100%);
    color: #ffffff;
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 15% 50%, rgba(255, 0, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 85% 30%, rgba(0, 0, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--bs-primary);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.5);
}

/* Text Colors */
.card-title {
    color: #ffffff;
}

.price-tag {
    color: #ffffff;
}

.duration {
    color: rgba(255, 255, 255, 0.7);
}

/* Divider */
.divider::before {
    background: rgba(255, 255, 255, 0.1);
}

.divider-text {
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
}

/* Features List */
.feature-item {
    border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.feature-item i {
    color: #0d6efd;
}

/* Section Title */
h2.text-center {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 3rem;
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

h2.text-center::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, #0d6efd, transparent);
}

/* Current Plan Box */
.current-plan-box .card {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(13, 110, 253, 0.05) 100%);
    border: 1px solid rgba(13, 110, 253, 0.2);
}

/* Floating Animation */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.card {
    animation: float 6s ease-in-out infinite;
    animation-delay: calc(var(--animation-order) * 0.2s);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(13, 110, 253, 0.5);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(13, 110, 253, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 2rem;
    }
    
    body::before {
        background-size: 200% 200%;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation delay to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.setProperty('--animation-order', index);
    });
});
</script>
