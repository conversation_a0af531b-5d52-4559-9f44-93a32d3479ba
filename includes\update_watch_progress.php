<?php
session_start();
require_once 'db.php';

if (!isset($_SESSION['user_id'])) {
    die(json_encode(['error' => 'Not logged in']));
}

$user_id = $_SESSION['user_id'];
$content_type = $_POST['content_type'] ?? '';
$content_id = (int)($_POST['content_id'] ?? 0);
$progress = (float)($_POST['progress'] ?? 0);

if (!$content_id || !in_array($content_type, ['movie', 'series'])) {
    die(json_encode(['error' => 'Invalid parameters']));
}

try {
    $stmt = $db->prepare("
        INSERT INTO watch_history (user_id, content_type, content_id, progress)
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE progress = ?, watched_at = CURRENT_TIMESTAMP
    ");
    
    $stmt->execute([$user_id, $content_type, $content_id, $progress, $progress]);
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    error_log("Watch history error: " . $e->getMessage());
    echo json_encode(['error' => 'Database error']);
}