-- Create genres table if not exists
CREATE TABLE IF NOT EXISTS genres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_status (status)
);

-- Create movie_genres table if not exists
CREATE TABLE IF NOT EXISTS movie_genres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    movie_id INT NOT NULL,
    genre_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (genre_id) REFERENCES genres(id) ON DELETE CASCADE,
    UNIQUE KEY unique_movie_genre (movie_id, genre_id),
    INDEX idx_movie_id (movie_id),
    INDEX idx_genre_id (genre_id)
);

-- Insert default genres
INSERT INTO genres (name, slug, description) VALUES
('Action', 'action', 'Action movies and shows'),
('Adventure', 'adventure', 'Adventure movies and shows'),
('Animation', 'animation', 'Animated movies and shows'),
('Comedy', 'comedy', 'Comedy movies and shows'),
('Crime', 'crime', 'Crime movies and shows'),
('Documentary', 'documentary', 'Documentary movies and shows'),
('Drama', 'drama', 'Drama movies and shows'),
('Family', 'family', 'Family movies and shows'),
('Fantasy', 'fantasy', 'Fantasy movies and shows'),
('History', 'history', 'Historical movies and shows'),
('Horror', 'horror', 'Horror movies and shows'),
('Music', 'music', 'Music movies and shows'),
('Mystery', 'mystery', 'Mystery movies and shows'),
('Romance', 'romance', 'Romance movies and shows'),
('Science Fiction', 'science-fiction', 'Science Fiction movies and shows'),
('Thriller', 'thriller', 'Thriller movies and shows'),
('War', 'war', 'War movies and shows'),
('Western', 'western', 'Western movies and shows');