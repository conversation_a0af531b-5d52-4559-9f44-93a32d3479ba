<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/server_manager.php';
require_once '../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$series_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$series_id) {
    header('Location: series.php');
    exit;
}

// Get series details
$stmt = $db->prepare("SELECT * FROM series WHERE id = ?");
$stmt->execute([$series_id]);
$series = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$series) {
    header('Location: series.php');
    exit;
}

$tmdb = new TMDBHandler();
$server_manager = new ServerManager($db);

// Handle series update
if (isset($_POST['update_series'])) {
    $title = $_POST['title'];
    $overview = $_POST['overview'];
    $status = $_POST['status'];

    $sql = "UPDATE series SET title = ?, overview = ?, status = ? WHERE id = ?";
    $stmt = $db->prepare($sql);
    if ($stmt->execute([$title, $overview, $status, $series_id])) {
        $success = "Series updated successfully";
    } else {
        $error = "Failed to update series";
    }
}

// Handle season import
if (isset($_POST['import_season'])) {
    $season_number = $_POST['season_number'];
    $season_data = $tmdb->getTVSeasonById($series['tmdb_id'], $season_number);
    
    if ($season_data) {
        $db->beginTransaction();
        try {
            // Add each episode
            foreach ($season_data['episodes'] as $episode) {
                $episode_id = $server_manager->addEpisode(
                    $series_id,
                    $episode['id'],
                    $season_number,
                    $episode['episode_number'],
                    $episode['name'],
                    $episode['overview'],
                    $episode['still_path'],
                    $episode['air_date']
                );

                if ($episode_id) {
                    $server_manager->addEpisodeServer(
                        $episode_id,
                        $series['tmdb_id'],
                        $season_number,
                        $episode['episode_number']
                    );
                }
            }
            $db->commit();
            $success = "Season imported successfully";
        } catch (Exception $e) {
            $db->rollBack();
            $error = "Failed to import season";
        }
    }
}

// Add series server
if (isset($_POST['add_series_server'])) {
    $server_id = (int)$_POST['server_id'];
    
    // Check if server already exists
    $check = $db->prepare("SELECT COUNT(*) FROM series_servers WHERE series_id = ? AND server_id = ?");
    $check->execute([$series_id, $server_id]);
    
    if ($check->fetchColumn() == 0) {
        $stmt = $db->prepare("
            INSERT INTO series_servers (series_id, server_id, status) 
            VALUES (?, ?, 'active')
        ");
        
        if ($stmt->execute([$series_id, $server_id])) {
            $success = "Server added successfully";
        } else {
            $error = "Failed to add server";
        }
    } else {
        $error = "This server is already added to the series";
    }
}

// Add AJAX handlers for server actions
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'remove_server':
            $server_id = (int)$_POST['server_id'];
            $stmt = $db->prepare("DELETE FROM series_servers WHERE id = ? AND series_id = ?");
            $success = $stmt->execute([$server_id, $series_id]);
            echo json_encode(['success' => $success]);
            exit;
            
        case 'toggle_status':
            $server_id = (int)$_POST['server_id'];
            $new_status = $_POST['status'] === 'active' ? 'inactive' : 'active';
            $stmt = $db->prepare("UPDATE series_servers SET status = ? WHERE id = ? AND series_id = ?");
            $success = $stmt->execute([$new_status, $server_id, $series_id]);
            echo json_encode(['success' => $success, 'new_status' => $new_status]);
            exit;
    }
}

// Get all episodes grouped by season
$episodes = $db->prepare("
    SELECT * FROM episodes 
    WHERE series_id = ? 
    ORDER BY season_number ASC, episode_number ASC
");
$episodes->execute([$series_id]);
$episodes = $episodes->fetchAll(PDO::FETCH_ASSOC);

// Group episodes by season
$seasons = [];
foreach ($episodes as $episode) {
    $season_num = $episode['season_number'];
    if (!isset($seasons[$season_num])) {
        $seasons[$season_num] = [];
    }
    $seasons[$season_num][] = $episode;
}

$page_title = 'Edit Series: ' . $series['title'];
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row g-4"> <!-- Added g-4 for gap between columns -->
        <div class="col-md-8 pe-md-4"> <!-- Added padding-end on medium screens -->
            <!-- Series Details Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">Edit Series Details</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">Title</label>
                            <input type="text" name="title" class="form-control" 
                                   value="<?php echo htmlspecialchars($series['title']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Overview</label>
                            <textarea name="overview" class="form-control" rows="4"><?php 
                                echo htmlspecialchars($series['overview']); 
                            ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="active" <?php echo $series['status'] === 'active' ? 'selected' : ''; ?>>
                                    Active
                                </option>
                                <option value="inactive" <?php echo $series['status'] === 'inactive' ? 'selected' : ''; ?>>
                                    Inactive
                                </option>
                            </select>
                        </div>
                        <button type="submit" name="update_series" class="btn btn-primary">
                            Update Series
                        </button>
                    </form>
                </div>
            </div>

            <!-- Series Servers Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Series Servers</h3>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSeriesServerModal">
                        Add Server
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Server Name</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $series_servers = $db->prepare("
                                    SELECT ss.*, s.name as server_name 
                                    FROM series_servers ss
                                    JOIN servers s ON ss.server_id = s.id
                                    WHERE ss.series_id = ?
                                ");
                                $series_servers->execute([$series_id]);
                                
                                foreach ($series_servers->fetchAll() as $server):
                                ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($server['server_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $server['status'] === 'active' ? 'success' : 'danger'; ?>">
                                            <?php echo ucfirst($server['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-danger" onclick="removeSeriesServer(<?php echo $server['id']; ?>)">
                                            Remove
                                        </button>
                                        <button class="btn btn-sm btn-<?php echo $server['status'] === 'active' ? 'warning' : 'success'; ?>"
                                                onclick="toggleServerStatus(<?php echo $server['id']; ?>, '<?php echo $server['status']; ?>')">
                                            <?php echo $server['status'] === 'active' ? 'Deactivate' : 'Activate'; ?>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Seasons & Episodes Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">Seasons & Episodes</h3>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" 
                            data-bs-target="#importSeasonModal">
                        Import Season
                    </button>
                </div>
                <div class="card-body">
                    <div class="accordion" id="seasonsAccordion">
                        <?php foreach ($seasons as $season_num => $season_episodes): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" 
                                        data-bs-target="#season<?php echo $season_num; ?>">
                                    Season <?php echo $season_num; ?>
                                </button>
                            </h2>
                            <div id="season<?php echo $season_num; ?>" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Episode</th>
                                                    <th>Title</th>
                                                    <th>Air Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($season_episodes as $episode): ?>
                                                <tr>
                                                    <td><?php echo $episode['episode_number']; ?></td>
                                                    <td><?php echo htmlspecialchars($episode['title']); ?></td>
                                                    <td><?php echo $episode['air_date']; ?></td>
                                                    <td>
                                                        <a href="edit_episode.php?id=<?php echo $episode['id']; ?>" 
                                                           class="btn btn-sm btn-primary">Edit</a>
                                                        <button class="btn btn-sm btn-danger delete-episode" 
                                                                data-id="<?php echo $episode['id']; ?>">
                                                            Delete
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Series Poster -->
            <div class="card sticky-top" style="top: 1rem;"> <!-- Made poster sticky -->
                <img src="https://image.tmdb.org/t/p/w500<?php echo $series['poster_path']; ?>" 
                     class="card-img-top" alt="<?php echo htmlspecialchars($series['title']); ?>">
            </div>
        </div>
    </div>
</div>

<!-- Import Season Modal -->
<div class="modal fade" id="importSeasonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Season</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">Season Number</label>
                        <input type="number" name="season_number" class="form-control" min="1" required>
                    </div>
                    <button type="submit" name="import_season" class="btn btn-primary">
                        Import Season
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Series Server Modal -->
<div class="modal fade" id="addSeriesServerModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Server to Series</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addSeriesServerForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Server</label>
                        <select name="server_id" class="form-select" required>
                            <option value="">Choose a server...</option>
                            <?php
                            $available_servers = $db->prepare("
                                SELECT s.* FROM servers s
                                WHERE s.id NOT IN (
                                    SELECT server_id FROM series_servers WHERE series_id = ?
                                )
                            ");
                            $available_servers->execute([$series_id]);
                            
                            foreach ($available_servers->fetchAll() as $server):
                            ?>
                            <option value="<?php echo $server['id']; ?>">
                                <?php echo htmlspecialchars($server['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_series_server" class="btn btn-primary">Add Server</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function removeSeriesServer(serverId) {
    if (confirm('Are you sure you want to remove this server?')) {
        fetch('edit_series.php?id=<?php echo $series_id; ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=remove_server&server_id=${serverId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to remove server');
            }
        });
    }
}

function toggleServerStatus(serverId, currentStatus) {
    fetch('edit_series.php?id=<?php echo $series_id; ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=toggle_status&server_id=${serverId}&status=${currentStatus}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to update server status');
        }
    });
}

// Add form submission handling
document.getElementById('addSeriesServerForm').addEventListener('submit', function(e) {
    const serverSelect = this.querySelector('select[name="server_id"]');
    if (!serverSelect.value) {
        e.preventDefault();
        alert('Please select a server');
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Handle episode deletion
    const deleteButtons = document.querySelectorAll('.delete-episode');
    deleteButtons.forEach(button => {
        button.addEventListener('click', async function() {
            if (confirm('Are you sure you want to delete this episode?')) {
                const episodeId = this.dataset.id;
                try {
                    const response = await fetch('ajax/delete_episode.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ episode_id: episodeId })
                    });
                    const data = await response.json();
                    if (data.success) {
                        this.closest('tr').remove();
                    } else {
                        alert('Failed to delete episode');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('An error occurred');
                }
            }
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>

<style>
/* Add responsive padding */
@media (min-width: 768px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Ensure cards have proper spacing */
.card {
    margin-bottom: 1.5rem;
}

/* Make table responsive */
.table-responsive {
    margin: 0;
    padding: 0;
    border: none;
}

/* Improve accordion styling */
.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
}

/* Improve button spacing in tables */
.btn-group-sm > .btn, .btn-sm {
    margin: 0.25rem;
}
</style>
