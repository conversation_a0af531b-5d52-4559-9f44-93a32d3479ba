<?php
function generateUniqueSlug($db, $title, $release_date = null, $table = 'movies') {
    // Basic slug generation
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    // Check if slug exists
    $stmt = $db->prepare("SELECT COUNT(*) FROM $table WHERE slug = ?");
    $stmt->execute([$slug]);
    $count = $stmt->fetchColumn();
    
    // If slug exists and we have release date, try with year
    if ($count > 0 && $release_date) {
        $year = substr($release_date, 0, 4);
        $slug_with_year = $slug . '-' . $year;
        
        $stmt->execute([$slug_with_year]);
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            return $slug_with_year;
        }
    }
    
    // If still exists, append number
    $original_slug = $slug;
    $counter = 1;
    while ($count > 0) {
        $slug = $original_slug . '-' . $counter;
        $stmt->execute([$slug]);
        $count = $stmt->fetchColumn();
        $counter++;
    }
    
    return $slug;
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
