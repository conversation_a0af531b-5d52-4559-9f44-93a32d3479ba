<?php
class B<PERSON><PERSON><PERSON><PERSON><PERSON> {
    private $app_key;
    private $app_secret;
    private $username;
    private $password;
    private $base_url;
    private $token;

    public function __construct() {
        $this->app_key = BKASH_APP_KEY;
        $this->app_secret = BKASH_APP_SECRET;
        $this->username = BKASH_USERNAME;
        $this->password = BKASH_PASSWORD;
        $this->base_url = BKASH_SANDBOX_URL;
    }

    private function getToken() {
        $token_url = $this->base_url . '/checkout/token/grant';
        
        $headers = [
            'username:' . $this->username,
            'password:' . $this->password,
            'Content-Type:application/json'
        ];
        
        $post_data = [
            'app_key' => $this->app_key,
            'app_secret' => $this->app_secret
        ];

        $curl = curl_init($token_url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        
        $response = curl_exec($curl);
        curl_close($curl);

        $response_data = json_decode($response, true);
        return $response_data['id_token'] ?? null;
    }

    public function createPayment($amount, $invoice) {
        $this->token = $this->getToken();
        
        $headers = [
            'Authorization: ' . $this->token,
            'X-APP-Key: ' . $this->app_key,
            'Content-Type: application/json'
        ];

        $post_data = [
            'mode' => '0011',
            'payerReference' => ' ',
            'callbackURL' => 'https://your-domain.com/bkash-callback.php',
            'amount' => $amount,
            'currency' => 'BDT',
            'intent' => 'sale',
            'merchantInvoiceNumber' => $invoice
        ];

        $curl = curl_init($this->base_url . '/checkout/create');
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($post_data));
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        
        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response, true);
    }

    public function executePayment($paymentID) {
        $this->token = $this->getToken();
        
        $headers = [
            'Authorization: ' . $this->token,
            'X-APP-Key: ' . $this->app_key,
            'Content-Type: application/json'
        ];

        $curl = curl_init($this->base_url . '/checkout/execute/' . $paymentID);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        
        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response, true);
    }
}