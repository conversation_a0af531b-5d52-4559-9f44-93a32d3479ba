-- Drop existing database if exists and create new
DROP DATABASE IF EXISTS tipsbdxy_099;
CREATE DATABASE tipsbdxy_099;
USE tipsbdxy_099;

-- Users table with all necessary fields
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
    status ENUM('active', 'banned', 'pending') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    profile_picture VARCHAR(255) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    bio TEXT,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- Categories table
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(50) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Movies table with enhanced fields
CREATE TABLE movies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tmdb_id INT UNIQUE,
    title VARCHAR(255) NOT NULL,
    original_title VARCHAR(255),
    slug VARCHAR(255) UNIQUE NOT NULL,
    overview TEXT,
    poster_path VARCHAR(255),
    backdrop_path VARCHAR(255),
    release_date DATE,
    runtime INT,
    rating DECIMAL(3,1),
    genres TEXT,
    language VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    views INT DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tmdb_id (tmdb_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured)
);

-- Series table with enhanced fields
CREATE TABLE series (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tmdb_id INT UNIQUE,
    title VARCHAR(255) NOT NULL,
    original_title VARCHAR(255),
    slug VARCHAR(255) UNIQUE NOT NULL,
    overview TEXT,
    poster_path VARCHAR(255),
    backdrop_path VARCHAR(255),
    first_air_date DATE,
    last_air_date DATE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    views INT DEFAULT 0,
    featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tmdb_id (tmdb_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured)
);

-- Seasons table
CREATE TABLE seasons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    series_id INT NOT NULL,
    tmdb_id INT,
    season_number INT NOT NULL,
    name VARCHAR(255),
    overview TEXT,
    poster_path VARCHAR(255),
    air_date DATE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
    UNIQUE KEY unique_season (series_id, season_number)
);

-- Episodes table with enhanced fields
CREATE TABLE episodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    series_id INT NOT NULL,
    season_id INT NOT NULL,
    tmdb_id INT,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    overview TEXT,
    still_path VARCHAR(255),
    air_date DATE,
    runtime INT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    views INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE,
    UNIQUE KEY unique_episode (series_id, season_id, episode_number)
);

-- First create server_patterns table
CREATE TABLE server_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    movie_pattern VARCHAR(255) NOT NULL,
    series_pattern VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Then create servers table with pattern_id column
CREATE TABLE servers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    base_url VARCHAR(255) NOT NULL,
    api_url VARCHAR(255),
    api_key VARCHAR(255),
    pattern_id INT,
    type ENUM('movie', 'tv') DEFAULT 'movie',
    priority INT DEFAULT 1,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pattern_id) REFERENCES server_patterns(id),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);

-- Now insert server patterns
INSERT INTO server_patterns (name, movie_pattern, series_pattern) VALUES
('Flicky Default Pattern', '/embed/movie/?id={tmdb_id}', '/embed/tv/?id={tmdb_id}/{season_number}/{episode_number}'),
('AutoEmbed Default Pattern', '/movie/{tmdb_id}', '/tv/{tmdb_id}/{season_number}/{episode_number}'),
('VidSrc Pattern', '/embed/movie/{tmdb_id}', '/embed/tv/{tmdb_id}/{season_number}/{episode_number}'),
('SuperEmbed Pattern', '/embed/{tmdb_id}', '/embed-tv/{tmdb_id}/{season_number}/{episode_number}'),
('2embed Pattern', '/embed/{tmdb_id}', '/embed/series/{tmdb_id}/{season_number}/{episode_number}');

-- Finally insert default servers
INSERT INTO servers (name, base_url, status, priority, pattern_id) VALUES 
('Flicky', 'https://flicky.host', 'active', 1, 
    (SELECT id FROM server_patterns WHERE name = 'Flicky Default Pattern')),
('AutoEmbed', 'https://player.autoembed.cc/embed', 'active', 2,
    (SELECT id FROM server_patterns WHERE name = 'AutoEmbed Default Pattern')),
('VidSrc', 'https://vidsrc.xyz', 'active', 3,
    (SELECT id FROM server_patterns WHERE name = 'VidSrc Pattern')),
('SuperEmbed', 'https://superembed.stream', 'active', 4,
    (SELECT id FROM server_patterns WHERE name = 'SuperEmbed Pattern')),
('2embed', 'https://2embed.org', 'active', 5,
    (SELECT id FROM server_patterns WHERE name = '2embed Pattern'));

-- Create movie_servers table
CREATE TABLE movie_servers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    movie_id INT NOT NULL,
    server_id INT NOT NULL,
    url VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    INDEX idx_movie_id (movie_id),
    INDEX idx_server_id (server_id),
    INDEX idx_status (status)
);

-- Create episode_servers table
CREATE TABLE episode_servers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    episode_id INT NOT NULL,
    server_id INT NOT NULL,
    url VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    INDEX idx_episode_id (episode_id),
    INDEX idx_server_id (server_id),
    INDEX idx_status (status)
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_days INT NOT NULL,
    features TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User subscriptions table
CREATE TABLE user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id),
    INDEX idx_user_id (user_id),
    INDEX idx_end_date (end_date)
);

-- Payments table
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('bkash', 'nagad', 'rocket', 'offline') NOT NULL,
    transaction_id VARCHAR(100),
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- User activity tracking
CREATE TABLE user_activity (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    activity_type ENUM('login', 'view_movie', 'view_episode', 'subscription') NOT NULL,
    content_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type)
);

-- Admin table with enhanced security and logging
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role ENUM('super_admin', 'admin', 'editor') DEFAULT 'admin',
    status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    auth_token VARCHAR(255),
    last_login TIMESTAMP NULL,
    last_login_ip VARCHAR(45),
    login_attempts INT DEFAULT 0,
    profile_picture VARCHAR(255),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- Admin activity log table
CREATE TABLE admin_activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action_type ENUM(
        'login', 
        'logout', 
        'add_movie', 
        'edit_movie', 
        'delete_movie',
        'add_series',
        'edit_series',
        'delete_series',
        'manage_user',
        'manage_subscription',
        'manage_payment',
        'system_settings'
    ) NOT NULL,
    action_details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at)
);

-- Admin permissions table
CREATE TABLE admin_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin role permissions mapping
CREATE TABLE admin_role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES admin_permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_admin_permission (admin_id, permission_id)
);

-- Insert default super admin (password: Admin@123)
INSERT INTO admins (username, email, password, full_name, role, status) VALUES 
('superadmin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Super Admin', 'super_admin', 'active');

-- Insert default permissions
INSERT INTO admin_permissions (name, slug, description) VALUES
('Manage Movies', 'manage-movies', 'Can add, edit, and delete movies'),
('Manage Series', 'manage-series', 'Can add, edit, and delete TV series'),
('Manage Users', 'manage-users', 'Can manage user accounts'),
('Manage Subscriptions', 'manage-subscriptions', 'Can manage subscription plans'),
('Manage Payments', 'manage-payments', 'Can handle payment operations'),
('View Statistics', 'view-statistics', 'Can view site statistics and analytics'),
('Manage Settings', 'manage-settings', 'Can modify system settings'),
('Manage Admins', 'manage-admins', 'Can manage admin accounts');

-- Assign all permissions to super admin
INSERT INTO admin_role_permissions (admin_id, permission_id)
SELECT 1, id FROM admin_permissions;

-- After creating all tables, add server patterns
CREATE TABLE server_patterns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    movie_pattern VARCHAR(255) NOT NULL,
    series_pattern VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add pattern_id column to servers table
ALTER TABLE servers
ADD COLUMN pattern_id INT,
ADD FOREIGN KEY (pattern_id) REFERENCES server_patterns(id);

-- Insert default server patterns
INSERT INTO server_patterns (name, movie_pattern, series_pattern) VALUES
('Flicky Default Pattern', '/embed/movie/?id={tmdb_id}', '/embed/tv/?id={tmdb_id}/{season_number}/{episode_number}'),
('AutoEmbed Default Pattern', '/movie/{tmdb_id}', '/tv/{tmdb_id}/{season_number}/{episode_number}'),
('VidSrc Pattern', '/embed/movie/{tmdb_id}', '/embed/tv/{tmdb_id}/{season_number}/{episode_number}'),
('SuperEmbed Pattern', '/embed/{tmdb_id}', '/embed-tv/{tmdb_id}/{season_number}/{episode_number}'),
('2embed Pattern', '/embed/{tmdb_id}', '/embed/series/{tmdb_id}/{season_number}/{episode_number}');

-- Insert default servers
INSERT INTO servers (name, base_url, status, priority, pattern_id) VALUES 
('Flicky', 'https://flicky.host', 'active', 1, 
    (SELECT id FROM server_patterns WHERE name = 'Flicky Default Pattern')),
('AutoEmbed', 'https://player.autoembed.cc/embed', 'active', 2,
    (SELECT id FROM server_patterns WHERE name = 'AutoEmbed Default Pattern')),
('VidSrc', 'https://vidsrc.xyz', 'active', 3,
    (SELECT id FROM server_patterns WHERE name = 'VidSrc Pattern')),
('SuperEmbed', 'https://superembed.stream', 'active', 4,
    (SELECT id FROM server_patterns WHERE name = 'SuperEmbed Pattern')),
('2embed', 'https://2embed.org', 'active', 5,
    (SELECT id FROM server_patterns WHERE name = '2embed Pattern'));

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role, status) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active');

-- Insert default subscription plans
INSERT INTO subscriptions (name, slug, description, price, duration_days, features, status) VALUES
('Free Plan', 'free-plan', 'Basic streaming access with ads', 0.00, 30, '["480p Streaming", "With Ads"]', 'active'),
('Premium Plan', 'premium-plan', 'HD streaming with no ads', 199.00, 30, '["1080p Streaming", "Ad-free viewing", "Full content access", "Download support"]', 'active'),
('VIP Plan', 'vip-plan', '4K streaming with all features', 299.00, 30, '["4K Streaming", "Ad-free viewing", "Full content access", "Download support", "Early access"]', 'active');

-- Add indexes for better performance
ALTER TABLE movies ADD FULLTEXT INDEX idx_movie_search (title, original_title);
ALTER TABLE series ADD FULLTEXT INDEX idx_series_search (title, original_title);

-- Insert some movie categories
INSERT INTO categories (name, slug, description, status) VALUES
('Action', 'action', 'Action movies', 'active'),
('Comedy', 'comedy', 'Comedy movies', 'active'),
('Drama', 'drama', 'Drama movies', 'active'),
('Horror', 'horror', 'Horror movies', 'active'),
('Sci-Fi', 'sci-fi', 'Science Fiction movies', 'active');

-- Settings table
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    site_name VARCHAR(100) NOT NULL DEFAULT 'CinePix32',
    site_description TEXT,
    site_keywords TEXT,
    tmdb_api_key VARCHAR(255),
    items_per_page INT DEFAULT 24,
    maintenance_mode TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO settings (id, site_name, site_description, site_keywords, tmdb_api_key, items_per_page) 
VALUES (
    1, 
    'CinePix32', 
    'Watch Movies and TV Shows Online', 
    'movies, tv shows, streaming, online movies',
    '3d36f64b789ec5484c76838f0ba11daf',
    24
);

ALTER TABLE seasons ADD COLUMN poster_path VARCHAR(255) AFTER season_number;

CREATE TABLE series_servers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    series_id INT NOT NULL,
    server_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_series_server (series_id, server_id)
);

-- Live TV Channels table
CREATE TABLE live_channels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    logo VARCHAR(255),
    stream_url TEXT NOT NULL,
    description TEXT,
    category VARCHAR(50),
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_category (category)
);

CREATE TABLE IF NOT EXISTS watch_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'series') NOT NULL,
    content_id INT NOT NULL,
    progress FLOAT DEFAULT 0,
    watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_content (user_id, content_type, content_id)
);

CREATE TABLE user_content_access (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_type ENUM('movies', 'series', 'tv', 'premium') NOT NULL,
    access_type ENUM('granted', 'blocked') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_content (user_id, content_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE support_tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    status ENUM('open', 'closed', 'pending') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE support_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_id INT NOT NULL,
    sender_id INT NOT NULL,
    message TEXT NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Direct sources table for movies and series
CREATE TABLE direct_sources (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_type ENUM('movie', 'series') NOT NULL,
    content_id INT NOT NULL,
    video_url VARCHAR(255) NOT NULL,
    quality VARCHAR(20) NOT NULL,
    episode_id INT DEFAULT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    INDEX idx_content (content_type, content_id),
    INDEX idx_episode (episode_id)
);

-- First, let's check the current structure
DESCRIBE direct_sources;

-- Then alter the table to match our needs
ALTER TABLE direct_sources 
    MODIFY COLUMN video_url VARCHAR(255) NOT NULL,
    MODIFY COLUMN quality VARCHAR(20) NOT NULL,
    MODIFY COLUMN status ENUM('active', 'inactive') DEFAULT 'active',
    ADD INDEX idx_content (content_type, content_id) IF NOT EXISTS,
    ADD INDEX idx_episode (episode_id) IF NOT EXISTS;

CREATE TABLE user_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'series') NOT NULL,
    content_id INT NOT NULL,
    server_id INT NOT NULL,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (server_id) REFERENCES servers(id),
    INDEX idx_user_content (user_id, content_type, content_id)
);

-- Add indexes for better search performance
ALTER TABLE movies ADD FULLTEXT INDEX idx_movie_search (title, original_title, overview);
ALTER TABLE manual_movies ADD FULLTEXT INDEX idx_manual_movie_search (title, original_title, overview);
ALTER TABLE series ADD FULLTEXT INDEX idx_series_search (title, original_title, overview);

-- Add indexes for year searching
ALTER TABLE movies ADD INDEX idx_movie_release_date (release_date);
ALTER TABLE manual_movies ADD INDEX idx_manual_movie_release_date (release_date);
ALTER TABLE series ADD INDEX idx_series_first_air_date (first_air_date);

CREATE TABLE remember_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(64) NOT NULL,
    expires_at DATETIME NOT NULL,
    is_valid TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_remember_token ON remember_tokens(token);
