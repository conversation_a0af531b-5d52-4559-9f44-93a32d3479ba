<?php
/**
 * Renders a content card (movie/series)
 * 
 * @param array $content Content data with keys:
 *   - id: Content ID
 *   - title: Content title
 *   - poster_path: Poster image URL
 *   - rating: Rating value (optional)
 *   - release_date/first_air_date: Release date (optional)
 *   - content_type: 'movie' or 'series' (optional)
 *   - season_count: Number of seasons (for series, optional)
 */
function render_content_card($content) {
    $title = isset($content['title']) ? $content['title'] : $content['name'];
    $poster = $content['poster_path'];
    $rating = isset($content['rating']) ? $content['rating'] : 0;
    $year = isset($content['release_date']) ? date('Y', strtotime($content['release_date'])) : '';
    $content_type = isset($content['seasons']) ? 'series' : 'movie';
    $content_id = $content['id'];
    
    // Calculate rating stars
    $full_stars = floor($rating / 2);
    $half_star = ($rating / 2) - $full_stars >= 0.5;
    ?>
    
    <div class="content-card">
        <a href="<?php echo $content_type; ?>.php?id=<?php echo $content_id; ?>" class="card-link">
            <div class="card-poster">
                <!-- Poster Image with Gradient Overlay -->
                <img src="<?php echo htmlspecialchars($poster); ?>" alt="<?php echo htmlspecialchars($title); ?>" class="poster-img">
                <div class="card-overlay">
                    <div class="play-icon">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
                
                <!-- Quality Badge -->
                <div class="quality-badge">
                    HD
                </div>
                
                <!-- Rating Stars -->
                <div class="rating-stars">
                    <?php
                    for ($i = 0; $i < 5; $i++) {
                        if ($i < $full_stars) {
                            echo '<i class="fas fa-star"></i>';
                        } elseif ($i == $full_stars && $half_star) {
                            echo '<i class="fas fa-star-half-alt"></i>';
                        } else {
                            echo '<i class="far fa-star"></i>';
                        }
                    }
                    ?>
                </div>
            </div>
            
            <!-- Content Info -->
            <div class="card-info">
                <h3 class="content-title"><?php echo htmlspecialchars($title); ?></h3>
                <div class="content-meta">
                    <span class="year"><?php echo $year; ?></span>
                    <?php if ($content_type === 'series'): ?>
                        <span class="type-badge">Series</span>
                    <?php endif; ?>
                </div>
            </div>
        </a>
    </div>

    <style>
    .content-card {
        position: relative;
        transition: transform 0.3s ease;
        margin-bottom: 1rem;
        border-radius: 8px;
        overflow: hidden;
        background: #1a1a1a;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .content-card:hover {
        transform: translateY(-5px);
    }

    .card-link {
        text-decoration: none;
        color: inherit;
    }

    .card-poster {
        position: relative;
        padding-top: 150%; /* 2:3 Aspect Ratio */
        overflow: hidden;
    }

    .poster-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .content-card:hover .poster-img {
        transform: scale(1.05);
    }

    .card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.3s ease;
    }

    .content-card:hover .card-overlay {
        opacity: 1;
    }

    .play-icon {
        width: 50px;
        height: 50px;
        background: rgba(255,255,255,0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .play-icon i {
        color: #e50914;
        font-size: 20px;
        margin-left: 4px;
    }

    .quality-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(229, 9, 20, 0.9);
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }

    .rating-stars {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 8px;
        background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
        color: #ffd700;
        font-size: 14px;
    }

    .card-info {
        padding: 12px;
    }

    .content-title {
        color: #fff;
        font-size: 14px;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .content-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 4px;
        font-size: 12px;
        color: #aaa;
    }

    .type-badge {
        background: #e50914;
        color: white;
        padding: 1px 6px;
        border-radius: 3px;
        font-size: 11px;
    }

    .year {
        color: #aaa;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .content-title {
            font-size: 13px;
        }
        
        .play-icon {
            width: 40px;
            height: 40px;
        }
        
        .play-icon i {
            font-size: 16px;
        }
    }
    </style>
    <?php
}
?>
