<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';
require_once '../includes/server_manager.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$tmdb = new TMDBHandler();

// Pagination Configuration
$items_per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 20;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$current_page = max(1, $current_page);

// Get search and filter parameters
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';

try {
    // Base query for counting total records
    $count_query = "SELECT COUNT(DISTINCT m.id) FROM movies m";
    $where_conditions = [];
    $params = [];

    // Add search conditions
    if (!empty($search_query)) {
        $where_conditions[] = "(m.title LIKE ? OR m.tmdb_id LIKE ?)";
        $params[] = "%$search_query%";
        $params[] = "%$search_query%";
    }

    // Add status filter
    if (!empty($status_filter)) {
        $where_conditions[] = "m.status = ?";
        $params[] = $status_filter;
    }

    // Add WHERE clause if conditions exist
    if (!empty($where_conditions)) {
        $count_query .= " WHERE " . implode(" AND ", $where_conditions);
    }

    // Execute count query
    $stmt = $db->prepare($count_query);
    $stmt->execute($params);
    $total_items = $stmt->fetchColumn();

    // Calculate pagination values
    $total_pages = ceil($total_items / $items_per_page);
    $current_page = min($current_page, max(1, $total_pages));
    $offset = ($current_page - 1) * $items_per_page;

    // Main query for fetching movies
    $query = "SELECT m.*, 
                     (SELECT COUNT(*) FROM movie_servers ms WHERE ms.movie_id = m.id) as server_count
              FROM movies m";

    // Add WHERE clause if conditions exist
    if (!empty($where_conditions)) {
        $query .= " WHERE " . implode(" AND ", $where_conditions);
    }

    // Add ORDER BY and LIMIT - using direct integer values instead of parameters
    $query .= " ORDER BY m.id DESC LIMIT $items_per_page OFFSET $offset";

    // Execute main query
    $stmt = $db->prepare($query);
    $stmt->execute($params); // Remove LIMIT/OFFSET from params
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    $movies = [];
    $total_items = 0;
    $total_pages = 1;
}

// Pagination URL generator function
function generatePaginationUrl($page) {
    $params = $_GET;
    $params['page'] = $page;
    return '?' . http_build_query($params);
}

// Handle TMDB Search
if (isset($_POST['tmdb_search'])) {
    $search_query = $_POST['tmdb_query'];
    $tmdb_results = $tmdb->searchMovie($search_query);
}

// Handle Bulk Import
if (isset($_POST['bulk_import'])) {
    $year = $_POST['year'];
    $page = 1; // You can add pagination later
    
    try {
        $response = $tmdb->bulkImportMovies($year, $page);
        if (isset($response['results'])) {
            $imported = 0;
            foreach ($response['results'] as $movie) {
                // Check if movie exists
                $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
                $stmt->execute([$movie['id']]);
                if (!$stmt->fetch()) {
                    // Import movie
                    $result = $tmdb->importMovie($db, $movie['id']);
                    if ($result['success']) {
                        $imported++;
                    }
                }
            }
            $success = "Successfully imported $imported movies from year $year";
        }
    } catch (Exception $e) {
        $error = "Import error: " . $e->getMessage();
    }
}

// Handle movie search
$search_query = isset($_GET['search']) ? $_GET['search'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Add this to your existing movie update logic
if (isset($_POST['toggle_featured'])) {
    $movie_id = (int)$_POST['movie_id'];
    $featured = (int)$_POST['featured'];
    
    try {
        // First, remove featured flag from all movies if setting a new featured movie
        if ($featured === 1) {
            $db->query("UPDATE movies SET featured = 0");
        }
        
        // Then update the selected movie
        $stmt = $db->prepare("UPDATE movies SET featured = ? WHERE id = ?");
        $stmt->execute([$featured, $movie_id]);
        
        $success = "Movie featured status updated successfully";
    } catch (PDOException $e) {
        $error = "Error updating featured status: " . $e->getMessage();
    }
}

require_once 'includes/header.php';
?>

<div class="container-fluid py-1">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Movies Management</h2>
        <div>
            <!-- Bulk Import Button -->
            <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#tmdbIdImportModal">
                <i class="fas fa-cloud-download-alt"></i> Import by TMDB ID
            </button>
            
            <!-- TMDB Search Button -->
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tmdbSearchModal">
                <i class="fas fa-search"></i> Search TMDB
            </button>
        </div>
    </div>

    <!-- TMDB Search Modal -->
    <div class="modal fade" id="tmdbSearchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Search TMDB Movies</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form method="POST" class="mb-4">
                        <div class="input-group">
                            <input type="text" name="tmdb_query" class="form-control" placeholder="Enter movie name..." required>
                            <button type="submit" name="tmdb_search" class="btn btn-primary">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </form>
                    
                    <!-- Search Results will appear here -->
                    <?php if (!empty($tmdb_results['results'])): ?>
                        <div class="row">
                            <?php foreach ($tmdb_results['results'] as $movie): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <img src="https://image.tmdb.org/t/p/w342<?php echo $movie['poster_path']; ?>" 
                                             class="card-img-top" alt="<?php echo $movie['title']; ?>">
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo $movie['title']; ?></h6>
                                            <p class="small text-muted"><?php echo substr($movie['release_date'], 0, 4); ?></p>
                                            <form method="POST">
                                                <input type="hidden" name="tmdb_id" value="<?php echo $movie['id']; ?>">
                                                <button type="submit" name="import_tmdb" class="btn btn-success btn-sm w-100">
                                                    <i class="fas fa-download"></i> Import
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Import Modal -->
    <div class="modal fade" id="tmdbIdImportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Import Movie by TMDB ID</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="tmdb_ids" class="form-label">Enter TMDB IDs (one per line)</label>
                            <textarea name="tmdb_ids" id="tmdb_ids" class="form-control" rows="5" placeholder="Enter TMDB IDs here...&#10;Example:&#10;505642&#10;976573&#10;667538" required></textarea>
                            <small class="text-muted">You can find TMDB ID from themoviedb.org URL</small>
                        </div>
                        <button type="submit" name="import_by_ids" class="btn btn-success w-100">
                            <i class="fas fa-download"></i> Import Movies
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?php 
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
        </div>
    <?php endif; ?>

    <!-- Search and Filter Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <input type="text" name="search" class="form-control" placeholder="Search by title or TMDB ID" value="<?php echo htmlspecialchars($search_query); ?>">
                </div>
                <div class="col-md-4">
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Search</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Movies Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>ID</th>
                            <th>Poster</th>
                            <th>Title</th>
                            <th>TMDB ID</th>
                            <th>Release Date</th>
                            <th>Servers</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($movies as $movie): ?>
                        <tr>
                            <td><input type="checkbox" class="movie-checkbox" value="<?php echo $movie['id']; ?>"></td>
                            <td><?php echo $movie['id']; ?></td>
                            <td>
                                <img src="https://image.tmdb.org/t/p/w92<?php echo $movie['poster_path']; ?>" 
                                     alt="<?php echo htmlspecialchars($movie['title']); ?>"
                                     style="width: 50px; cursor: pointer"
                                     onclick="window.open(this.src.replace('w92', 'original'), '_blank')">
                            </td>
                            <td><?php echo htmlspecialchars($movie['title']); ?></td>
                            <td><?php echo $movie['tmdb_id']; ?></td>
                            <td><?php echo $movie['release_date']; ?></td>
                            <td>
                                <span class="badge bg-info"><?php echo $movie['server_count']; ?></span>
                            </td>
                            <td>
                                <button class="btn btn-sm status-toggle <?php echo $movie['status'] === 'active' ? 'btn-success' : 'btn-danger'; ?>"
                                        data-id="<?php echo $movie['id']; ?>"
                                        data-status="<?php echo $movie['status']; ?>">
                                    <?php echo ucfirst($movie['status']); ?>
                                </button>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="edit_movie.php?id=<?php echo $movie['id']; ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="manage_servers.php?movie_id=<?php echo $movie['id']; ?>" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-server"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-danger btn-sm" 
                                            onclick="deleteMovie(<?php echo $movie['id']; ?>)">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                                    <input type="hidden" name="featured" value="<?php echo $movie['featured'] ? 0 : 1; ?>">
                                    <button type="submit" name="toggle_featured" class="btn btn-sm <?php echo $movie['featured'] ? 'btn-success' : 'btn-secondary'; ?>">
                                        <i class="fas fa-star"></i> <?php echo $movie['featured'] ? 'Featured' : 'Make Featured'; ?>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Bulk Actions -->
            <div class="mt-3">
                <button id="bulkDelete" class="btn btn-danger" disabled>Delete Selected</button>
                <button id="bulkActivate" class="btn btn-success ms-2" disabled>Activate Selected</button>
                <button id="bulkDeactivate" class="btn btn-warning ms-2" disabled>Deactivate Selected</button>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div class="text-muted">
                    Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $items_per_page, $total_items); ?> 
                    of <?php echo $total_items; ?> movies
                </div>
                
                <nav aria-label="Page navigation">
                    <ul class="pagination mb-0">
                        <!-- First Page -->
                        <li class="page-item <?php echo ($current_page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo generatePaginationUrl(1); ?>">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        
                        <!-- Previous Page -->
                        <li class="page-item <?php echo ($current_page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo generatePaginationUrl($current_page - 1); ?>">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>

                        <?php
                        $range = 2;
                        $start_page = max(1, $current_page - $range);
                        $end_page = min($total_pages, $current_page + $range);

                        if ($start_page > 1) {
                            echo '<li class="page-item"><a class="page-link" href="' . generatePaginationUrl(1) . '">1</a></li>';
                            if ($start_page > 2) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                        }

                        for ($i = $start_page; $i <= $end_page; $i++) {
                            echo '<li class="page-item ' . ($current_page == $i ? 'active' : '') . '">';
                            echo '<a class="page-link" href="' . generatePaginationUrl($i) . '">' . $i . '</a>';
                            echo '</li>';
                        }

                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="' . generatePaginationUrl($total_pages) . '">' . $total_pages . '</a></li>';
                        }
                        ?>

                        <!-- Next Page -->
                        <li class="page-item <?php echo ($current_page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo generatePaginationUrl($current_page + 1); ?>">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>

                        <!-- Last Page -->
                        <li class="page-item <?php echo ($current_page >= $total_pages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="<?php echo generatePaginationUrl($total_pages); ?>">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>

                <!-- Items per page selector -->
                <div class="d-flex align-items-center">
                    <label class="me-2">Per page:</label>
                    <select class="form-select form-select-sm" style="width: auto;" onchange="window.location.href=this.value">
                        <?php
                        $per_page_options = [10, 20, 50, 100];
                        foreach ($per_page_options as $option) {
                            $selected = $items_per_page == $option ? 'selected' : '';
                            $url = generatePaginationUrl(1) . '&per_page=' . $option;
                            echo "<option value='$url' $selected>$option</option>";
                        }
                        ?>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-open TMDB search modal if there are search results
    <?php if (!empty($tmdb_results['results'])): ?>
    var tmdbModal = new bootstrap.Modal(document.getElementById('tmdbSearchModal'));
    tmdbModal.show();
    <?php endif; ?>
});
</script>

<!-- Add Movie Form -->
<div class="modal fade" id="addMovieModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">নতুন মুভি যোগ করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="POST" id="addMovieForm">
                    <!-- Existing fields -->
                    
                    <!-- Servers Section -->
                    <div class="mb-3">
                        <label class="form-label">সার্ভার</label>
                        <div id="servers-container">
                            <div class="server-entry mb-2">
                                <div class="row">
                                    <div class="col">
                                        <input type="text" name="server_name[]" class="form-control" placeholder="সার্ভারের নাম">
                                    </div>
                                    <div class="col">
                                        <input type="text" name="server_url[]" class="form-control" placeholder="URL">
                                    </div>
                                    <div class="col">
                                        <select name="server_quality[]" class="form-control">
                                            <option value="1080p">1080p</option>
                                            <option value="720p">720p</option>
                                            <option value="480p">480p</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-secondary btn-sm mt-2" onclick="addServerField()">
                            + আরও সার্ভার
                        </button>
                    </div>
                    
                    <button type="submit" name="add_movie" class="btn btn-primary">সেভ করুন</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function addServerField() {
    const container = document.getElementById('servers-container');
    const newServer = document.createElement('div');
    newServer.className = 'server-entry mb-2';
    newServer.innerHTML = `
        <div class="row">
            <div class="col">
                <input type="text" name="server_name[]" class="form-control" placeholder="সার্ভারের নাম">
            </div>
            <div class="col">
                <input type="text" name="server_url[]" class="form-control" placeholder="URL">
            </div>
            <div class="col">
                <select name="server_quality[]" class="form-control">
                    <option value="1080p">1080p</option>
                    <option value="720p">720p</option>
                    <option value="480p">480p</option>
                </select>
            </div>
            <div class="col-auto">
                <button type="button" class="btn btn-danger btn-sm" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(newServer);
}
</script>

<?php require_once 'includes/footer.php'; ?>

<?php
// Add this PHP code at the top where other POST handlers are
if (isset($_POST['import_by_ids'])) {
    $tmdb_ids = array_filter(array_map('trim', explode("\n", $_POST['tmdb_ids'])));
    
    $imported = 0;
    $failed = 0;
    $skipped = 0;
    $messages = [];
    
    foreach ($tmdb_ids as $tmdb_id) {
        if (!is_numeric($tmdb_id)) {
            $messages[] = "Invalid TMDB ID: $tmdb_id";
            $failed++;
            continue;
        }
        
        // Check if movie already exists
        $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
        $stmt->execute([$tmdb_id]);
        if ($stmt->fetch()) {
            $messages[] = "Movie with TMDB ID $tmdb_id already exists";
            $skipped++;
            continue;
        }
        
        try {
            $db->beginTransaction();
            
            // Import movie
            $result = $tmdb->importMovie($db, $tmdb_id);
            if ($result['success']) {
                $movie_id = $result['movie_id'];
                
                // Add default servers
                $stmt = $db->prepare("
                    SELECT id, name 
                    FROM servers 
                    WHERE status = 'active' 
                    AND (content_type = 'movie' OR content_type = 'all')
                ");
                $stmt->execute();
                $servers = $stmt->fetchAll();
                
                foreach ($servers as $server) {
                    // Get server pattern and generate URL using ServerManager
                    $server_manager = new ServerManager($db);
                    $server_url = $server_manager->generateMovieUrl($server, $tmdb_id);
                    
                    // Insert server
                    $stmt = $db->prepare("
                        INSERT INTO content_servers (content_id, server_id, content_type, url) 
                        VALUES (?, ?, 'movie', ?)
                    ");
                    $stmt->execute([$movie_id, $server['id'], $server_url]);
                }
                
                $db->commit();
                $imported++;
                $messages[] = "Successfully imported: " . $movie_data['title'];
            } else {
                $db->rollBack();
                $messages[] = "Failed to import TMDB ID $tmdb_id: " . $result['message'];
                $failed++;
            }
        } catch (Exception $e) {
            $db->rollBack();
            $messages[] = "Error importing TMDB ID $tmdb_id: " . $e->getMessage();
            $failed++;
        }
    }
    
    // Set success/error messages
    if ($imported > 0) {
        $success = "Successfully imported $imported movies. ";
    }
    if ($skipped > 0) {
        $success .= "$skipped movies were already in database. ";
    }
    if ($failed > 0) {
        $error = "$failed imports failed. Check details below:<br>" . implode("<br>", $messages);
    }
}
?>

<!-- Add this JavaScript for better UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show import results in modal if there are messages
    <?php if (!empty($messages)): ?>
    var importModal = new bootstrap.Modal(document.getElementById('tmdbIdImportModal'));
    importModal.show();
    <?php endif; ?>
});
</script>

<script>
const DELETE_CONFIRMATION_MESSAGE = 'Are you sure you want to delete this movie?';

document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const movieCheckboxes = document.querySelectorAll('.movie-checkbox');
    const bulkDelete = document.getElementById('bulkDelete');
    const bulkActivate = document.getElementById('bulkActivate');
    const bulkDeactivate = document.getElementById('bulkDeactivate');

    // Select All functionality
    selectAll.addEventListener('change', function() {
        movieCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkButtons();
    });

    // Individual checkbox change
    movieCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });

    // Update bulk action buttons state
    function updateBulkButtons() {
        const checkedCount = document.querySelectorAll('.movie-checkbox:checked').length;
        bulkDelete.disabled = checkedCount === 0;
        bulkActivate.disabled = checkedCount === 0;
        bulkDeactivate.disabled = checkedCount === 0;
    }

    // Helper function for bulk actions
    async function performBulkAction(action, selectedIds) {
        const button = document.getElementById(`bulk${action.charAt(0).toUpperCase() + action.slice(1)}`);
        if (button) button.disabled = true;

        try {
            console.log('Sending bulk action request:', { action, ids: selectedIds });

            const response = await fetch('ajax/bulk_movie_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ action, ids: selectedIds })
            });

            // Log response details for debugging
            console.log('Response status:', response.status);
            console.log('Response headers:', Object.fromEntries([...response.headers]));

            // Get raw response text first
            const rawResponse = await response.text();
            console.log('Raw response:', rawResponse);

            // Try to parse as JSON
            let data;
            try {
                data = JSON.parse(rawResponse);
            } catch (e) {
                console.error('Failed to parse JSON response:', e);
                throw new Error(`Invalid JSON response from server (Status: ${response.status})`);
            }

            // Check for success
            if (!data.success) {
                throw new Error(data.message || 'Operation failed');
            }

            // Show success message
            alert(data.message);
            
            // Reload the page
            window.location.reload();

        } catch (error) {
            console.error('Bulk action error:', error);
            alert(`Error: ${error.message}`);
        } finally {
            if (button) button.disabled = false;
        }
    }

    // Set up bulk action handlers
    document.addEventListener('DOMContentLoaded', () => {
        const bulkActions = {
            'bulkDelete': 'delete',
            'bulkActivate': 'activate',
            'bulkDeactivate': 'deactivate'
        };

        Object.entries(bulkActions).forEach(([buttonId, action]) => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.addEventListener('click', () => {
                    const selectedIds = [...document.querySelectorAll('.movie-checkbox:checked')]
                        .map(cb => cb.value);

                    if (selectedIds.length === 0) {
                        alert('Please select at least one movie');
                        return;
                    }

                    if (confirm(`Are you sure you want to ${action} ${selectedIds.length} selected movie(s)?`)) {
                        performBulkAction(action, selectedIds);
                    }
                });
            }
        });
    });

    // Unified delete handler function
    function handleDelete(event, id = null) {
        if (!confirm(DELETE_CONFIRMATION_MESSAGE)) {
            event.preventDefault();
            return false;
        }
        
        if (id) {
            event.preventDefault();
            performBulkAction('delete', [id])
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message || 'An error occurred while deleting the movie');
                });
        }
    }

    // Handle delete buttons
    document.querySelectorAll('.delete-movie').forEach(button => {
        button.addEventListener('click', (e) => handleDelete(e, button.dataset.id));
    });

    // Handle delete forms
    document.querySelectorAll('.delete-movie-form').forEach(form => {
        form.addEventListener('submit', (e) => handleDelete(e));
    });
});

function deleteMovie(movieId) {
    if (!confirm('Are you sure you want to delete this movie?')) {
        return;
    }

    fetch('ajax/delete_movie.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ movie_id: movieId })
    })
    .then(response => response.text())
    .then(text => {
        console.log('Raw response:', text); // Debug log
        
        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert('Movie deleted successfully');
                window.location.reload();
            } else {
                alert(data.message || 'Failed to delete movie');
            }
        } catch (e) {
            console.error('JSON Parse Error:', e);
            console.error('Response Text:', text);
            alert('Error processing server response');
        }
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        alert('Error connecting to server');
    });
}
</script>
