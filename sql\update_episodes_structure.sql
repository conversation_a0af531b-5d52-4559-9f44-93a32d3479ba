-- First check if columns exist and add them if they don't
SET @dbname = DATABASE();
SET @tablename = "episodes";
SET @columnname = "season_number";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      TABLE_SCHEMA = @dbname
      AND TABLE_NAME = @tablename
      AND COLUMN_NAME = @columnname
  ) > 0,
  "SELECT 1",
  "ALTER TABLE episodes ADD COLUMN season_number INT NOT NULL AFTER series_id"
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Same for episode_number
SET @columnname = "episode_number";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      TABLE_SCHEMA = @dbname
      AND TABLE_NAME = @tablename
      AND COLUMN_NAME = @columnname
  ) > 0,
  "SELECT 1",
  "ALTER TABLE episodes ADD COLUMN episode_number INT NOT NULL AFTER season_number"
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Add indexes for better performance
ALTER TABLE episodes
ADD INDEX idx_series_season (series_id, season_number),
ADD INDEX idx_episode_number (episode_number);

-- Update any NULL values
UPDATE episodes SET season_number = 1 WHERE season_number IS NULL;
UPDATE episodes SET episode_number = 1 WHERE episode_number IS NULL;