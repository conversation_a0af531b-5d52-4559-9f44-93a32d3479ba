<?php
session_start();
require_once '../includes/db.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    die('Admin access required');
}

// Get all active movies
$movies = $db->query("SELECT id, tmdb_id FROM movies WHERE status = 'active'")->fetchAll();

// Get AutoEmbed server info
$stmt = $db->prepare("SELECT id, base_url FROM servers WHERE name = 'AutoEmbed' AND status = 'active'");
$stmt->execute();
$autoembed = $stmt->fetch();

if ($autoembed) {
    $added = 0;
    
    // Add AutoEmbed server for each movie
    foreach ($movies as $movie) {
        // Check if server already exists for this movie
        $stmt = $db->prepare("SELECT id FROM movie_servers WHERE movie_id = ? AND server_id = ?");
        $stmt->execute([$movie['id'], $autoembed['id']]);
        
        if (!$stmt->fetch()) {
            // Create URL using pattern
            $url = $autoembed['base_url'] . '/movie/' . $movie['tmdb_id'];
            
            // Add new server
            $stmt = $db->prepare("
                INSERT INTO movie_servers (movie_id, server_id, url, status)
                VALUES (?, ?, ?, 'active')
            ");
            $stmt->execute([$movie['id'], $autoembed['id'], $url]);
            $added++;
        }
    }
    
    echo "Added AutoEmbed server to $added movies";
} else {
    echo "AutoEmbed server not found or not active";
}
