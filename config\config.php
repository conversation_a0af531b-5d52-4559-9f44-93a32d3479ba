<?php
// First, check if a session is already active
if (session_status() === PHP_SESSION_NONE) {
    // Set all session configurations before starting the session
    ini_set('session.gc_probability', 1);
    ini_set('session.gc_divisor', 100);
    ini_set('session.gc_maxlifetime', 86400); // 24 hours
    ini_set('session.cookie_lifetime', 86400); // 24 hours
    ini_set('session.save_path', __DIR__ . '/../tmp/sessions');
    
    // Ensure session directory exists
    if (!file_exists(__DIR__ . '/../tmp/sessions')) {
        mkdir(__DIR__ . '/../tmp/sessions', 0777, true);
    }
}

// Get the current domain and project path
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https://" : "http://";
$domain = $_SERVER['HTTP_HOST'];
$project_path = dirname($_SERVER['PHP_SELF']);

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'tipsbdxy_099');
define('DB_PASS', '@mdsrabon13');
define('DB_NAME', 'tipsbdxy_099');

define('TMDB_API_KEY', '3d36f64b789ec5484c76838f0ba11daf');
define('SITE_URL', $protocol . $domain . $project_path); // আপনার প্রজেক্টের পাথ অনুযায়ী

