<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/auth.php';
require_once '../../includes/tmdb_handler.php';

$auth = new Auth($db);

// Only check admin status, not login status
if (!$auth->isAdmin()) {
    error_log('Admin authorization failed');
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

$data = json_decode(file_get_contents('php://input'), true);
$id = $data['id'] ?? null;
$type = $data['type'] ?? 'movie';

if (!$id) {
    die(json_encode(['success' => false, 'message' => 'ID is required']));
}

try {
    $tmdb = new TMDBHandler();
    
    if ($type == 'movie') {
        $result = importMovie($tmdb, $db, $id);
    } else {
        $result = importSeries($tmdb, $db, $id);
    }
    
    echo json_encode($result);
} catch (Exception $e) {
    error_log("Import error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

function importMovie($tmdb, $db, $id) {
    try {
        // Get movie details
        $movie_data = $tmdb->getMovieById($id);
        if (!$movie_data) {
            throw new Exception("Failed to fetch movie data");
        }

        // Check if movie already exists
        $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
        $stmt->execute([$id]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Movie already exists'];
        }

        $db->beginTransaction();

        try {
            // Generate slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $movie_data['title'])));
            
            // Insert movie
            $stmt = $db->prepare("
                INSERT INTO movies (
                    tmdb_id,
                    title,
                    original_title,
                    slug,
                    overview,
                    poster_path,
                    backdrop_path,
                    release_date,
                    runtime,
                    rating,
                    status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");
            
            $stmt->execute([
                $movie_data['id'],
                $movie_data['title'],
                $movie_data['original_title'] ?? $movie_data['title'],
                $slug,
                $movie_data['overview'],
                $movie_data['poster_path'] ?? null,
                $movie_data['backdrop_path'] ?? null,
                $movie_data['release_date'],
                $movie_data['runtime'] ?? 0,
                $movie_data['vote_average'] ?? 0
            ]);

            $movie_id = $db->lastInsertId();

            // Add default servers for movie
            $server_sql = "INSERT INTO movie_servers (movie_id, server_id, url) 
                           SELECT 
                               ?, 
                               s.id,
                               CONCAT(
                                   s.base_url,
                                   REPLACE(
                                       mp.movie_pattern,
                                       '{tmdb_id}', 
                                       ?
                                   )
                               ) as url
                           FROM servers s
                           JOIN server_patterns mp ON s.pattern_id = mp.id
                           WHERE s.status = 'active'";
            $stmt = $db->prepare($server_sql);
            $stmt->execute([$movie_id, $movie_data['id']]);

            $db->commit();
            return ['success' => true, 'message' => 'Movie imported successfully'];

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        throw new Exception("Error importing movie: " . $e->getMessage());
    }
}

function importSeries($tmdb, $db, $id) {
    try {
        // Get series details
        $series_data = $tmdb->getTVShowById($id);
        if (!$series_data) {
            throw new Exception("Failed to fetch series data");
        }

        // Check if series already exists
        $stmt = $db->prepare("SELECT id FROM series WHERE tmdb_id = ?");
        $stmt->execute([$id]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Series already exists'];
        }

        $db->beginTransaction();

        try {
            // Generate slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $series_data['name'])));
            
            // Insert series
            $stmt = $db->prepare("
                INSERT INTO series (
                    tmdb_id,
                    title,
                    original_title,
                    slug,
                    overview,
                    poster_path,
                    backdrop_path,
                    first_air_date,
                    status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");
            
            $stmt->execute([
                $series_data['id'],
                $series_data['name'],
                $series_data['original_name'] ?? $series_data['name'],
                $slug,
                $series_data['overview'],
                $series_data['poster_path'] ?? null,
                $series_data['backdrop_path'] ?? null,
                $series_data['first_air_date']
            ]);

            $series_id = $db->lastInsertId();

            // Filter out season 0 and sort seasons
            $valid_seasons = array_filter($series_data['seasons'], function($season) {
                return isset($season['season_number']) && $season['season_number'] > 0;
            });

            usort($valid_seasons, function($a, $b) {
                return $a['season_number'] - $b['season_number'];
            });

            foreach ($valid_seasons as $season) {
                // Get detailed season info
                $season_details = $tmdb->getTVSeasonById($series_data['id'], $season['season_number']);
                
                if (!$season_details || empty($season_details['episodes'])) {
                    continue;
                }

                // Insert season
                $stmt = $db->prepare("
                    INSERT INTO seasons (
                        series_id,
                        season_number,
                        name,
                        overview,
                        poster_path,
                        air_date
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $series_id,
                    $season['season_number'],
                    $season['name'],
                    $season['overview'] ?? null,
                    $season['poster_path'] ?? null,
                    $season['air_date'] ?? null
                ]);

                $season_id = $db->lastInsertId();

                // Filter and sort episodes
                $valid_episodes = array_filter($season_details['episodes'], function($episode) {
                    return isset($episode['episode_number']) && $episode['episode_number'] > 0;
                });

                usort($valid_episodes, function($a, $b) {
                    return $a['episode_number'] - $b['episode_number'];
                });

                foreach ($valid_episodes as $episode) {
                    // Insert episode
                    $stmt = $db->prepare("
                        INSERT INTO episodes (
                            series_id,
                            season_id,
                            season_number,
                            episode_number,
                            title,
                            overview,
                            still_path,
                            air_date,
                            status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')
                    ");

                    $stmt->execute([
                        $series_id,
                        $season_id,
                        $season['season_number'],
                        $episode['episode_number'],
                        $episode['name'],
                        $episode['overview'] ?? null,
                        $episode['still_path'] ?? null,
                        $episode['air_date'] ?? null
                    ]);

                    $episode_id = $db->lastInsertId();

                    // Add default servers for episode with server URL pattern
                    $stmt = $db->prepare("
                        INSERT INTO episode_servers (episode_id, server_id, url) 
                        SELECT 
                            ?, 
                            s.id,
                            CONCAT(
                                s.base_url,
                                REPLACE(
                                    REPLACE(
                                        REPLACE(
                                            sp.series_pattern,
                                            '{tmdb_id}', 
                                            ?
                                        ),
                                        '{season_number}',
                                        ?
                                    ),
                                    '{episode_number}',
                                    ?
                                )
                            ) as url
                        FROM servers s
                        JOIN server_patterns sp ON s.pattern_id = sp.id
                        WHERE s.status = 'active'
                    ");

                    $stmt->execute([
                        $episode_id,
                        $series_data['id'],
                        $season['season_number'],
                        $episode['episode_number']
                    ]);
                }
            }

            $db->commit();
            return ['success' => true, 'message' => 'Series imported successfully'];

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        throw new Exception("Error importing series: " . $e->getMessage());
    }
}
