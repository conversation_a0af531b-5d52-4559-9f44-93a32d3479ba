<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';

$auth = new Auth($db);

if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Get user details
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Get subscription details
$stmt = $db->prepare("
    SELECT us.*, s.name as plan_name, s.features 
    FROM user_subscriptions us 
    JOIN subscriptions s ON us.subscription_id = s.id 
    WHERE us.user_id = ? AND us.end_date > NOW() 
    ORDER BY us.end_date DESC LIMIT 1
");
$stmt->execute([$_SESSION['user_id']]);
$subscription = $stmt->fetch(PDO::FETCH_ASSOC);

// Get watch history
$stmt = $db->prepare("
    SELECT wh.*, m.title, m.poster_path, 'movie' as type
    FROM watch_history wh
    JOIN movies m ON wh.content_id = m.id
    WHERE wh.user_id = ? AND wh.content_type = 'movie'
    UNION
    SELECT wh.*, s.title, s.poster_path, 'series' as type
    FROM watch_history wh
    JOIN series s ON wh.content_id = s.id
    WHERE wh.user_id = ? AND wh.content_type = 'series'
    ORDER BY watched_at DESC
    LIMIT 10
");
$stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
$watch_history = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'My Profile';
require_once 'includes/header.php';
?>

<div class="container mt-5 pt-4">
    <div class="row">
        <!-- Profile Info Card -->
        <div class="col-md-4">
            <div class="card bg-dark mb-4">
                <div class="card-body text-center">
                    <div class="profile-avatar mb-3">
                        <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                    </div>
                    <h3 class="card-title"><?php echo htmlspecialchars($user['username']); ?></h3>
                    <p class="text-muted">Member since <?php echo date('M d, Y', strtotime($user['created_at'])); ?></p>
                    
                    <div class="profile-details mt-4">
                        <p><i class="fas fa-envelope me-2"></i> <?php echo htmlspecialchars($user['email']); ?></p>
                        <p><i class="fas fa-clock me-2"></i> Last login: <?php echo date('M d, Y H:i', strtotime($user['last_login'])); ?></p>
                    </div>
                    
                    <button class="btn btn-outline-danger mt-3" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </button>
                </div>
            </div>

            <!-- Subscription Status Card -->
            <div class="card bg-dark mb-4">
                <div class="card-body">
                    <h4 class="card-title"><i class="fas fa-crown me-2 text-warning"></i>Subscription Status</h4>
                    <?php if ($subscription): ?>
                        <div class="subscription-info">
                            <p class="text-success mb-2">
                                <i class="fas fa-check-circle me-2"></i>Active
                            </p>
                            <p><strong>Plan:</strong> <?php echo htmlspecialchars($subscription['plan_name']); ?></p>
                            <p><strong>Expires:</strong> <?php echo date('M d, Y', strtotime($subscription['end_date'])); ?></p>
                            <div class="mt-3">
                                <a href="subscription.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-sync-alt me-2"></i>Renew Plan
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="subscription-info">
                            <p class="text-danger mb-2">
                                <i class="fas fa-times-circle me-2"></i>No Active Subscription
                            </p>
                            <div class="mt-3">
                                <a href="subscription.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-shopping-cart me-2"></i>Get Subscription
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Watch History and Favorites -->
        <div class="col-md-8">
            <!-- Watch History Card -->
            <div class="card bg-dark mb-4">
                <div class="card-body">
                    <h4 class="card-title mb-4">
                        <i class="fas fa-history me-2"></i>Watch History
                    </h4>
                    <?php if ($watch_history): ?>
                        <div class="row row-cols-2 row-cols-md-4 g-3">
                            <?php foreach ($watch_history as $item): ?>
                                <div class="col">
                                    <div class="history-item">
                                        <a href="<?php echo $item['type'] === 'movie' ? 'movie.php?id=' : 'series.php?id='; ?><?php echo $item['content_id']; ?>" 
                                           class="text-decoration-none">
                                            <img src="<?php echo $item['poster_path']; ?>" 
                                                 class="img-fluid rounded" 
                                                 alt="<?php echo htmlspecialchars($item['title']); ?>">
                                            <div class="history-info p-2">
                                                <h6 class="text-truncate"><?php echo htmlspecialchars($item['title']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo date('M d, Y', strtotime($item['watched_at'])); ?>
                                                </small>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-4">
                            <a href="history.php" class="btn btn-outline-light btn-sm">
                                View Full History
                            </a>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No watch history yet</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Watchlist Card -->
            <div class="card bg-dark">
                <div class="card-body">
                    <h4 class="card-title mb-4">
                        <i class="fas fa-bookmark me-2"></i>My Watchlist
                    </h4>
                    <!-- Add watchlist content here -->
                    <p class="text-muted text-center">Coming soon...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header">
                <h5 class="modal-title">Edit Profile</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProfileForm" method="POST" action="update_profile.php">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="<?php echo htmlspecialchars($user['username']); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($user['email']); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="new_password" name="new_password">
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editProfileForm" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<style>
.profile-avatar {
    width: 100px;
    height: 100px;
    background: var(--netflix-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    margin: 0 auto;
}

.history-item {
    position: relative;
    transition: transform 0.2s;
}

.history-item:hover {
    transform: scale(1.05);
}

.history-info {
    background: rgba(0, 0, 0, 0.8);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    color: white;
}

.subscription-info {
    border-left: 4px solid var(--netflix-red);
    padding-left: 1rem;
}
</style>

<?php require_once 'includes/footer.php'; ?>
