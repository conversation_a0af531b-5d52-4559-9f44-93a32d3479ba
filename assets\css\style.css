/* Hero Section */
.hero-section {
    height: 80vh;
    background-size: cover;
    background-position: center;
    position: relative;
    margin-bottom: 2rem;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.8));
}

.hero-content {
    position: absolute;
    bottom: 10%;
    left: 5%;
    color: #fff;
    max-width: 50%;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero-buttons {
    margin-top: 2rem;
}

.hero-buttons a {
    display: inline-block;
    padding: 0.8rem 2rem;
    margin-right: 1rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
}

.play-btn {
    background: #fff;
    color: #000;
}

.info-btn {
    background: rgba(109, 109, 110, 0.7);
    color: #fff;
}

/* Content Sections */
.content-section {
    padding: 2rem 5%;
}

.content-section h2 {
    color: #fff;
    margin-bottom: 1.5rem;
}

.content-slider {
    display: flex;
    overflow-x: auto;
    gap: 1rem;
    padding: 0.5rem 0;
}

.content-slider::-webkit-scrollbar {
    display: none;
}

.content-card {
    flex: 0 0 auto;
    width: 200px;
    position: relative;
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: scale(1.05);
}

.content-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 4px;
}

.content-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    background: linear-gradient(to top, rgba(0,0,0,0.9), transparent);
    color: #fff;
}

.content-info h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.rating {
    color: #ffd700;
    font-size: 0.9rem;
}

/* Top 10 Section */
.top-10 .content-card {
    width: 250px;
}

.rank {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background: rgba(0,0,0,0.7);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Top 10 Styling */
.top-10-card {
    width: 300px !important;
    margin-right: 40px;
}

.rank-number {
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 140px;
    font-weight: bold;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    z-index: 2;
    -webkit-text-stroke: 2px #404040;
    font-family: 'Arial Black', sans-serif;
}

/* Hero Section Fix */
.hero-backdrop {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.hero-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(
        to bottom,
        rgba(20, 20, 20, 0.2) 0%,
        rgba(20, 20, 20, 0.8) 60%,
        rgba(20, 20, 20, 1) 100%
    );
    z-index: 2;
}

.hero-content {
    z-index: 3;
}

/* Responsive adjustments for Top 10 */
@media (max-width: 768px) {
    .hero-content {
        max-width: 90%;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .content-card {
        width: 150px;
    }
    
    .top-10 .content-card {
        width: 200px;
    }
    
    .top-10-card {
        width: 200px !important;
        margin-right: 20px;
    }
    
    .rank-number {
        font-size: 80px;
        left: -10px;
    }
}

/* Bengali Content Section */
.bengali-content {
    background: rgba(0,0,0,0.2);
    border-radius: 8px;
    margin-bottom: 2rem;
}

.bengali-content .section-title {
    font-family: 'SolaimanLipi', Arial, sans-serif;
    color: #fff;
    margin-bottom: 0;
}

.movie-card {
    transition: transform 0.2s;
    margin-bottom: 1rem;
}

.movie-card:hover {
    transform: translateY(-5px);
}

.poster-wrapper {
    position: relative;
    margin-bottom: 0.5rem;
}

.rating {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: #ffd700;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
}

.movie-title {
    font-size: 0.9rem;
    margin: 0.5rem 0;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.movie-year {
    font-size: 0.8rem;
    color: #aaa;
}

/* Continue Watching Section */
.continue-watching .content-card .progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255,255,255,0.2);
    border-radius: 0;
}

.continue-watching .content-card .progress-bar {
    background-color: #e50914;
}
