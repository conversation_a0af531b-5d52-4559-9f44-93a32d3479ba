<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/server_manager.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$episode_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$episode_id) {
    header('Location: series.php');
    exit;
}

// Get episode details with series info
$stmt = $db->prepare("
    SELECT e.*, s.title as series_title, s.tmdb_id as series_tmdb_id 
    FROM episodes e 
    JOIN series s ON e.series_id = s.id 
    WHERE e.id = ?
");
$stmt->execute([$episode_id]);
$episode = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$episode) {
    header('Location: series.php');
    exit;
}

// Get all servers
$servers = $db->query("SELECT * FROM servers WHERE status = 'active' ORDER BY priority ASC")->fetchAll(PDO::FETCH_ASSOC);

// Get episode servers
$stmt = $db->prepare("
    SELECT es.*, s.name as server_name 
    FROM episode_servers es 
    LEFT JOIN servers s ON es.server_id = s.id 
    WHERE es.episode_id = ?
");
$stmt->execute([$episode_id]);
$episode_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle episode update
if (isset($_POST['update_episode'])) {
    $title = $_POST['title'];
    $overview = $_POST['overview'];
    $air_date = $_POST['air_date'];
    $status = $_POST['status'];

    $sql = "UPDATE episodes SET title = ?, overview = ?, air_date = ?, status = ? WHERE id = ?";
    $stmt = $db->prepare($sql);
    if ($stmt->execute([$title, $overview, $air_date, $status, $episode_id])) {
        $success = "Episode updated successfully";
    } else {
        $error = "Failed to update episode";
    }
}

// Handle server addition
if (isset($_POST['add_server'])) {
    $server_id = $_POST['server_id'];
    
    // Get server details
    $stmt = $db->prepare("SELECT * FROM servers WHERE id = ?");
    $stmt->execute([$server_id]);
    $server = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($server) {
        $server_manager = new ServerManager($db);
        
        // Generate URL using pattern
        $url = $server_manager->generateTVUrl(
            $server, 
            $episode['series_tmdb_id'],
            $episode['season_number'],
            $episode['episode_number']
        );
        
        // Add server
        $stmt = $db->prepare("INSERT INTO episode_servers (episode_id, server_id, url, status) VALUES (?, ?, ?, 'active')");
        if ($stmt->execute([$episode_id, $server_id, $url])) {
            $success = "Server added successfully";
            // Refresh episode servers
            $stmt = $db->prepare("SELECT es.*, s.name as server_name FROM episode_servers es LEFT JOIN servers s ON es.server_id = s.id WHERE es.episode_id = ?");
            $stmt->execute([$episode_id]);
            $episode_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $error = "Failed to add server";
        }
    }
}

// Add auto-add servers functionality
if (empty($episode_servers)) {
    $server_manager = new ServerManager($db);
    
    // Debug: Let's see what servers are available
    $debug_servers = $db->query("SELECT * FROM servers WHERE status = 'active'")->fetchAll(PDO::FETCH_ASSOC);
    error_log('Available servers: ' . print_r($debug_servers, true));
    
    // Get Flicky and AutoEmbed servers
    $default_servers = $db->query("
        SELECT * FROM servers 
        WHERE name IN ('Flicky', 'AutoEmbed') 
        AND status = 'active'
        ORDER BY priority ASC
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    // Debug: Let's see what default servers were found
    error_log('Default servers found: ' . print_r($default_servers, true));
    
    foreach ($default_servers as $server) {
        // Generate URL using pattern
        $url = $server_manager->generateTVUrl(
            $server,
            $episode['series_tmdb_id'],
            $episode['season_number'],
            $episode['episode_number']
        );
        
        // Debug: Log the generated URL
        error_log("Generated URL for {$server['name']}: $url");
        
        // Check if this server already exists for this episode
        $exists = $db->prepare("
            SELECT COUNT(*) 
            FROM episode_servers 
            WHERE episode_id = ? AND server_id = ?
        ");
        $exists->execute([$episode_id, $server['id']]);
        $server_exists = $exists->fetchColumn() > 0;
        
        if (!$server_exists) {
            // Add server
            $stmt = $db->prepare("
                INSERT INTO episode_servers (episode_id, server_id, url, status) 
                VALUES (?, ?, ?, 'active')
            ");
            try {
                $result = $stmt->execute([$episode_id, $server['id'], $url]);
                error_log("Server {$server['name']} addition result: " . ($result ? 'success' : 'failed'));
            } catch (PDOException $e) {
                error_log("Error adding server {$server['name']}: " . $e->getMessage());
            }
        } else {
            error_log("Server {$server['name']} already exists for this episode");
        }
    }
    
    // Refresh episode servers list
    $stmt = $db->prepare("
        SELECT es.*, s.name as server_name 
        FROM episode_servers es 
        LEFT JOIN servers s ON es.server_id = s.id 
        WHERE es.episode_id = ?
    ");
    $stmt->execute([$episode_id]);
    $episode_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Debug: Final episode servers
    error_log('Final episode servers: ' . print_r($episode_servers, true));
}

$page_title = "Edit Episode: " . $episode['title'];
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row g-4">
        <div class="col-md-8">
            <!-- Episode Details Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">Edit Episode Details</h3>
                </div>
                <div class="card-body">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Series</label>
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($episode['series_title']); ?>" readonly>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Season</label>
                                <input type="number" class="form-control" value="<?php echo $episode['season_number']; ?>" readonly>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Episode</label>
                                <input type="number" class="form-control" value="<?php echo $episode['episode_number']; ?>" readonly>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select">
                                    <option value="active" <?php echo $episode['status'] == 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $episode['status'] == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Title</label>
                            <input type="text" name="title" class="form-control" value="<?php echo htmlspecialchars($episode['title']); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Overview</label>
                            <textarea name="overview" class="form-control" rows="4"><?php echo htmlspecialchars($episode['overview']); ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Air Date</label>
                            <input type="date" name="air_date" class="form-control" value="<?php echo $episode['air_date']; ?>">
                        </div>
                        <button type="submit" name="update_episode" class="btn btn-primary">Update Episode</button>
                    </form>
                </div>
            </div>

            <!-- Servers Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">Streaming Servers</h3>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServerModal">
                        Add Server
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Server</th>
                                    <th>URL</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($episode_servers as $server): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($server['server_name']); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <input type="text" class="form-control form-control-sm me-2" 
                                                   value="<?php echo htmlspecialchars($server['url']); ?>" readonly>
                                            <button class="btn btn-sm btn-secondary copy-url" 
                                                    data-url="<?php echo htmlspecialchars($server['url']); ?>">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $server['status'] == 'active' ? 'success' : 'danger'; ?>">
                                            <?php echo ucfirst($server['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-danger delete-server" data-id="<?php echo $server['id']; ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Episode Still -->
            <div class="card sticky-top" style="top: 1rem;">
                <img src="https://image.tmdb.org/t/p/w500<?php echo $episode['still_path']; ?>" 
                     class="card-img-top" alt="<?php echo htmlspecialchars($episode['title']); ?>">
            </div>
        </div>
    </div>
</div>

<!-- Add Server Modal -->
<div class="modal fade" id="addServerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Streaming Server</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Server</label>
                        <select name="server_id" class="form-select" required>
                            <option value="">Select a server</option>
                            <?php foreach ($servers as $server): ?>
                                <?php
                                // Check if this server is already added
                                $exists = false;
                                foreach ($episode_servers as $es) {
                                    if ($es['server_id'] == $server['id']) {
                                        $exists = true;
                                        break;
                                    }
                                }
                                if (!$exists):
                                ?>
                                    <option value="<?php echo $server['id']; ?>">
                                        <?php echo htmlspecialchars($server['name']); ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_server" class="btn btn-primary">Add Server</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Server addition form handling
    const addServerForm = document.querySelector('#addServerModal form');
    if (addServerForm) {
        addServerForm.addEventListener('submit', function(e) {
            const serverSelect = this.querySelector('select[name="server_id"]');
            if (!serverSelect.value) {
                e.preventDefault();
                alert('Please select a server');
            }
        });
    }
    
    // Copy URL functionality
    document.querySelectorAll('.copy-url').forEach(button => {
        button.addEventListener('click', function() {
            const url = this.dataset.url;
            navigator.clipboard.writeText(url).then(() => {
                // Show feedback
                const originalHTML = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                }, 1000);
            });
        });
    });

    // Delete server functionality
    document.querySelectorAll('.delete-server').forEach(button => {
        button.addEventListener('click', async function() {
            if (confirm('Are you sure you want to delete this server?')) {
                const serverId = this.dataset.id;
                try {
                    const response = await fetch('ajax/delete_episode_server.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ server_id: serverId })
                    });
                    const data = await response.json();
                    if (data.success) {
                        this.closest('tr').remove();
                    } else {
                        alert('Failed to delete server');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Failed to delete server');
                }
            }
        });
    });
});
</script>
