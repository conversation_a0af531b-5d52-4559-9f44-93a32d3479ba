// Refresh session every 5 minutes
setInterval(() => {
    fetch('debug_session.php')
        .then(response => response.json())
        .then(data => {
            console.log('Session status:', data);
            
            // Optional: Show warning if session is about to expire
            const timeRemaining = parseInt(data.session_data.session_expires_in.split(':')[0]);
            if (timeRemaining < 5) {
                alert('Session will expire soon!');
            }
        });
}, 300000); // 5 minutes