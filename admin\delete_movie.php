<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/auth_check.php';

if (!isset($_POST['movie_id']) || empty($_POST['movie_id'])) {
    $_SESSION['error'] = "Invalid movie ID";
    header('Location: movies.php');
    exit;
}

$movie_id = (int)$_POST['movie_id'];

try {
    $db->beginTransaction();

    // First delete from movie_servers
    $stmt = $db->prepare("DELETE FROM movie_servers WHERE movie_id = ?");
    $stmt->execute([$movie_id]);

    // Then delete from movies table
    $stmt = $db->prepare("DELETE FROM movies WHERE id = ?");
    $stmt->execute([$movie_id]);

    $db->commit();
    $_SESSION['success'] = "Movie deleted successfully";

} catch (Exception $e) {
    $db->rollBack();
    error_log("Error deleting movie: " . $e->getMessage());
    $_SESSION['error'] = "Failed to delete movie";
}

header('Location: movies.php');
exit;