<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$error = $success = '';

// Add new server
if (isset($_POST['add_server'])) {
    $name = trim($_POST['name']);
    $base_url = trim($_POST['base_url']);
    $pattern_id = (int)$_POST['pattern_id'];
    $type = $_POST['type'];
    $priority = (int)$_POST['priority'];
    
    if (empty($name) || empty($base_url) || empty($pattern_id)) {
        $error = "All fields are required";
    } else {
        try {
            $stmt = $db->prepare("INSERT INTO servers (name, base_url, pattern_id, type, priority, status) 
                                 VALUES (?, ?, ?, ?, ?, 'active')");
            $stmt->execute([$name, $base_url, $pattern_id, $type, $priority]);
            $success = "Server added successfully";
        } catch (PDOException $e) {
            $error = "Error adding server: " . $e->getMessage();
        }
    }
}

// Handle server edit
if (isset($_POST['edit_server'])) {
    $server_id = (int)$_POST['server_id'];
    $name = trim($_POST['name']);
    $base_url = trim($_POST['base_url']);
    $pattern_id = (int)$_POST['pattern_id'];
    $type = $_POST['type'];
    $priority = (int)$_POST['priority'];
    
    if (empty($name) || empty($base_url) || empty($pattern_id)) {
        $error = "All fields are required";
    } else {
        try {
            $stmt = $db->prepare("UPDATE servers SET 
                name = ?, 
                base_url = ?, 
                pattern_id = ?, 
                type = ?, 
                priority = ? 
                WHERE id = ?");
            $stmt->execute([$name, $base_url, $pattern_id, $type, $priority, $server_id]);
            $success = "Server updated successfully";
        } catch (PDOException $e) {
            $error = "Error updating server: " . $e->getMessage();
        }
    }
}

// Get server details for edit modal
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $stmt = $db->prepare("SELECT * FROM servers WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_server = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Toggle server status
if (isset($_POST['toggle_status'])) {
    $server_id = (int)$_POST['server_id'];
    $new_status = $_POST['status'] === 'active' ? 'inactive' : 'active';
    
    try {
        $stmt = $db->prepare("UPDATE servers SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $server_id]);
        $success = "Server status updated";
    } catch (PDOException $e) {
        $error = "Error updating status: " . $e->getMessage();
    }
}

// Delete server
if (isset($_POST['delete_server'])) {
    $server_id = (int)$_POST['server_id'];
    
    try {
        $db->beginTransaction();
        
        // Delete server links from episode_servers
        $stmt = $db->prepare("DELETE FROM episode_servers WHERE server_id = ?");
        $stmt->execute([$server_id]);
        
        // Delete server
        $stmt = $db->prepare("DELETE FROM servers WHERE id = ?");
        $stmt->execute([$server_id]);
        
        $db->commit();
        $success = "Server deleted successfully";
    } catch (PDOException $e) {
        $db->rollBack();
        $error = "Error deleting server: " . $e->getMessage();
    }
}

// Update servers table display
$servers_query = "SELECT s.*, sp.name as pattern_name, 
                 sp.movie_pattern, sp.series_pattern 
                 FROM servers s 
                 LEFT JOIN server_patterns sp ON s.pattern_id = sp.id 
                 ORDER BY s.priority ASC, s.id DESC";
$servers = $db->query($servers_query)->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Manage Servers';
require_once 'includes/header.php';
?>

<div class="wrapper">
    <div class="content-wrapper">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>

                    <!-- Add Server Form -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">Add New Server</h5>
                            <form method="post">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Server Name</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   placeholder="e.g., VidStream" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="pattern_id" class="form-label">URL Pattern</label>
                                            <select class="form-control" id="pattern_id" name="pattern_id" required>
                                                <option value="">Select Pattern</option>
                                                <?php
                                                $patterns = $db->query("SELECT * FROM server_patterns ORDER BY name")->fetchAll();
                                                foreach ($patterns as $pattern) {
                                                    echo "<option value='{$pattern['id']}'>{$pattern['name']}</option>";
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="base_url" class="form-label">Base URL</label>
                                            <input type="url" class="form-control" id="base_url" name="base_url" 
                                                   placeholder="e.g., https://example.com" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="type" class="form-label">Server Type</label>
                                            <select class="form-control" id="type" name="type" required>
                                                <option value="both">Both Movies & Series</option>
                                                <option value="movie">Movies Only</option>
                                                <option value="tv">TV Series Only</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority</label>
                                            <input type="number" class="form-control" id="priority" name="priority" 
                                                   min="1" max="100" value="1" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" name="add_server" class="btn btn-primary w-100">
                                                <i class="fas fa-plus-circle"></i> Add Server
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> 
                                            <strong>URL Pattern Guide:</strong><br>
                                            • Movies: The pattern will use {tmdb_id} placeholder<br>
                                            • Series: The pattern will use {tmdb_id}, {season_number}, and {episode_number} placeholders
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Servers List -->
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Existing Servers</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Base URL</th>
                                            <th>Pattern</th>
                                            <th>Type</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($servers as $server): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($server['name']); ?></td>
                                            <td><?php echo htmlspecialchars($server['base_url']); ?></td>
                                            <td>
                                                <small>
                                                    <strong>Pattern:</strong> <?php echo htmlspecialchars($server['pattern_name']); ?><br>
                                                    <span class="text-muted">
                                                        Movie: <?php echo htmlspecialchars($server['movie_pattern']); ?><br>
                                                        Series: <?php echo htmlspecialchars($server['series_pattern']); ?>
                                                    </span>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $server['type'] === 'both' ? 'primary' : 
                                                    ($server['type'] === 'movie' ? 'success' : 'info'); ?>">
                                                    <?php echo ucfirst($server['type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo $server['priority']; ?></td>
                                            <td>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                                    <input type="hidden" name="status" value="<?php echo $server['status']; ?>">
                                                    <button type="submit" name="toggle_status" 
                                                            class="btn btn-sm btn-<?php echo $server['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                        <?php echo ucfirst($server['status']); ?>
                                                    </button>
                                                </form>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" 
                                                        data-bs-target="#editModal<?php echo $server['id']; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <form method="post" style="display: inline;" 
                                                      onsubmit="return confirm('Are you sure you want to delete this server?');">
                                                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                                    <button type="submit" name="delete_server" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Server Modals -->
<?php foreach ($servers as $server): ?>
<div class="modal fade" id="editModal<?php echo $server['id']; ?>" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Server: <?php echo htmlspecialchars($server['name']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name<?php echo $server['id']; ?>" class="form-label">Server Name</label>
                                <input type="text" class="form-control" 
                                       id="edit_name<?php echo $server['id']; ?>" 
                                       name="name" 
                                       value="<?php echo htmlspecialchars($server['name']); ?>" 
                                       required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_base_url<?php echo $server['id']; ?>" class="form-label">Base URL</label>
                                <input type="url" class="form-control" 
                                       id="edit_base_url<?php echo $server['id']; ?>" 
                                       name="base_url" 
                                       value="<?php echo htmlspecialchars($server['base_url']); ?>" 
                                       required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_pattern<?php echo $server['id']; ?>" class="form-label">URL Pattern</label>
                                <select class="form-control" 
                                        id="edit_pattern<?php echo $server['id']; ?>" 
                                        name="pattern_id" required>
                                    <?php foreach ($patterns as $pattern): ?>
                                        <option value="<?php echo $pattern['id']; ?>" 
                                            <?php echo $pattern['id'] == $server['pattern_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($pattern['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_type<?php echo $server['id']; ?>" class="form-label">Server Type</label>
                                <select class="form-control" 
                                        id="edit_type<?php echo $server['id']; ?>" 
                                        name="type" required>
                                    <option value="both" <?php echo $server['type'] == 'both' ? 'selected' : ''; ?>>
                                        Both Movies & Series
                                    </option>
                                    <option value="movie" <?php echo $server['type'] == 'movie' ? 'selected' : ''; ?>>
                                        Movies Only
                                    </option>
                                    <option value="tv" <?php echo $server['type'] == 'tv' ? 'selected' : ''; ?>>
                                        TV Series Only
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_priority<?php echo $server['id']; ?>" class="form-label">Priority</label>
                                <input type="number" class="form-control" 
                                       id="edit_priority<?php echo $server['id']; ?>" 
                                       name="priority" 
                                       value="<?php echo $server['priority']; ?>" 
                                       min="1" max="100" required>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        <strong>Current Pattern Details:</strong><br>
                        • Movie Pattern: <?php echo htmlspecialchars($server['movie_pattern']); ?><br>
                        • Series Pattern: <?php echo htmlspecialchars($server['series_pattern']); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="edit_server" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php require_once 'includes/footer.php'; ?>
