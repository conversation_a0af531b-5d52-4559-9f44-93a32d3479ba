<?php
require_once '../../includes/db.php';
require_once '../../includes/auth.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_id'])) {
    die("Unauthorized access");
}

try {
    // Get movies without servers
    $stmt = $db->prepare("
        SELECT m.id, m.tmdb_id, m.title 
        FROM movies m 
        LEFT JOIN movie_servers ms ON m.id = ms.movie_id 
        WHERE ms.id IS NULL AND m.status = 'active'
    ");
    $stmt->execute();
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($movies)) {
        die("No movies found without servers.");
    }

    // Get default servers
    $stmt = $db->prepare("
        SELECT id, name, pattern_id 
        FROM servers 
        WHERE status = 'active' 
        AND type = 'movie'
        ORDER BY priority ASC
    ");
    $stmt->execute();
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($servers)) {
        die("No active servers found.");
    }

    // Begin transaction
    $db->beginTransaction();

    // Prepare insert statement
    $insert_stmt = $db->prepare("
        INSERT INTO movie_servers (movie_id, server_id, status, created_at) 
        VALUES (?, ?, 'active', NOW())
    ");

    $success_count = 0;
    $errors = [];

    // Add servers to each movie
    foreach ($movies as $movie) {
        foreach ($servers as $server) {
            try {
                $insert_stmt->execute([$movie['id'], $server['id']]);
                $success_count++;
            } catch (PDOException $e) {
                $errors[] = "Error adding server '{$server['name']}' to movie '{$movie['title']}': " . $e->getMessage();
                continue;
            }
        }
    }

    // Commit transaction if successful
    if (empty($errors)) {
        $db->commit();
        echo "Success! Added servers to {$success_count} movies.\n";
    } else {
        $db->rollBack();
        echo "Errors occurred:\n";
        foreach ($errors as $error) {
            echo "- {$error}\n";
        }
    }

} catch (PDOException $e) {
    $db->rollBack();
    die("Database error: " . $e->getMessage());
}