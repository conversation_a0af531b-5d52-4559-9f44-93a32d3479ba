<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$tmdb = new TMDBHandler();
$error = $success = '';

// Handle bulk import by year
if (isset($_POST['bulk_import'])) {
    $year = $_POST['year'];
    $page = $_POST['page'] ?? 1;
    
    try {
        $response = $tmdb->bulkImportMovies($year, $page);
        if (isset($response['results'])) {
            $imported = 0;
            foreach ($response['results'] as $movie) {
                // Check if movie exists
                $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
                $stmt->execute([$movie['id']]);
                if (!$stmt->fetch()) {
                    // Import movie
                    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $movie['title'])));
                    
                    $sql = "INSERT INTO movies (
                        tmdb_id, title, original_title, slug, overview, 
                        poster_path, backdrop_path, release_date, rating, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')";
                    
                    $stmt = $db->prepare($sql);
                    $stmt->execute([
                        $movie['id'],
                        $movie['title'],
                        $movie['original_title'] ?? $movie['title'],
                        $slug,
                        $movie['overview'],
                        $movie['poster_path'],
                        $movie['backdrop_path'],
                        $movie['release_date'],
                        $movie['vote_average']
                    ]);
                    
                    $movie_id = $db->lastInsertId();

                    // Add servers
                    require_once '../includes/server_manager.php';
                    $server_manager = new ServerManager($db);

                    // Try to add Flixhq server
                    if (!$server_manager->addMovieServer($movie_id, 1, $movie['id'])) {
                        error_log("Failed to add Flixhq server for movie ID: " . $movie_id);
                    }

                    // Try to add AutoEmbed server
                    if (!$server_manager->addMovieServer($movie_id, 2, $movie['id'])) {
                        error_log("Failed to add AutoEmbed server for movie ID: " . $movie_id);
                    }

                    $imported++;
                }
            }
            $success = "Successfully imported $imported movies from page $page";
        }
    } catch (Exception $e) {
        $error = "Import error: " . $e->getMessage();
    }
}

// Get current year
$current_year = date('Y');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TMDB Movie Importer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #1a1a1a; color: #fff; }
        .card { background-color: #2d2d2d; border: 1px solid #404040; }
        .form-control { background-color: #333; border-color: #404040; color: #fff; }
        .form-control:focus { background-color: #404040; color: #fff; }
        .btn-primary { background-color: #0d6efd; border-color: #0d6efd; }
        .alert { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">TMDB Movie Importer</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>

                        <form method="post" class="mb-4">
                            <div class="mb-3">
                                <label for="year" class="form-label">Select Year</label>
                                <select name="year" id="year" class="form-control" required>
                                    <?php for ($year = $current_year; $year >= 1900; $year--): ?>
                                        <option value="<?php echo $year; ?>"><?php echo $year; ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="page" class="form-label">Page Number</label>
                                <input type="number" name="page" id="page" class="form-control" value="1" min="1" max="1000" required>
                                <small class="text-muted">Each page contains 20 movies</small>
                            </div>

                            <button type="submit" name="bulk_import" class="btn btn-primary">
                                Import Movies
                            </button>
                        </form>

                        <div class="mt-4">
                            <h5>Instructions:</h5>
                            <ul>
                                <li>Select a year to import movies from that year</li>
                                <li>Enter a page number (1-1000)</li>
                                <li>Click "Import Movies" to start the import process</li>
                                <li>The importer will skip movies that already exist in the database</li>
                                <li>Each import will automatically add both Flixhq and AutoEmbed servers</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
