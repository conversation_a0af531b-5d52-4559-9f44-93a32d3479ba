<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/server_manager.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$server_manager = new ServerManager($db);

// Handle pattern addition
if (isset($_POST['add_pattern'])) {
    $name = trim($_POST['name']);
    $movie_pattern = trim($_POST['movie_pattern']);
    $series_pattern = trim($_POST['series_pattern']);
    
    if ($server_manager->addServerPattern($name, $movie_pattern, $series_pattern)) {
        $success = "Pattern added successfully";
    } else {
        $error = "Failed to add pattern";
    }
}

// Handle pattern deletion
if (isset($_POST['delete_pattern'])) {
    if ($server_manager->deletePattern($_POST['pattern_id'])) {
        $success = "Pattern deleted successfully";
    } else {
        $error = "Failed to delete pattern";
    }
}

// Get all patterns
$patterns = $server_manager->getAllPatterns();

$page_title = 'Manage Server Patterns';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Add New Pattern</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="form-group">
                            <label>Pattern Name</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>Movie Pattern</label>
                            <input type="text" name="movie_pattern" class="form-control" required>
                            <small class="form-text text-muted">Use {tmdb_id} as placeholder</small>
                        </div>
                        <div class="form-group">
                            <label>Series Pattern</label>
                            <input type="text" name="series_pattern" class="form-control" required>
                            <small class="form-text text-muted">Use {tmdb_id}, {season_number}, {episode_number} as placeholders</small>
                        </div>
                        <button type="submit" name="add_pattern" class="btn btn-primary">Add Pattern</button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Existing Patterns</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Movie Pattern</th>
                                    <th>Series Pattern</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($patterns as $pattern): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($pattern['name']); ?></td>
                                    <td><?php echo htmlspecialchars($pattern['movie_pattern']); ?></td>
                                    <td><?php echo htmlspecialchars($pattern['series_pattern']); ?></td>
                                    <td><?php echo $pattern['created_at']; ?></td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="pattern_id" value="<?php echo $pattern['id']; ?>">
                                            <button type="submit" name="delete_pattern" class="btn btn-danger btn-sm" 
                                                    onclick="return confirm('Are you sure?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>