<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/auth.php';

$auth = new Auth($db);

// Get active channels
$stmt = $db->prepare("SELECT * FROM live_channels WHERE status = 'active' ORDER BY sort_order, name");
$stmt->execute();
$channels = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get current channel
$current_channel = null;
if (isset($_GET['channel'])) {
    $stmt = $db->prepare("SELECT * FROM live_channels WHERE slug = ? AND status = 'active'");
    $stmt->execute([$_GET['channel']]);
    $current_channel = $stmt->fetch(PDO::FETCH_ASSOC);
}

$page_title = 'Live TV';
require_once 'includes/header.php';
?>

<div class="live-tv-page">
    <div class="container-fluid">
        <div class="row">
            <!-- Mobile Channel Toggle Button -->
            <div class="col-12 d-lg-none mb-3">
                <button class="btn btn-primary w-100" id="toggleChannels">
                    <i class="fas fa-tv me-2"></i>চ্যানেল লিস্ট
                </button>
            </div>

            <!-- Channels Sidebar -->
            <div class="col-lg-3 channels-sidebar-wrapper">
                <div class="channels-sidebar">
                    <div class="sidebar-header">
                        <h3 class="mb-3">লাইভ চ্যানেল</h3>
                        <div class="search-box mb-3">
                            <input type="text" id="channelSearch" class="form-control" 
                                   placeholder="চ্যানেল খুঁজুন...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="channel-list" id="channelList">
                        <?php foreach ($channels as $channel): ?>
                            <a href="?channel=<?php echo $channel['slug']; ?>" 
                               class="channel-item <?php echo ($current_channel && $current_channel['id'] === $channel['id']) ? 'active' : ''; ?>"
                               data-channel-name="<?php echo strtolower(htmlspecialchars($channel['name'])); ?>">
                                <img src="<?php echo htmlspecialchars($channel['logo']); ?>" 
                                     alt="<?php echo htmlspecialchars($channel['name']); ?>"
                                     class="channel-logo"
                                     loading="lazy">
                                <span class="channel-name"><?php echo htmlspecialchars($channel['name']); ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Player Section -->
            <div class="col-lg-9">
                <?php if ($current_channel): ?>
                    <div class="player-section">
                        <h2 class="channel-title mb-3"><?php echo htmlspecialchars($current_channel['name']); ?></h2>
                        <div class="player-wrapper">
                            <video id="video" controls crossorigin playsinline>
                                <source type="application/x-mpegURL" src="<?php echo htmlspecialchars($current_channel['stream_url']); ?>">
                            </video>
                        </div>
                        <?php if ($current_channel['description']): ?>
                            <div class="channel-description mt-3">
                                <?php echo nl2br(htmlspecialchars($current_channel['description'])); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <h3>দেখতে চাওয়া চ্যানেলটি সিলেক্ট করুন</h3>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add HLS.js -->
<script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Video Player initialization
    const video = document.getElementById('video');
    
    if (video) {
        // Add fullscreen event listener
        video.addEventListener('fullscreenchange', function() {
            if (document.fullscreenElement) {
                screen.orientation.lock('landscape')
                    .catch(function(error) {
                        // Silently handle error if orientation lock fails
                        console.log(error);
                    });
            }
        });
        
        if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = video.querySelector('source').src;
        }
        else if (Hls.isSupported()) {
            const hls = new Hls({
                debug: false,
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                maxMaxBufferLength: 600,
                maxBufferSize: 60 * 1000 * 1000, // 60MB
                maxBufferHole: 0.5,
                liveSyncDurationCount: 3,
                liveMaxLatencyDurationCount: 10,
                progressive: true,
                // Retry configuration
                manifestLoadingTimeOut: 10000,
                manifestLoadingMaxRetry: 3,
                manifestLoadingRetryDelay: 1000,
                levelLoadingTimeOut: 10000,
                levelLoadingMaxRetry: 3,
                levelLoadingRetryDelay: 1000,
                fragLoadingTimeOut: 20000,
                fragLoadingMaxRetry: 3,
                fragLoadingRetryDelay: 1000
            });
            
            hls.loadSource(video.querySelector('source').src);
            hls.attachMedia(video);
            
            hls.on(Hls.Events.ERROR, function(event, data) {
                if (data.fatal) {
                    switch(data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                            console.log("Network error, trying to recover...");
                            hls.startLoad();
                            break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                            console.log("Media error, trying to recover...");
                            hls.recoverMediaError();
                            break;
                        default:
                            console.log("Unrecoverable error");
                            // Try to reload the source
                            setTimeout(() => {
                                hls.loadSource(video.querySelector('source').src);
                                hls.startLoad();
                            }, 1000);
                            break;
                    }
                }
            });
        }
    }

    // Live Search Implementation
    const searchInput = document.getElementById('channelSearch');
    const channelList = document.getElementById('channelList');
    const channels = channelList.getElementsByClassName('channel-item');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        Array.from(channels).forEach(channel => {
            const channelName = channel.getAttribute('data-channel-name');
            if (channelName.includes(searchTerm)) {
                channel.style.display = 'flex';
            } else {
                channel.style.display = 'none';
            }
        });
    });

    // Mobile Channel Toggle
    const toggleBtn = document.getElementById('toggleChannels');
    const sidebarWrapper = document.querySelector('.channels-sidebar-wrapper');

    toggleBtn.addEventListener('click', function() {
        sidebarWrapper.classList.toggle('show-sidebar');
        this.classList.toggle('active');
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        if (window.innerWidth < 992) {
            const isClickInsideSidebar = sidebarWrapper.contains(event.target);
            const isClickOnToggleBtn = toggleBtn.contains(event.target);
            
            if (!isClickInsideSidebar && !isClickOnToggleBtn && sidebarWrapper.classList.contains('show-sidebar')) {
                sidebarWrapper.classList.remove('show-sidebar');
                toggleBtn.classList.remove('active');
            }
        }
    });
});
</script>

<style>
.live-tv-page {
    background: var(--netflix-black);
    min-height: 100vh;
    padding: 20px 0;
}

/* Sidebar Styles */
.channels-sidebar {
    background: rgba(0,0,0,0.3);
    padding: 20px;
    border-radius: 8px;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    flex-shrink: 0;
}

.search-box {
    position: relative;
}

.search-box input {
    padding-right: 35px;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
}

.search-box input::placeholder {
    color: rgba(255,255,255,0.5);
}

.search-box .search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255,255,255,0.5);
}

.channel-list {
    flex-grow: 1;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255,255,255,0.2) transparent;
}

.channel-list::-webkit-scrollbar {
    width: 6px;
}

.channel-list::-webkit-scrollbar-thumb {
    background-color: rgba(255,255,255,0.2);
    border-radius: 3px;
}

.channel-item {
    display: flex;
    align-items: center;
    padding: 10px;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.05);
}

.channel-item:hover,
.channel-item.active {
    background: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.channel-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: 15px;
    background: rgba(0,0,0,0.2);
    border-radius: 4px;
    padding: 3px;
}

.channel-name {
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Player Styles */
.player-wrapper {
    position: relative;
    padding-top: 56.25%;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

#video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.channel-title {
    color: #fff;
    font-size: 1.5rem;
}

.channel-description {
    color: rgba(255,255,255,0.8);
    font-size: 14px;
    line-height: 1.6;
    background: rgba(0,0,0,0.3);
    padding: 15px;
    border-radius: 8px;
}

/* Mobile Styles */
@media (max-width: 991px) {
    .channels-sidebar-wrapper {
        position: fixed;
        left: -100%;
        top: 0;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: all 0.3s ease;
        padding: 20px;
        background: var(--netflix-black);
    }

    .channels-sidebar-wrapper.show-sidebar {
        left: 0;
    }

    .channels-sidebar {
        height: calc(100vh - 40px);
    }

    #toggleChannels {
        background: var(--netflix-red);
        border: none;
        padding: 12px;
        font-size: 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    #toggleChannels.active {
        background: #c2000d;
    }

    .player-section {
        margin-top: 20px;
    }
}

/* Responsive Font Sizes */
@media (max-width: 576px) {
    .channel-title {
        font-size: 1.2rem;
    }

    .channel-name {
        font-size: 14px;
    }

    .channel-description {
        font-size: 13px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>

