<?php
// Prevent any output before intended JSON response
ob_start();

session_start();
require_once '../../includes/db.php';
require_once '../../includes/auth_check.php';

header('Content-Type: application/json');

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action']) || !isset($input['ids'])) {
        throw new Exception('Invalid input parameters');
    }

    $action = $input['action'];
    $ids = array_map('intval', (array)$input['ids']);

    if (empty($ids)) {
        throw new Exception('No valid IDs provided');
    }

    $db->beginTransaction();

    switch ($action) {
        case 'delete':
            // First delete related records
            $stmt = $db->prepare("DELETE FROM movie_servers WHERE movie_id IN (" . str_repeat('?,', count($ids) - 1) . "?)");
            $stmt->execute($ids);
            
            // Then delete the movies
            $stmt = $db->prepare("DELETE FROM movies WHERE id IN (" . str_repeat('?,', count($ids) - 1) . "?)");
            $stmt->execute($ids);
            
            $affectedRows = $stmt->rowCount();
            break;

        default:
            throw new Exception('Invalid action specified');
    }

    $db->commit();

    echo json_encode([
        'success' => true,
        'message' => "Successfully deleted $affectedRows movies",
        'affectedRows' => $affectedRows
    ]);

} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Ensure no additional output
while (ob_get_level()) {
    ob_end_clean();
}
