<?php
class CustomPlayer {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    public function getDirectSource($content_type, $content_id, $episode_id = null) {
        try {
            $sql = "SELECT video_url, quality 
                    FROM direct_sources 
                    WHERE content_type = ? 
                    AND content_id = ?";
            $params = [$content_type, $content_id];
            
            if ($episode_id) {
                $sql .= " AND episode_id = ?";
                $params[] = $episode_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            error_log($e->getMessage());
            return null;
        }
    }
    
    public function renderPlayer($video_url, $quality = '720p') {
        return <<<HTML
        <div class="video-player-container">
            <video id="customPlayer" class="video-js vjs-default-skin" controls preload="auto" width="100%" height="100%">
                <source src="{$video_url}" type="video/mp4" label="{$quality}">
                Your browser does not support the video tag.
            </video>
        </div>
        <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>
        <script>
            var player = videojs('customPlayer', {
                fluid: true,
                playbackRates: [0.5, 1, 1.5, 2],
                controlBar: {
                    children: [
                        'playToggle',
                        'volumePanel',
                        'currentTimeDisplay',
                        'timeDivider',
                        'durationDisplay',
                        'progressControl',
                        'playbackRateMenuButton',
                        'fullscreenToggle'
                    ]
                }
            });
        </script>
        HTML;
    }
}