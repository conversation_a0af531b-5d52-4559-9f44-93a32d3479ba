<?php
require_once '../includes/init.php';
require_once '../includes/server_manager.php';

// Check if user is admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$server_manager = new ServerManager($db);
$success = $error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && verify_csrf_token()) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $result = $server_manager->addServerPattern(
                    trim($_POST['server_name']),
                    trim($_POST['movie_pattern']),
                    trim($_POST['series_pattern'])
                );
                
                if ($result['success']) {
                    $success = "Server pattern added successfully!";
                    $_POST = array();
                } else {
                    $error = $result['message'];
                }
                break;

            case 'delete':
                if (isset($_POST['server_id'])) {
                    $result = $server_manager->deletePattern($_POST['server_id']);
                    if ($result) {
                        $success = "Server deleted successfully!";
                    } else {
                        $error = "Error deleting server";
                    }
                }
                break;

            case 'delete_selected':
                if (isset($_POST['selected_servers']) && is_array($_POST['selected_servers'])) {
                    $result = $server_manager->deleteMultiplePatterns($_POST['selected_servers']);
                    if ($result['success']) {
                        $success = "Selected servers deleted successfully!";
                    } else {
                        $error = $result['message'];
                    }
                }
                break;
        }
    }
}

// Generate new CSRF token
$csrf_token = generate_csrf_token();

// Fetch all server patterns
$servers = $server_manager->getAllPatterns();

$page_title = 'Manage Servers';
require_once 'includes/admin_header.php';
?>

<!DOCTYPE html>
<html>
<head>
    <title>Manage Servers</title>
    <!-- Add your CSS here -->
</head>
<body>
    <div class="container">
        <h2>Add New Server</h2>
        <form method="POST">
            <div class="form-group">
                <label>Server Name</label>
                <input type="text" name="name" class="form-control" required>
            </div>
            <div class="form-group">
                <label>Base URL</label>
                <input type="text" name="base_url" class="form-control" required>
            </div>
            <div class="form-group">
                <label>Pattern</label>
                <select name="pattern_id" class="form-control" required>
                    <?php foreach ($server_manager->getAllPatterns() as $pattern): ?>
                    <option value="<?php echo $pattern['id']; ?>">
                        <?php echo htmlspecialchars($pattern['name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="form-group">
                <label>Type</label>
                <select name="type" class="form-control" required>
                    <option value="movie">Movie</option>
                    <option value="tv">TV Show</option>
                </select>
            </div>
            <div class="form-group">
                <label>Priority</label>
                <input type="number" name="priority" class="form-control" value="1" required>
            </div>
            <button type="submit" name="add_server" class="btn btn-primary">Add Server</button>
        </form>

        <h2 class="mt-4">Movie Servers</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Base URL</th>
                    <th>Pattern</th>
                    <th>Priority</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($movie_servers as $server): ?>
                <tr>
                    <td><?php echo htmlspecialchars($server['name']); ?></td>
                    <td><?php echo htmlspecialchars($server['base_url']); ?></td>
                    <td><?php echo htmlspecialchars($server['url_pattern']); ?></td>
                    <td><?php echo $server['priority']; ?></td>
                    <td><?php echo $server['status']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <h2 class="mt-4">TV Servers</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Base URL</th>
                    <th>Pattern</th>
                    <th>Priority</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($tv_servers as $server): ?>
                <tr>
                    <td><?php echo htmlspecialchars($server['name']); ?></td>
                    <td><?php echo htmlspecialchars($server['base_url']); ?></td>
                    <td><?php echo htmlspecialchars($server['url_pattern']); ?></td>
                    <td><?php echo $server['priority']; ?></td>
                    <td><?php echo $server['status']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</body>
</html>
