<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$ticket_id = $_GET['id'] ?? 0;

// Get ticket details
$stmt = $db->prepare("
    SELECT 
        st.*,
        u.username,
        u.email
    FROM support_tickets st
    LEFT JOIN users u ON st.user_id = u.id
    WHERE st.id = ?
");
$stmt->execute([$ticket_id]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$ticket) {
    header('Location: support_tickets.php');
    exit;
}

// Get messages
$stmt = $db->prepare("
    SELECT 
        sm.*,
        CASE 
            WHEN sm.sender_id = ? THEN 'admin'
            ELSE 'user'
        END as sender_type
    FROM support_messages sm
    WHERE sm.ticket_id = ?
    ORDER BY sm.created_at ASC
");
$stmt->execute([$_SESSION['admin_id'], $ticket_id]);
$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle new reply
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reply'])) {
    $message = trim($_POST['message']);
    
    // First verify admin exists in users table
    $stmt = $db->prepare("SELECT id FROM users WHERE id = ? AND role = 'admin'");
    $stmt->execute([$_SESSION['admin_id']]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        // Insert the message
        $stmt = $db->prepare("
            INSERT INTO support_messages (ticket_id, sender_id, message, is_admin)
            VALUES (?, ?, ?, TRUE)
        ");
        $stmt->execute([$ticket_id, $admin['id'], $message]);
        
        // Update ticket's updated_at timestamp
        $stmt = $db->prepare("
            UPDATE support_tickets 
            SET updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        $stmt->execute([$ticket_id]);
        
        header("Location: view_ticket.php?id=$ticket_id");
        exit;
    } else {
        // Handle error - admin not found
        $error = "Error: Admin account not found";
    }
}

$page_title = "View Ticket";
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>টিকেট #<?= $ticket['id'] ?> - <?= htmlspecialchars($ticket['subject']) ?></h6>
                        <span class="badge bg-<?= $ticket['status'] === 'open' ? 'success' : 'secondary' ?>">
                            <?= $ticket['status'] === 'open' ? 'চলমান' : 'সমাধান হয়েছে' ?>
                        </span>
                    </div>
                    <p class="text-sm mb-0">
                        ইউজার: <?= htmlspecialchars($ticket['username']) ?> (<?= $ticket['email'] ?>)
                    </p>
                </div>
                <div class="card-body">
                    <div class="messages-container" style="max-height: 500px; overflow-y: auto;">
                        <?php foreach ($messages as $message): ?>
                            <div class="message <?= $message['sender_type'] === 'admin' ? 'admin-message' : 'user-message' ?> mb-3">
                                <div class="message-content p-3 rounded" 
                                     style="background: <?= $message['sender_type'] === 'admin' ? '#e3f2fd' : '#f5f5f5' ?>;">
                                    <div class="message-header d-flex justify-content-between mb-2">
                                        <strong><?= $message['sender_type'] === 'admin' ? 'Admin' : $ticket['username'] ?></strong>
                                        <small class="text-muted">
                                            <?= date('d M Y H:i', strtotime($message['created_at'])) ?>
                                        </small>
                                    </div>
                                    <div class="message-text">
                                        <?= nl2br(htmlspecialchars($message['message'])) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if ($ticket['status'] === 'open'): ?>
                        <form method="POST" class="mt-4">
                            <div class="form-group">
                                <label>আপনার রি��্লাই</label>
                                <textarea name="message" class="form-control" rows="4" required></textarea>
                            </div>
                            <div class="mt-3">
                                <button type="submit" name="reply" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> রিপ্লাই পাঠান
                                </button>
                                <button type="button" onclick="closeTicket(<?= $ticket['id'] ?>)" 
                                        class="btn btn-success">
                                    <i class="fas fa-check"></i> টিকেট সমাধান করুন
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-message {
    margin-left: 20%;
}
.user-message {
    margin-right: 20%;
}
.message-content {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>

<script>
function closeTicket(ticketId) {
    if (confirm('আপনি কি নিশ্চিত যে এই টিকেটটি সমাধান করতে চান?')) {
        fetch('ajax/update_ticket.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ticket_id: ticketId,
                status: 'closed'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
