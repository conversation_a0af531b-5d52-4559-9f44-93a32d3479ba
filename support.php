<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Handle new ticket submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['new_ticket'])) {
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);
    
    $db->beginTransaction();
    try {
        // Create ticket
        $stmt = $db->prepare("INSERT INTO support_tickets (user_id, subject) VALUES (?, ?)");
        $stmt->execute([$_SESSION['user_id'], $subject]);
        $ticket_id = $db->lastInsertId();
        
        // Add first message
        $stmt = $db->prepare("INSERT INTO support_messages (ticket_id, sender_id, message) VALUES (?, ?, ?)");
        $stmt->execute([$ticket_id, $_SESSION['user_id'], $message]);
        
        $db->commit();
        header('Location: support.php?success=1');
        exit;
    } catch (Exception $e) {
        $db->rollBack();
        $error = "Error creating ticket";
    }
}

$page_title = 'Support';
require_once 'includes/header.php';

// Get user's tickets
$stmt = $db->prepare("SELECT * FROM support_tickets WHERE user_id = ? ORDER BY created_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<style>
    .support-container {
        background-color: var(--netflix-dark);
        color: #fff;
        min-height: 100vh;
        padding: 20px 0;
    }
    .support-card {
        background-color: rgba(0, 0, 0, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
    }
    .support-card .card-header {
        background-color: rgba(0, 0, 0, 0.4);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    .support-card .card-body {
        padding: 20px;
    }
    .form-control {
        background-color: rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    .form-control:focus {
        background-color: rgba(0, 0, 0, 0.6);
        border-color: var(--netflix-red);
        color: #fff;
        box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
    }
    .form-label {
        color: #fff;
        font-weight: 500;
    }
    .ticket-card {
        transition: transform 0.2s;
    }
    .ticket-card:hover {
        transform: translateY(-5px);
    }
    .btn-primary {
        background-color: var(--netflix-red);
        border: none;
        padding: 8px 20px;
    }
    .btn-primary:hover {
        background-color: #f40612;
    }
    .badge {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
    .page-title {
        color: #fff;
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 30px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
    .section-title {
        color: #fff;
        font-size: 1.8rem;
        margin-bottom: 20px;
        font-weight: 500;
    }
    .ticket-status {
        font-size: 0.85rem;
        margin-bottom: 15px;
    }
    .ticket-date {
        color: #aaa;
        font-size: 0.85rem;
    }
</style>

<div class="support-container">
    <div class="container">
        <h1 class="page-title">সাপোর্ট সেন্টার</h1>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                টিকেট সফলভাবে তৈরি হয়েছে!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- New Ticket Form -->
        <div class="support-card card mb-5">
            <div class="card-header">
                <h5 class="mb-0">নতুন টিকেট তৈরি করুন</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-4">
                        <label class="form-label">বিষয়</label>
                        <input type="text" name="subject" class="form-control" required 
                               placeholder="আপনার সমস্যার বিষয় লিখুন">
                    </div>
                    <div class="mb-4">
                        <label class="form-label">মেসেজ</label>
                        <textarea name="message" class="form-control" rows="4" required
                                  placeholder="বিস্তারিত বর্ণনা করুন..."></textarea>
                    </div>
                    <button type="submit" name="new_ticket" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>সাবমিট করুন
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Tickets List -->
        <h3 class="section-title">আপনার টিকেটসমূহ</h3>
        <div class="row">
            <?php foreach ($tickets as $ticket): ?>
                <div class="col-md-6 mb-4">
                    <div class="support-card ticket-card card">
                        <div class="card-body">
                            <h5 class="card-title"><?= htmlspecialchars($ticket['subject']) ?></h5>
                            <div class="ticket-status">
                                <span class="badge bg-<?= $ticket['status'] === 'open' ? 'success' : 'secondary' ?>">
                                    <?= $ticket['status'] === 'open' ? 'চলমান' : 'সমাধান হয়েছে' ?>
                                </span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="ticket.php?id=<?= $ticket['id'] ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i> বিস্তারিত দেখুন
                                </a>
                                <span class="ticket-date">
                                    <i class="far fa-clock me-1"></i>
                                    <?= date('d M Y', strtotime($ticket['created_at'])) ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <?php if (empty($tickets)): ?>
                <div class="col-12">
                    <div class="support-card card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-ticket-alt fa-3x mb-3" style="color: var(--netflix-red);"></i>
                            <h5>কোন টিকেট নেই</h5>
                            <p class="text-muted">আপনি এখনও কোন সাপোর্ট টিকেট তৈরি করেননি</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
