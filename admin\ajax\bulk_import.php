<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

header('Content-Type: application/json');

try {
    $year = $_POST['year'] ?? date('Y');
    $sort_by = $_POST['sort_by'] ?? 'popularity.desc';
    $genre = $_POST['genre'] ?? '';

    // Debug parameters
    error_log("Import Parameters: " . json_encode([
        'year' => $year,
        'sort_by' => $sort_by,
        'genre' => $genre
    ]));

    $tmdb = new TMDBHandler();
    $imported = 0;
    $errors = [];

    // Get movies from TMDB
    $params = [
        'primary_release_year' => $year,
        'sort_by' => $sort_by,
        'with_genres' => $genre,
        'page' => 1,
        'include_adult' => false,
        'language' => 'en-US'
    ];

    // Debug API parameters
    error_log("TMDB API Parameters: " . json_encode($params));

    $response = $tmdb->discoverMovies($params);
    
    // Debug response
    error_log('TMDB Response: ' . print_r($response, true));

    if (!isset($response['results']) || empty($response['results'])) {
        throw new Exception('No movies found from TMDB. Response: ' . json_encode($response));
    }

    foreach ($response['results'] as $movie) {
        try {
            // Debug movie data
            error_log('Processing movie: ' . print_r($movie, true));

            // Check if movie exists
            $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
            $stmt->execute([$movie['id']]);
            if ($stmt->fetch()) {
                $errors[] = "Movie already exists: {$movie['title']}";
                continue;
            }

            // Get full movie details
            $movie_details = $tmdb->getMovieById($movie['id']);
            if (!$movie_details) {
                $errors[] = "Failed to get details for movie: {$movie['title']}";
                continue;
            }
            
            // Generate slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $movie['title'])));
            
            // Insert movie
            $sql = "INSERT INTO movies (
                tmdb_id, 
                title, 
                original_title, 
                slug, 
                overview, 
                poster_path, 
                backdrop_path, 
                release_date, 
                rating, 
                status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')";
            
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([
                $movie['id'],
                $movie['title'],
                $movie['original_title'] ?? $movie['title'],
                $slug,
                $movie['overview'],
                $movie['poster_path'],
                $movie['backdrop_path'],
                $movie['release_date'],
                $movie['vote_average']
            ]);

            if (!$result) {
                throw new Exception("Failed to insert movie: " . implode(", ", $stmt->errorInfo()));
            }
            
            $movie_id = $db->lastInsertId();

            // Add servers
            require_once '../../includes/server_manager.php';
            $server_manager = new ServerManager($db);
            
            $server1_added = $server_manager->addMovieServer($movie_id, 1, $movie['id']); // Flixhq
            $server2_added = $server_manager->addMovieServer($movie_id, 2, $movie['id']); // AutoEmbed

            if (!$server1_added && !$server2_added) {
                $errors[] = "Failed to add servers for movie: {$movie['title']}";
            }

            $imported++;
            
        } catch (Exception $e) {
            $errors[] = "Error importing {$movie['title']}: " . $e->getMessage();
        }
    }

    echo json_encode([
        'success' => true,
        'imported' => $imported,
        'errors' => $errors,
        'message' => "Successfully imported $imported movies" . 
                    (count($errors) > 0 ? " with " . count($errors) . " errors" : ""),
        'debug' => [
            'params' => $params,
            'total_results' => isset($response['total_results']) ? $response['total_results'] : 0
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Import failed: ' . $e->getMessage()
    ]);
}
