-- Add AutoEmbed server for existing movies
INSERT INTO movie_servers (movie_id, server_id, url, status)
SELECT 
    m.id as movie_id,
    s.id as server_id,
    CONCAT(s.base_url, '/movie/', m.tmdb_id) as url,
    'active' as status
FROM movies m
CROSS JOIN servers s
WHERE s.name = 'AutoEmbed'
AND NOT EXISTS (
    SELECT 1 
    FROM movie_servers ms 
    WHERE ms.movie_id = m.id 
    AND ms.server_id = s.id
);