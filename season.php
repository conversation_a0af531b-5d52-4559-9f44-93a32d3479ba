<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/auth.php';
require_once 'includes/server_manager.php';

$auth = new Auth($db);

// Check login status
if (!$auth->isLoggedIn()) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    header('Location: login.php');
    exit;
}

function checkSeriesAccess($db, $user_id) {
    // If no user is logged in, return true (allow access)
    if (!$user_id) {
        return true;
    }
    
    $sql = "SELECT access_type FROM user_content_access 
            WHERE user_id = ? AND content_type = 'series' 
            AND access_type = 'blocked'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    return $stmt->rowCount() === 0;
}

// Get user_id safely
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Check access before showing content
if (!checkSeriesAccess($db, $user_id)) {
    ?>
    <div class="modal fade" id="seriesBlockedModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark">
                <div class="modal-header border-bottom border-danger">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-lock-alt text-danger me-2"></i>
                        <span class="text-danger">অ্যাক্সেস ব্লক করা হয়েছে!</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="blocked-animation mb-4">
                        <i class="fas fa-tv text-danger fa-3x mb-3"></i>
                        <div class="lock-icon">
                            <i class="fas fa-lock text-danger"></i>
                        </div>
                    </div>
                    
                    <div class="alert custom-alert">
                        <h4 class="alert-heading mb-3 text-white">দুঃখিত!</h4>
                        <p class="mb-2 text-light">আপনার অ্যাকাউন্টের সিরিজ দেখার অ্যাক্সেস সাময়িকভাবে বন্ধ করা হয়েছে।</p>
                        <p class="mb-0 text-light">কারণ জানতে অথবা অ্যাক্সেস পুনরায় চালু করতে অনুগ্রহ করে সাপোর্টে যোগাযোগ করুন।</p>
                    </div>

                    <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                        <a href="support.php" class="btn custom-btn-danger btn-lg px-4">
                            <i class="fas fa-headset me-2"></i>সাপোর্টে যোগাযোগ করুন
                        </a>
                        <a href="index.php" class="btn custom-btn-outline btn-lg px-4">
                            <i class="fas fa-home me-2"></i>হোম পেজে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    #seriesBlockedModal .modal-content {
        background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
        color: #fff;
        box-shadow: 0 0 30px rgba(229, 9, 20, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .custom-alert {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 10px;
        backdrop-filter: blur(10px);
    }

    .blocked-animation {
        position: relative;
        display: inline-block;
        animation: float 3s ease-in-out infinite;
    }

    .blocked-animation .lock-icon {
        position: absolute;
        bottom: -10px;
        right: -10px;
        background: #2d2d2d;
        border-radius: 50%;
        padding: 10px;
        border: 2px solid #dc3545;
        animation: pulse 2s infinite;
        box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
    }

    .custom-btn-danger {
        background: linear-gradient(145deg, #dc3545, #c82333);
        border: none;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .custom-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        background: linear-gradient(145deg, #c82333, #dc3545);
    }

    .custom-btn-outline {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: #fff;
        transition: all 0.3s ease;
    }

    .custom-btn-outline:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: #fff;
        transform: translateY(-2px);
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
    }

    @media (max-width: 576px) {
        .d-sm-flex {
            flex-direction: column;
        }
        
        .btn-lg {
            width: 100%;
            margin: 0.5rem 0;
        }
    }
    </style>
    <?php
    exit();
}

$show_id = isset($_GET['show_id']) ? (int)$_GET['show_id'] : 0;
$season_number = isset($_GET['season']) ? (int)$_GET['season'] : 1;
$episode_id = isset($_GET['episode']) ? (int)$_GET['episode'] : null;

// Get show details
$stmt = $db->prepare("
    SELECT s.* 
    FROM series s 
    WHERE s.id = ? AND s.status = 'active'
");
$stmt->execute([$show_id]);
$show = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$show) {
    header('Location: 404.php');
    exit;
}

// Get episodes for this season
$stmt = $db->prepare("
    SELECT * 
    FROM episodes 
    WHERE series_id = ? AND season_number = ?
    ORDER BY episode_number ASC
");
$stmt->execute([$show_id, $season_number]);
$episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// If no episode is selected, select the first one
if (!$episode_id && !empty($episodes)) {
    $episode_id = $episodes[0]['id'];
}

// Get current episode details
$current_episode = null;
foreach ($episodes as $episode) {
    if ($episode['id'] == $episode_id) {
        $current_episode = $episode;
        break;
    }
}

// Get servers for current episode
$server_manager = new ServerManager($db);
$servers = [];
if ($current_episode) {
    $servers = $server_manager->getEpisodeServers($current_episode['id']);
}

// Get current server
$current_server = isset($_GET['server']) ? (int)$_GET['server'] : null;
if (!$current_server && !empty($servers)) {
    $current_server = $servers[0]['server_id'];
}

// Get current server URL
$current_url = '';
if ($current_server && !empty($servers)) {
    foreach ($servers as $server) {
        if ($server['server_id'] == $current_server) {
            $current_url = $server['url'];
            break;
        }
    }
}

$page_title = $show['title'] . " - Season " . $season_number;
require_once 'includes/header.php';
?>

<div class="season-page">
    <!-- Player Section -->
    <div class="container-fluid py-4">
        <div class="player-section">
            <?php if ($current_episode): ?>
                <div class="current-episode-info mb-4">
                    <h1 class="text-light">
                        <?php echo htmlspecialchars($show['title']); ?> - 
                        Season <?php echo $season_number; ?> Episode <?php echo $current_episode['episode_number']; ?>
                    </h1>
                    <h3 class="text-light opacity-75"><?php echo htmlspecialchars($current_episode['title']); ?></h3>
                </div>

                <!-- Server Selection -->
                <?php if (!empty($servers)): ?>
                <div class="server-buttons mb-4">
                    <div class="d-flex align-items-center flex-wrap">
                        <span class="server-label me-3">Select Server:</span>
                        <div class="server-list">
                            <?php foreach ($servers as $server): ?>
                                <a href="?show_id=<?php echo $show_id; ?>&season=<?php echo $season_number; ?>&episode=<?php echo $episode_id; ?>&server=<?php echo $server['server_id']; ?>" 
                                   class="btn <?php echo ($server['server_id'] == $current_server) ? 'btn-primary' : 'btn-outline-primary'; ?> me-2 mb-2">
                                    <?php echo htmlspecialchars($server['server_name']); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Video Player -->
                <div class="player-wrapper">
                    <div id="player-container">
                        <iframe src="<?php echo htmlspecialchars($current_url); ?>"
                                id="episode-player"
                                allowfullscreen
                                webkitallowfullscreen
                                mozallowfullscreen
                                sandbox="allow-forms allow-scripts allow-same-origin allow-presentation"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen; orientation-lock"
                                class="episode-player">
                        </iframe>
                    </div>
                </div>

                <!-- Download Section - Placed directly under the player -->
                <div class="download-section mt-4 p-4 bg-dark rounded">
                    <div class="download-instructions mb-3">
                        <h5 class="text-white mb-3"><i class="fas fa-download me-2"></i>ডাউনলোড নির্দেশনা</h5>
                        <ol class="text-light mb-4">
                            <li>নিচের "ডাউনলোড করুন" বাটনে ক্লিক করুন</li>
                            <li>টেলিগ্রাম বটে /start কমান্ড দিন</li>
                            <li>সিরিজের নাম টাইপ করে সার্চ করুন</li>
                            <li>আপনার কাঙ্ক্ষিত সিজন এবং এপিসোড সিলেক্ট করে ডাউনলোড করুন</li>
                        </ol>
                    </div>
                    
                    <div class="text-center">
                        <a href="https://t.me/cinepix_premium_bot" 
                           class="btn btn-danger btn-lg download-btn" 
                           target="_blank">
                            <i class="fab fa-telegram me-2"></i>ডাউনলোড করুন
                        </a>
                    </div>
                </div>

                <style>
                .player-wrapper {
                    position: relative;
                    padding-top: 56.25%;
                    background: #000;
                    margin-top: 20px;
                }

                #player-container {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }

                .episode-player {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border: none;
                    border-radius: 8px;
                }
                </style>

                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const player = document.getElementById('episode-player');
                    const container = document.getElementById('player-container');

                    // Function to handle fullscreen changes
                    function onFullscreenChange() {
                        if (document.fullscreenElement) {
                            lockOrientation();
                        } else {
                            // Reset orientation and styles when exiting fullscreen
                            unlockOrientation();
                            if (player) {
                                player.style.transform = '';
                                player.style.width = '100%';
                                player.style.height = '100%';
                            }
                        }
                    }

                    // Function to lock orientation
                    async function lockOrientation() {
                        try {
                            await screen.orientation.lock('landscape');
                        } catch (err) {
                            console.log('Orientation lock failed:', err);
                        }
                    }

                    // Function to unlock orientation
                    async function unlockOrientation() {
                        try {
                            await screen.orientation.unlock();
                        } catch (err) {
                            console.log('Orientation unlock failed:', err);
                        }
                    }

                    // Function to enter fullscreen
                    async function enterFullscreen() {
                        try {
                            await screen.orientation.lock('landscape').catch(err => console.log(err));

                            if (container.requestFullscreen) {
                                await container.requestFullscreen();
                            } else if (container.webkitRequestFullscreen) {
                                await container.webkitRequestFullscreen();
                            } else if (container.msRequestFullscreen) {
                                await container.msRequestFullscreen();
                            } else if (container.mozRequestFullScreen) {
                                await container.mozRequestFullScreen();
                            }
                        } catch (err) {
                            console.log('Fullscreen failed:', err);
                        }
                    }

                    // Add event listeners
                    document.addEventListener('fullscreenchange', onFullscreenChange);
                    document.addEventListener('webkitfullscreenchange', onFullscreenChange);
                    document.addEventListener('mozfullscreenchange', onFullscreenChange);
                    document.addEventListener('MSFullscreenChange', onFullscreenChange);

                    // Double tap to fullscreen
                    let lastTap = 0;
                    container.addEventListener('touchend', function(e) {
                        const currentTime = new Date().getTime();
                        const tapLength = currentTime - lastTap;
                        if (tapLength < 500 && tapLength > 0) {
                            e.preventDefault();
                            enterFullscreen();
                        }
                        lastTap = currentTime;
                    });

                    // Single click/tap to fullscreen
                    container.addEventListener('click', function(e) {
                        enterFullscreen();
                    });

                    // Handle orientation change
                    window.addEventListener('orientationchange', function() {
                        if (document.fullscreenElement) {
                            lockOrientation();
                        } else {
                            unlockOrientation();
                        }
                    });
                });
                </script>

            <?php else: ?>
                <div class="alert alert-info">
                    Please select an episode to watch.
                </div>
            <?php endif; ?>

            <!-- Episode List Section -->
            <div class="episode-list-section mt-5">
                <div class="container-fluid">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="section-title">Episodes</h3>
                        <div class="dropdown">
                            <button class="btn btn-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                Season <?php echo $season_number; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-dark">
                                <?php
                                $stmt = $db->prepare("SELECT DISTINCT season_number FROM episodes WHERE series_id = ? ORDER BY season_number");
                                $stmt->execute([$show_id]);
                                while ($season = $stmt->fetch()) {
                                    echo '<li><a class="dropdown-item ' . ($season['season_number'] == $season_number ? 'active' : '') . '" 
                                        href="?show_id=' . $show_id . '&season=' . $season['season_number'] . '">Season ' . $season['season_number'] . '</a></li>';
                                }
                                ?>
                            </ul>
                        </div>
                    </div>

                    <div class="episode-grid">
                        <?php foreach ($episodes as $episode): ?>
                            <div class="episode-card <?php echo ($episode['id'] == $episode_id) ? 'active' : ''; ?>">
                                <a href="?show_id=<?php echo $show_id; ?>&season=<?php echo $season_number; ?>&episode=<?php echo $episode['id']; ?>" 
                                   class="episode-link">
                                    <div class="episode-thumbnail">
                                        <?php if ($episode['still_path']): ?>
                                            <img src="https://image.tmdb.org/t/p/w300<?php echo $episode['still_path']; ?>" 
                                                 alt="Episode <?php echo $episode['episode_number']; ?>"
                                                 loading="lazy">
                                        <?php else: ?>
                                            <div class="no-thumbnail">
                                                <i class="fas fa-film"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="episode-number">Ep <?php echo $episode['episode_number']; ?></div>
                                    </div>
                                    <div class="episode-info">
                                        <h4 class="episode-title">
                                            <?php if ($episode['id'] == $episode_id): ?>
                                                <i class="fas fa-play-circle playing-icon"></i>
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($episode['title']); ?>
                                        </h4>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.player-section {
    max-width: 1200px;
    margin: 0 auto;
}

.current-episode-info {
    padding: 0 15px;
}

.current-episode-info h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.current-episode-info h3 {
    font-size: 1.2rem;
}

.server-buttons {
    padding: 0 15px;
}

.server-label {
    color: #fff;
    font-weight: 500;
}

.player-wrapper {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin: 0 15px;
}

.episode-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .current-episode-info h1 {
        font-size: 1.4rem;
    }

    .current-episode-info h3 {
        font-size: 1rem;
    }

    .server-buttons {
        margin-bottom: 1rem;
    }

    .server-label {
        margin-bottom: 0.5rem;
        display: block;
        width: 100%;
    }

    .server-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .server-list .btn {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Dark theme enhancements */
.btn-outline-primary {
    color: #fff;
    border-color: var(--netflix-red);
}

.btn-outline-primary:hover {
    background-color: var(--netflix-red);
    border-color: var(--netflix-red);
}

.btn-primary {
    background-color: var(--netflix-red);
    border-color: var(--netflix-red);
}

.btn-primary:hover {
    background-color: #f40612;
    border-color: #f40612;
}

/* Episode List Styling */
.episode-list-section {
    background: var(--netflix-dark);
    padding: 30px 0;
    margin-top: 50px;
}

.section-title {
    color: #fff;
    font-size: 24px;
    font-weight: 600;
}

.episode-grid {
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    padding: 0 10px;
}

.episode-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.2s ease;
}

.episode-card:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
}

.episode-card.active {
    border: 1px solid var(--netflix-red);
}

.episode-link {
    text-decoration: none;
    color: #fff;
    display: block;
}

.episode-thumbnail {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    background: #000;
}

.episode-thumbnail img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2a2a2a;
}

.no-thumbnail i {
    font-size: 24px;
    color: #666;
}

.episode-number {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0, 0, 0, 0.8);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.episode-info {
    padding: 8px;
}

.episode-title {
    font-size: 13px;
    font-weight: 500;
    margin: 0;
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.playing-icon {
    color: var(--netflix-red);
    margin-right: 4px;
    font-size: 12px;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .episode-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 8px;
        padding: 0 8px;
    }

    .episode-title {
        font-size: 12px;
    }

    .episode-number {
        font-size: 11px;
        padding: 1px 4px;
    }
}

/* Small Mobile Screens */
@media (max-width: 375px) {
    .episode-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Dropdown Styling */
.dropdown-menu-dark {
    background: var(--netflix-black);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-item {
    color: #fff;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.dropdown-item.active {
    background: var(--netflix-red);
}

/* Mobile optimization for player section */
@media (max-width: 768px) {
    .player-section {
        padding: 0;      /* Remove padding */
        margin: 0;       /* Remove margin */
        border-radius: 0; /* Remove border radius */
        width: 100vw;    /* Full viewport width */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }

    .container-fluid {
        padding-left: 0;  /* Remove container padding */
        padding-right: 0;
    }

    #player-container,
    #episode-player {
        width: 100% !important;
        height: 100% !important;
    }

    .player-wrapper {
        width: 100%;
        margin: 0 !important;  /* Remove any margin */
        position: relative;
        padding-top: 56.25%;   /* 16:9 Aspect Ratio */
        border-radius: 0;      /* Remove border radius */
    }

    .player-wrapper iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    /* Adjust server buttons for mobile */
    .server-buttons {
        padding: 10px;
        margin-bottom: 0;
    }

    .server-list {
        display: flex;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 5px;
    }

    .server-btn {
        white-space: nowrap;
        margin-right: 8px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Plyr player
    if (typeof Plyr !== 'undefined') {
        const player = new Plyr('#player', {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'],
            settings: ['captions', 'quality', 'speed'],
            resetOnEnd: true,
            quality: { default: 720, options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240] }
        });
    }

    // Enhanced anti-redirect and ad-blocking protection
    const playerFrame = document.querySelector('.episode-player');
    if (playerFrame) {
        // Override window.open
        window.open = function() { return null; };
        
        // Block popups
        window.addEventListener('popup', function(e) {
            e.preventDefault();
            return false;
        }, true);

        // Intercept and block redirects
        let originalLocation = window.location.href;
        Object.defineProperty(window, 'location', {
            get: function() {
                return originalLocation;
            },
            set: function(value) {
                if (value.includes('ads') || value.includes('pop') || value.includes('redirect')) {
                    console.log('Blocked redirect attempt to:', value);
                    return originalLocation;
                }
                originalLocation = value;
                return value;
            }
        });

        // Frame protection
        playerFrame.addEventListener('load', function() {
            try {
                const frameDoc = playerFrame.contentDocument || playerFrame.contentWindow.document;
                
                // Remove unwanted elements
                const removeElements = (doc) => {
                    const unwantedSelectors = [
                        'iframe[src*="ads"]',
                        'iframe[src*="pop"]',
                        'div[id*="ads"]',
                        'div[class*="ads"]',
                        'script[src*="ads"]',
                        'script[src*="pop"]',
                        'a[href*="redirect"]',
                        'a[target="_blank"]'
                    ];
                    
                    unwantedSelectors.forEach(selector => {
                        doc.querySelectorAll(selector).forEach(el => el.remove());
                    });
                };

                // Initial cleanup
                removeElements(frameDoc);

                // Monitor for dynamic changes
                const observer = new MutationObserver((mutations) => {
                    removeElements(frameDoc);
                });

                observer.observe(frameDoc.body, {
                    childList: true,
                    subtree: true
                });

                // Override frame redirects
                frameDoc.addEventListener('click', function(e) {
                    if (e.target.tagName === 'A' && (
                        e.target.href.includes('ads') ||
                        e.target.href.includes('pop') ||
                        e.target.href.includes('redirect')
                    )) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                }, true);

                // Block frame scripts
                const originalCreateElement = frameDoc.createElement;
                frameDoc.createElement = function(tag) {
                    const element = originalCreateElement.call(document, tag);
                    if (tag.toLowerCase() === 'script') {
                        Object.defineProperty(element, 'src', {
                            set: function(value) {
                                if (value.includes('ads') || value.includes('pop') || value.includes('redirect')) {
                                    return;
                                }
                                element.setAttribute('src', value);
                            }
                        });
                    }
                    return element;
                };

            } catch (e) {
                console.log('Frame access restricted - cross-origin policy');
            }
        });

        // Prevent frame navigation
        playerFrame.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            return false;
        });
    }
});

// Disable form submission warning
window.onbeforeunload = null;

// Clean navigation
document.querySelectorAll('.episode-link').forEach(link => {
    link.addEventListener('click', function(e) {
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?> 
