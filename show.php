<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/server_manager.php';

$show_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get show details
$stmt = $db->prepare("
    SELECT s.* 
    FROM series s 
    WHERE s.id = ? AND s.status = 'active'
");
$stmt->execute([$show_id]);
$show = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$show) {
    header('Location: 404.php');
    exit;
}

// Get seasons with poster paths from database
$stmt = $db->prepare("
    SELECT 
        s.season_number,
        s.poster_path,
        COUNT(e.id) as episode_count
    FROM seasons s
    LEFT JOIN episodes e ON s.id = e.season_id
    WHERE s.series_id = ? 
    GROUP BY s.season_number
    ORDER BY s.season_number ASC
");
$stmt->execute([$show_id]);
$seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);

// If no seasons found in database, fetch from TMDB
if (empty($seasons)) {
    require_once 'includes/tmdb_handler.php';
    $tmdb = new TMDBHandler();
    
    try {
        $show_data = $tmdb->getTVShowById($show['tmdb_id']);
        if (isset($show_data['seasons'])) {
            $seasons = array_map(function($season) {
                return [
                    'season_number' => $season['season_number'],
                    'poster_path' => $season['poster_path'],
                    'episode_count' => $season['episode_count'] ?? 0
                ];
            }, $show_data['seasons']);
        }
    } catch (Exception $e) {
        error_log("TMDB API Error: " . $e->getMessage());
    }
}

$page_title = $show['title'];
require_once 'includes/header.php';
?>

<div class="show-details-page">
    <!-- Hero Section with Backdrop -->
    <div class="show-hero" style="background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.9)), url('https://image.tmdb.org/t/p/original<?php echo htmlspecialchars($show['backdrop_path']); ?>')">
        <div class="container-fluid py-5">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="poster-wrapper">
                        <img src="https://image.tmdb.org/t/p/w500<?php echo htmlspecialchars($show['poster_path']); ?>" 
                             class="show-poster" 
                             alt="<?php echo htmlspecialchars($show['title']); ?>">
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="show-info text-light">
                        <h1 class="show-title"><?php echo htmlspecialchars($show['title']); ?></h1>
                        <div class="show-meta">
                            <span class="first-air-date">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <?php echo date('Y', strtotime($show['first_air_date'])); ?>
                            </span>
                            <span class="status">
                                <i class="fas fa-broadcast-tower me-2"></i>
                                <?php echo htmlspecialchars($show['status']); ?>
                            </span>
                            <?php if (!empty($show['number_of_seasons'])): ?>
                                <span class="seasons-count">
                                    <i class="fas fa-film me-2"></i>
                                    <?php echo $show['number_of_seasons']; ?> Seasons
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="show-overview"><?php echo htmlspecialchars($show['overview']); ?></p>
                        <?php if (!empty($show['genres'])): ?>
                            <div class="genres mt-3">
                                <?php foreach (explode(',', $show['genres']) as $genre): ?>
                                    <span class="genre-badge"><?php echo trim($genre); ?></span>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Seasons Section -->
    <div class="seasons-section mt-4">
        <h3 class="mb-3">Seasons</h3>
        <div class="seasons-grid">
            <?php foreach ($seasons as $season): 
                $poster_path = $season['poster_path'] 
                    ? "https://image.tmdb.org/t/p/w342" . $season['poster_path']
                    : "https://image.tmdb.org/t/p/w342" . $show['poster_path'];
            ?>
                <div class="season-card">
                    <div class="season-poster">
                        <img src="<?php echo htmlspecialchars($poster_path); ?>" 
                             alt="Season <?php echo $season['season_number']; ?>"
                             loading="lazy">
                        <div class="season-number">
                            Season <?php echo $season['season_number']; ?>
                        </div>
                        <div class="season-overlay">
                            <a href="season.php?show_id=<?php echo $show_id; ?>&season=<?php echo $season['season_number']; ?>" 
                               class="watch-btn">
                                <i class="fas fa-play me-1"></i>Watch
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<style>
.show-details-page {
    background-color: #141414;
    min-height: 100vh;
}

.show-hero {
    background-size: cover;
    background-position: center;
    padding: 60px 0;
}

.poster-wrapper {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(0,0,0,0.6);
    transition: transform 0.3s ease;
}

.poster-wrapper:hover {
    transform: scale(1.02);
}

.show-poster {
    width: 100%;
    height: auto;
    border-radius: 12px;
}

.show-info {
    padding: 20px;
}

.show-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.show-meta {
    font-size: 1.1rem;
    margin-bottom: 25px;
}

.show-meta span {
    margin-right: 25px;
    opacity: 0.9;
}

.show-overview {
    font-size: 1.2rem;
    line-height: 1.7;
    opacity: 0.9;
    margin-bottom: 25px;
}

.genre-badge {
    display: inline-block;
    padding: 6px 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.seasons-section {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 30px;
    margin-top: -50px;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 600;
}

.season-card {
    background: linear-gradient(to bottom, #2d2d2d, #1a1a1a);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.season-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.season-poster {
    position: relative;
    overflow: hidden;
    padding-top: 150%; /* 2:3 aspect ratio */
}

.season-poster img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.season-card:hover .season-poster img {
    transform: scale(1.05);
}

.season-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.season-card:hover .season-overlay {
    opacity: 1;
}

.watch-btn {
    background: #e50914;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.season-card:hover .watch-btn {
    transform: translateY(0);
}

.watch-btn:hover {
    background: #f40612;
    color: white;
    transform: scale(1.05);
}

.season-info {
    padding: 15px;
    text-align: center;
    color: white;
}

.season-title {
    font-size: 1.2rem;
    margin-bottom: 8px;
    font-weight: 600;
    color: white;
}

.episode-count {
    color: #999;
    font-size: 0.9rem;
    display: block;
}

/* Responsive grid for seasons */
@media (max-width: 768px) {
    .seasons-section {
        margin-top: -30px;
        padding: 20px;
    }
    
    .col-md-3 {
        width: 50%; /* 2 cards per row on mobile */
        padding: 8px;
    }
    
    .season-title {
        font-size: 1rem;
    }
    
    .watch-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

/* Season grid layout update */
.seasons-section .row {
    margin: -8px;
}

.seasons-section .col-md-3 {
    padding: 8px;
}

/* Add season number badge */
.season-poster::before {
    content: "S" attr(data-season);
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(229, 9, 20, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 600;
    z-index: 1;
    font-size: 0.9rem;
}

/* Update poster size for mobile */
@media (max-width: 768px) {
    .poster-wrapper {
        max-width: 200px;
        margin: 0 auto 20px;
    }
    
    .show-title {
        font-size: 2rem;
    }
    
    .show-meta {
        font-size: 0.9rem;
    }
}

.season-card {
    margin-bottom: 15px;
    transition: transform 0.2s;
}

.season-poster {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    aspect-ratio: 2/3;
}

.season-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.season-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
}

.season-card:hover {
    transform: scale(1.03);
}

.season-card:hover .season-overlay {
    opacity: 1;
}

.watch-btn {
    background: var(--primary-color, #e50914);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9rem;
}

.watch-btn:hover {
    background: var(--primary-color-dark, #b2070f);
    color: white;
}

.seasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    padding: 15px;
}

.season-number {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px;
    text-align: center;
    font-size: 0.85rem;
}
</style>

<?php require_once 'includes/footer.php'; ?>
