<?php
class Auth {
    private $db;
    
    public function __construct($db) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->db = $db;
    }
    
    public function redirectIfNotLoggedIn() {
        if (!$this->isLoggedIn()) {
            $this->redirect('login.php');
            exit;
        }
    }
    
    public function redirectIfLoggedIn() {
        if ($this->isLoggedIn()) {
            $redirect_to = $_SESSION['redirect_after_login'] ?? 'index.php';
            unset($_SESSION['redirect_after_login']); // Clear stored URL
            header("Location: $redirect_to");
            exit;
        }
    }
    
    public function redirect($default = 'index.php') {
        if (headers_sent()) {
            echo "<script>window.location.href = '$default';</script>";
            exit;
        } else {
            header("Location: $default");
            exit;
        }
    }
    
    public function getCurrentUserId() {
        return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
    }
    
    public function register($username, $email, $password, $confirm_password) {
        // Validation
        $errors = [];
        
        // Username validation
        if (strlen($username) < 3 || strlen($username) > 50) {
            $errors[] = "Username must be between 3 and 50 characters";
        }
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = "Username can only contain letters, numbers and underscore";
        }

        // Email validation
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email format";
        }

        // Password validation
        if (strlen($password) < 6) {
            $errors[] = "Password must be at least 6 characters long";
        }
        if ($password !== $confirm_password) {
            $errors[] = "Passwords do not match";
        }

        // Check if username/email already exists
        $stmt = $this->db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        if ($stmt->fetch()) {
            $errors[] = "Username or email already exists";
        }

        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        try {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert user
            $stmt = $this->db->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, 'user')");
            $stmt->execute([$username, $email, $hashed_password]);

            return ['success' => true];
        } catch (PDOException $e) {
            return ['success' => false, 'errors' => ['Database error occurred']];
        }
    }

    public function login($email, $password, $remember = false) {
        try {
            // Check both username and email
            $stmt = $this->db->prepare("
                SELECT * FROM users 
                WHERE (username = ? OR email = ?) 
                AND status = 'active'
            ");
            $stmt->execute([$email, $email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Debug log
            error_log("Login attempt for user: " . print_r($user, true));

            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                if ($user['role'] === 'admin') {
                    $_SESSION['admin_id'] = $user['id'];
                    $_SESSION['is_admin'] = true;
                }

                // Set fingerprint
                $_SESSION['fingerprint'] = $this->generateFingerprint();
                $_SESSION['last_activity'] = time();

                // Update last login
                $this->updateLastLogin($user['id']);
                
                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    $expires = date('Y-m-d H:i:s', strtotime('+30 days'));
                    
                    $stmt = $this->db->prepare("INSERT INTO remember_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
                    $stmt->execute([$user['id'], $token, $expires]);
                    
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', isset($_SERVER['HTTPS']), true);
                }
                
                return true;
            }
            
            error_log("Login failed for email: $email");
            return false;
            
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }

    public function checkRememberToken() {
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            
            $stmt = $this->db->prepare("
                SELECT u.*, rt.token
                FROM remember_tokens rt
                JOIN users u ON rt.user_id = u.id
                WHERE rt.token = ? 
                AND rt.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                // Set session variables
                $_SESSION['user_id'] = $result['id'];
                $_SESSION['username'] = $result['username'];
                $_SESSION['role'] = $result['role'];
                $_SESSION['last_activity'] = time();
                $_SESSION['fingerprint'] = $this->generateFingerprint();
                
                if ($result['role'] === 'admin') {
                    $_SESSION['admin_id'] = $result['id'];
                    $_SESSION['is_admin'] = true;
                }
                
                // Refresh the remember token
                $this->refreshRememberToken($token);
                return true;
            }
        }
        return false;
    }

    public function isLoggedIn() {
        // Basic session check
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        // Fingerprint validation
        if (!isset($_SESSION['fingerprint']) || $_SESSION['fingerprint'] !== $this->generateFingerprint()) {
            $this->logout();
            return false;
        }

        // Session timeout check (30 minutes)
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
            $this->logout();
            return false;
        }

        // Update last activity time
        $_SESSION['last_activity'] = time();

        return true;
    }

    public function logout() {
        // Destroy session
        session_unset();
        session_destroy();
        
        // Delete session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
    }

    private function startSecureSession() {
        // Set secure session parameters
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', 1);
        
        // Set session lifetime
        ini_set('session.gc_maxlifetime', 3600); // 1 hour
        session_set_cookie_params(3600);
    }

    public function getDebugInfo() {
        return [
            'current_fingerprint' => $this->generateFingerprint(),
            'session_fingerprint' => $_SESSION['fingerprint'] ?? null,
            'user_id' => $_SESSION['user_id'] ?? null,
            'username' => $_SESSION['username'] ?? null,
            'role' => $_SESSION['role'] ?? null,
            'last_activity' => $_SESSION['last_activity'] ?? null,
            'is_logged_in' => $this->isLoggedIn(),
            'is_admin' => $this->isAdmin(),
            'session_status' => [
                'id' => session_id(),
                'status' => session_status(),
                'save_path' => session_save_path()
            ],
            'cookie_params' => session_get_cookie_params()
        ];
    }

    private function generateFingerprint() {
        return hash('sha256', 
            ($_SERVER['HTTP_USER_AGENT'] ?? '') .
            ($_SERVER['REMOTE_ADDR'] ?? '') .
            ($_SERVER['SERVER_NAME'] ?? '')
        );
    }

    private function logActivity($user_id, $activity_type, $content_id = null) {
        $stmt = $this->db->prepare("
            INSERT INTO user_activity (user_id, activity_type, content_id, ip_address) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $user_id,
            $activity_type,
            $content_id,
            $_SERVER['REMOTE_ADDR']
        ]);
    }

    public function isAdmin() {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }

        $stmt = $this->db->prepare("SELECT role FROM users WHERE id = ? AND status = 'active'");
        $stmt->execute([$_SESSION['admin_id']]);
        $user = $stmt->fetch();

        return $user && ($user['role'] === 'admin' || $user['role'] === 'super_admin');
    }

    public function adminLogin($email, $password) {
        $stmt = $this->db->prepare("SELECT id, password FROM users WHERE email = ? AND role = 'admin'");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['admin_id'] = $user['id'];
            $_SESSION['user_id'] = $user['id']; // Set both sessions
            return true;
        }
        return false;
    }

    public function refreshSession() {
        if (isset($_SESSION['user_id'])) {
            try {
                // Only update the session timestamp
                $_SESSION['last_activity'] = time();
                
                // Regenerate session ID periodically
                if (!isset($_SESSION['last_regeneration']) || 
                    (time() - $_SESSION['last_regeneration'] > 300)) {
                    session_regenerate_id(true);
                    $_SESSION['last_regeneration'] = time();
                }
            } catch (PDOException $e) {
                error_log("Session refresh error: " . $e->getMessage());
            }
        }
    }

    private function updateLastLogin($userId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE users 
                SET last_login = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$userId]);
        } catch (PDOException $e) {
            error_log("Error updating last login: " . $e->getMessage());
        }
    }

    private function refreshRememberToken($old_token) {
        try {
            $new_token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+30 days'));
            
            $stmt = $this->db->prepare("
                UPDATE remember_tokens 
                SET token = ?, expires_at = ? 
                WHERE token = ?
            ");
            $stmt->execute([$new_token, $expires, $old_token]);
            
            setcookie('remember_token', $new_token, time() + (30 * 24 * 60 * 60), '/', '', isset($_SERVER['HTTPS']), true);
        } catch (PDOException $e) {
            error_log("Error refreshing remember token: " . $e->getMessage());
        }
    }
}
