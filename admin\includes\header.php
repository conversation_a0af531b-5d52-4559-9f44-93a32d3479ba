<?php
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? 'Admin Dashboard'; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    
    <!-- Your custom admin CSS -->
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Admin sidebar/navigation -->
    <div class="admin-sidebar">
        <div class="admin-logo">
            <a href="index.php">
                <img src="../assets/images/admin-logo.png" alt="Admin Panel Logo">
            </a>
        </div>
        <nav class="admin-nav">
            <ul>
                <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                    <a href="index.php">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                </li>
                <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'movies.php' ? 'active' : ''; ?>">
                    <a href="movies.php">
                        <i class="fas fa-film"></i> Movies
                    </a>
                </li>
                <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'series.php' ? 'active' : ''; ?>">
                    <a href="series.php">
                        <i class="fas fa-tv"></i> TV Series
                    </a>
                </li>
                <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'servers.php' ? 'active' : ''; ?>">
                    <a href="servers.php">
                        <i class="fas fa-server"></i> Servers
                    </a>
                </li>
                <li class="<?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                    <a href="settings.php">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
                <li class="<?php echo ($current_page == 'import-playlist.php') ? 'active' : ''; ?>">
                    <a href="import-playlist.php">
                        <i class="fas fa-file-import"></i> Import M3U Playlist
                    </a>
                </li>
                <li>
                    <a href="logout.php" class="text-danger">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Main content area -->
    <div class="admin-main-content">
        <div class="container">
            <?php if (isset($page_title)): ?>
                <h1 class="page-title mb-4">
                    <?php echo $page_title; ?>
                </h1>
            <?php endif; ?>
            <div class="card">
                <div class="card-body">
                    <div class="card-text">
                        <!-- Content goes here -->
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
