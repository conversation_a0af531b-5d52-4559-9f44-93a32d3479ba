body {
    background-color: #141414;
    color: #fff;
}

.card {
    background-color: #181818;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    border: none;
    color: #fff;
}

.card-header {
    background-color: #232323;
    border-bottom: 1px solid #333;
    color: #fff;
}

.table {
    color: #fff;
}

.table td, .table th {
    padding: 1rem;
    vertical-align: middle;
    border-color: #333;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.navbar {
    background-color: #181818;
}

.navbar-light .navbar-nav .nav-link {
    color: #fff;
}

.navbar-light .navbar-brand {
    color: #e50914;
}

.modal-content {
    background-color: #181818;
    color: #fff;
}

.modal-header {
    border-bottom: 1px solid #333;
}

.modal-footer {
    border-top: 1px solid #333;
}

.form-control {
    background-color: #232323;
    border: 1px solid #333;
    color: #fff;
}

.form-control:focus {
    background-color: #232323;
    border-color: #e50914;
    color: #fff;
}

.dropdown-menu {
    background-color: #181818;
    border: 1px solid #333;
}

.dropdown-item {
    color: #fff;
}

.dropdown-item:hover {
    background-color: #232323;
    color: #fff;
}