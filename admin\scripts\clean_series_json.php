<?php
require_once '../../includes/tmdb_handler.php';

// Read the original JSON file
$series_json = file_get_contents('../../data/series.json');
$series_list = json_decode($series_json, true) ?? [];

$tmdb = new TMDBHandler();
$cleaned_series = [];
$movies = [];

echo "Starting cleanup process...\n";
echo "Total entries to process: " . count($series_list) . "\n";

foreach ($series_list as $item) {
    if (empty($item['tmdb'])) {
        echo "Skipping entry with no TMDB ID: {$item['title']}\n";
        continue;
    }

    $media_type = $tmdb->getMediaType($item['tmdb']);
    
    if ($media_type === 'tv') {
        $cleaned_series[] = $item;
        echo "Added TV show: {$item['title']}\n";
    } else {
        $movies[] = $item;
        echo "Found movie: {$item['title']}\n";
    }
    
    // Add delay to avoid rate limiting
    usleep(250000); // 0.25 second delay
}

// Save cleaned series data
file_put_contents('../../data/series_cleaned.json', json_encode($cleaned_series, JSON_PRETTY_PRINT));

// Save movies data separately (might be useful for movie import)
file_put_contents('../../data/movies_from_series.json', json_encode($movies, JSON_PRETTY_PRINT));

echo "\nCleanup completed!\n";
echo "TV Shows found: " . count($cleaned_series) . "\n";
echo "Movies found: " . count($movies) . "\n";
echo "Cleaned data saved to 'series_cleaned.json'\n";
echo "Movies saved to 'movies_from_series.json'\n";