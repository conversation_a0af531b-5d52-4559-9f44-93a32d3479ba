<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = "Manage Users";
require_once 'includes/header.php';

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $selected_users = $_POST['selected_users'] ?? [];
    $action = $_POST['bulk_action'];
    
    if (!empty($selected_users)) {
        try {
            switch ($action) {
                case 'activate_subscription':
                    $plan_id = $_POST['plan_id'];
                    $duration = $_POST['duration'];
                    foreach ($selected_users as $user_id) {
                        activateSubscription($db, $user_id, $plan_id, $duration);
                    }
                    $success = "Subscriptions activated successfully";
                    break;

                case 'reset_password':
                    foreach ($selected_users as $user_id) {
                        $new_password = generateRandomPassword();
                        resetUserPassword($db, $user_id, $new_password);
                        // You should implement email notification here
                    }
                    $success = "Passwords reset successfully";
                    break;

                case 'block_access':
                    $content_type = $_POST['content_type'];
                    foreach ($selected_users as $user_id) {
                        updateUserAccess($db, $user_id, $content_type, 'blocked');
                    }
                    $success = "Access restrictions updated";
                    break;

                case 'grant_access':
                    $content_type = $_POST['content_type'];
                    foreach ($selected_users as $user_id) {
                        updateUserAccess($db, $user_id, $content_type, 'granted');
                    }
                    $success = "Access granted successfully";
                    break;
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Helper Functions
function activateSubscription($db, $user_id, $plan_id, $duration) {
    $end_date = date('Y-m-d H:i:s', strtotime("+$duration days"));
    $sql = "INSERT INTO user_subscriptions (user_id, plan_id, start_date, end_date, status) 
            VALUES (?, ?, NOW(), ?, 'active')";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id, $plan_id, $end_date]);
}

function resetUserPassword($db, $user_id, $new_password) {
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    $sql = "UPDATE users SET password = ? WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$hashed_password, $user_id]);
}

function updateUserAccess($db, $user_id, $content_type, $access_type) {
    $sql = "INSERT INTO user_content_access (user_id, content_type, access_type) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE access_type = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id, $content_type, $access_type, $access_type]);
}

function generateRandomPassword($length = 10) {
    return substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $length);
}

// Fetch subscription plans
$plans_sql = "SELECT id, name, duration_days FROM subscriptions WHERE status = 'active'";
$plans = $db->query($plans_sql)->fetchAll(PDO::FETCH_ASSOC);

// Handle user status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_user'])) {
    $user_id = (int)$_POST['user_id'];
    $new_status = $_POST['status'];
    $valid_statuses = ['active', 'banned', 'pending'];

    if (in_array($new_status, $valid_statuses)) {
        try {
            $stmt = $db->prepare("UPDATE users SET status = ? WHERE id = ?");
            $stmt->execute([$new_status, $user_id]);
            $success = "User status updated successfully";
        } catch (PDOException $e) {
            $error = "Error updating user status: " . $e->getMessage();
        }
    }
}

// Handle user deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    $user_id = (int)$_POST['user_id'];
    try {
        $stmt = $db->prepare("DELETE FROM users WHERE id = ? AND role != 'admin'");
        $stmt->execute([$user_id]);
        $success = "User deleted successfully";
    } catch (PDOException $e) {
        $error = "Error deleting user: " . $e->getMessage();
    }
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$where = "WHERE role != 'admin'";
if ($search) {
    $where .= " AND (username LIKE :search OR email LIKE :search)";
}

// Get total users count
$count_sql = "SELECT COUNT(*) FROM users " . $where;
$count_stmt = $db->prepare($count_sql);
if ($search) {
    $count_stmt->bindValue(':search', "%$search%", PDO::PARAM_STR);
}
$count_stmt->execute();
$total_users = $count_stmt->fetchColumn();
$total_pages = ceil($total_users / $limit);

// Fetch users with subscription status
$sql = "
    SELECT 
        u.*,
        CASE 
            WHEN us.end_date > NOW() THEN 'Active'
            ELSE 'Inactive'
        END as subscription_status,
        us.end_date as subscription_end
    FROM users u
    LEFT JOIN user_subscriptions us ON u.id = us.user_id 
        AND us.end_date = (
            SELECT MAX(end_date) 
            FROM user_subscriptions 
            WHERE user_id = u.id
        )
    $where
    ORDER BY u.id DESC 
    LIMIT :limit OFFSET :offset
";

$stmt = $db->prepare($sql);
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
if ($search) {
    $stmt->bindValue(':search', "%$search%", PDO::PARAM_STR);
}
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h6>User Management</h6>
                        </div>
                        <div class="col-6">
                            <form class="float-end" method="GET">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search users..." name="search" value="<?php echo htmlspecialchars($search); ?>">
                                    <button class="btn btn-primary" type="submit">Search</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Bulk Actions Form -->
                    <form method="POST" id="bulkActionForm">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select name="bulk_action" class="form-select" id="bulkActionSelect">
                                    <option value="">Select Action</option>
                                    <option value="activate_subscription">Activate Subscription</option>
                                    <option value="reset_password">Reset Password</option>
                                    <option value="block_access">Block Content Access</option>
                                    <option value="grant_access">Grant Content Access</option>
                                </select>
                            </div>
                            
                            <!-- Dynamic action options -->
                            <div class="col-md-3 action-options" id="subscriptionOptions" style="display:none;">
                                <select name="plan_id" class="form-select">
                                    <?php foreach ($plans as $plan): ?>
                                        <option value="<?php echo $plan['id']; ?>">
                                            <?php echo htmlspecialchars($plan['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 action-options" id="contentOptions" style="display:none;">
                                <select name="content_type" class="form-select">
                                    <option value="movies">Movies</option>
                                    <option value="series">Series</option>
                                    <option value="tv">TV Shows</option>
                                    <option value="premium">Premium Content</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary" id="applyAction">Apply</button>
                            </div>
                        </div>

                        <?php if (isset($success)): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <div class="table-responsive">
                            <table class="table align-items-center mb-0">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Status</th>
                                        <th>Subscription</th>
                                        <th>Joined</th>
                                        <th>Access</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="selected_users[]" 
                                                       value="<?php echo $user['id']; ?>" 
                                                       class="form-check-input user-select">
                                            </td>
                                            <td><?php echo $user['id']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($user['username']); ?>
                                                <?php if ($user['role'] === 'moderator'): ?>
                                                    <span class="badge bg-info">Moderator</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <span class="badge badge-sm bg-<?php 
                                                    echo match($user['status']) {
                                                        'active' => 'success',
                                                        'pending' => 'warning',
                                                        'banned' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php echo ucfirst($user['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['subscription_status'] === 'Active'): ?>
                                                    <span class="badge bg-success">Active until <?php echo date('Y-m-d', strtotime($user['subscription_end'])); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <!-- Add content access indicators -->
                                                <?php
                                                $access_sql = "SELECT content_type, access_type FROM user_content_access WHERE user_id = ?";
                                                $access_stmt = $db->prepare($access_sql);
                                                $access_stmt->execute([$user['id']]);
                                                $access_rights = $access_stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                                                ?>
                                                <?php foreach ($access_rights as $type => $access): ?>
                                                    <?php if ($access === 'blocked'): ?>
                                                        <span class="badge bg-danger"><?php echo ucfirst($type); ?> Blocked</span>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#updateUser<?php echo $user['id']; ?>">
                                                    Update
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#deleteUser<?php echo $user['id']; ?>">
                                                    Delete
                                                </button>
                                            </td>
                                        </tr>

                                        <!-- Update Modal -->
                                        <div class="modal fade" id="updateUser<?php echo $user['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <form method="POST">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Update User Status</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <div class="mb-3">
                                                                <label class="form-label">Status</label>
                                                                <select name="status" class="form-select" required>
                                                                    <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                                                    <option value="pending" <?php echo $user['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                                    <option value="banned" <?php echo $user['status'] === 'banned' ? 'selected' : ''; ?>>Banned</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" name="update_user" class="btn btn-primary">Update</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteUser<?php echo $user['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <form method="POST">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Delete User</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <p>Are you sure you want to delete user "<?php echo htmlspecialchars($user['username']); ?>"?</p>
                                                            <p class="text-danger">This action cannot be undone!</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" name="delete_user" class="btn btn-danger">Delete</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add JavaScript for dynamic form handling -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const bulkActionSelect = document.getElementById('bulkActionSelect');
    const subscriptionOptions = document.getElementById('subscriptionOptions');
    const contentOptions = document.getElementById('contentOptions');
    const selectAll = document.getElementById('selectAll');
    const userCheckboxes = document.getElementsByClassName('user-select');

    bulkActionSelect.addEventListener('change', function() {
        subscriptionOptions.style.display = 'none';
        contentOptions.style.display = 'none';

        switch(this.value) {
            case 'activate_subscription':
                subscriptionOptions.style.display = 'block';
                break;
            case 'block_access':
            case 'grant_access':
                contentOptions.style.display = 'block';
                break;
        }
    });

    selectAll.addEventListener('change', function() {
        Array.from(userCheckboxes).forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
