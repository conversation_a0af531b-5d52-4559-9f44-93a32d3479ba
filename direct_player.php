<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: login.php');
    exit;
}

// Get content details
$content_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? $_GET['type'] : 'movie';
$server = isset($_GET['server']) ? $_GET['server'] : '';

// Get content info from database
if ($type === 'movie') {
    $stmt = $db->prepare("SELECT * FROM movies WHERE id = ?");
} else {
    $stmt = $db->prepare("SELECT * FROM series WHERE id = ?");
}
$stmt->execute([$content_id]);
$content = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$content) {
    die("Content not found");
}

// Get video sources
$sources_stmt = $db->prepare("SELECT * FROM direct_sources WHERE content_id = ? AND content_type = ?");
$sources_stmt->execute([$content_id, $type]);
$sources = $sources_stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle new source addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_source'])) {
    $quality = trim($_POST['quality']);
    $video_url = trim($_POST['video_url']);
    $subtitle_url = trim($_POST['subtitle_url'] ?? '');
    
    $insert = $db->prepare("INSERT INTO direct_sources (content_id, content_type, quality, video_url, subtitle_url) VALUES (?, ?, ?, ?, ?)");
    $insert->execute([$content_id, $type, $quality, $video_url, $subtitle_url]);
    
    header("Location: direct_player.php?id=$content_id&type=$type");
    exit;
}

$page_title = $content['title'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Direct Player</title>
    
    <!-- Plyr CSS -->
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #1a1a1a;
            color: #fff;
        }
        .player-wrapper {
            position: relative;
            max-width: 1280px;
            margin: 0 auto;
        }
        .quality-buttons {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .quality-btn {
            padding: 8px 16px;
            background: #2d2d2d;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .quality-btn.active {
            background: #007bff;
        }
        .form-control {
            background-color: #2d2d2d;
            border: 1px solid #444;
            color: #fff;
        }
        .form-control:focus {
            background-color: #2d2d2d;
            border-color: #007bff;
            color: #fff;
        }
        .card {
            background-color: #2d2d2d;
            border: 1px solid #444;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-8">
                <!-- Video Player -->
                <div class="player-wrapper">
                    <video id="player" playsinline controls>
                        <?php if ($sources): ?>
                            <?php foreach ($sources as $source): ?>
                                <source src="<?php echo htmlspecialchars($source['video_url']); ?>" type="video/mp4" size="<?php echo htmlspecialchars($source['quality']); ?>">
                            <?php endforeach; ?>
                            <?php foreach ($sources as $source): ?>
                                <?php if (!empty($source['subtitle_url'])): ?>
                                    <track kind="captions" label="Subtitle" src="<?php echo htmlspecialchars($source['subtitle_url']); ?>" default>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </video>
                </div>

                <!-- Quality Buttons -->
                <div class="quality-buttons">
                    <?php foreach ($sources as $source): ?>
                        <button class="quality-btn" data-quality="<?php echo htmlspecialchars($source['quality']); ?>">
                            <?php echo htmlspecialchars($source['quality']); ?>p
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Add New Source Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Add New Video Source</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="quality" class="form-label">Quality (e.g., 720, 1080)</label>
                                <input type="number" class="form-control" id="quality" name="quality" required>
                            </div>
                            <div class="mb-3">
                                <label for="video_url" class="form-label">Video URL (.mp4, .mkv)</label>
                                <input type="url" class="form-control" id="video_url" name="video_url" required>
                            </div>
                            <div class="mb-3">
                                <label for="subtitle_url" class="form-label">Subtitle URL (.srt, .vtt) - Optional</label>
                                <input type="url" class="form-control" id="subtitle_url" name="subtitle_url">
                            </div>
                            <button type="submit" name="add_source" class="btn btn-primary">Add Source</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.plyr.io/3.7.8/plyr.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Plyr
            const player = new Plyr('#player', {
                controls: [
                    'play-large', 'play', 'progress', 'current-time', 'duration',
                    'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
                ],
                settings: ['captions', 'quality', 'speed'],
                quality: {
                    default: <?php echo isset($sources[0]) ? $sources[0]['quality'] : 720; ?>,
                    options: [<?php echo implode(',', array_column($sources, 'quality')); ?>]
                }
            });

            // Quality button click handler
            document.querySelectorAll('.quality-btn').forEach(button => {
                button.addEventListener('click', () => {
                    const quality = button.dataset.quality;
                    player.quality = quality;
                    
                    // Update active button
                    document.querySelectorAll('.quality-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    button.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>