<?php
require_once __DIR__ . '/../includes/db.php';

function checkStreamStatus($url) {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HEADER => true,
        CURLOPT_NOBODY => false, // Changed to false to get body content
        CURLOPT_TIMEOUT => 15,   // Increased timeout
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 5,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    
    curl_close($ch);
    
    // Check for valid M3U8 content
    $isM3u8 = stripos($contentType, 'application/vnd.apple.mpegurl') !== false ||
              stripos($contentType, 'application/x-mpegurl') !== false ||
              (stripos($response, '#EXTM3U') !== false);
              
    return [
        'working' => ($httpCode >= 200 && $httpCode < 400) && $isM3u8,
        'http_code' => $httpCode,
        'content_type' => $contentType
    ];
}

try {
    $stmt = $db->query("SELECT id, name, stream_url FROM live_channels WHERE status = 'active'");
    $channels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $update_stmt = $db->prepare("UPDATE live_channels SET 
                                status = ?,
                                last_checked = NOW(),
                                last_check_status = ?,
                                check_attempts = CASE 
                                    WHEN last_check_status = 'not_working' AND ? = 'not_working' 
                                    THEN check_attempts + 1 
                                    ELSE 0 
                                END
                                WHERE id = ?");
    
    foreach ($channels as $channel) {
        echo "Checking channel: {$channel['name']}... ";
        
        $check_result = checkStreamStatus($channel['stream_url']);
        
        // Only mark as inactive if failed multiple times
        $current_status = $check_result['working'] ? 'active' : 'inactive';
        $check_status = $check_result['working'] ? 'working' : 'not_working';
        
        // Log the check result
        error_log(sprintf(
            "Channel Check: %s (ID: %d) - Status: %s, HTTP: %d, Content-Type: %s",
            $channel['name'],
            $channel['id'],
            $check_status,
            $check_result['http_code'],
            $check_result['content_type']
        ));
        
        $update_stmt->execute([$current_status, $check_status, $check_status, $channel['id']]);
        
        echo $check_result['working'] ? "OK\n" : "FAILED (HTTP: {$check_result['http_code']})\n";
    }
} catch (Exception $e) {
    error_log("Channel check error: " . $e->getMessage());
    echo "Error: " . $e->getMessage() . "\n";
}
