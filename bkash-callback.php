<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/bkash_config.php';
require_once 'includes/bkash_handler.php';

$bkash = new BkashHandler();

if (isset($_GET['paymentID']) && isset($_SESSION['payment_id']) && $_GET['paymentID'] === $_SESSION['payment_id']) {
    try {
        $response = $bkash->executePayment($_GET['paymentID']);
        
        if ($response['statusCode'] === '0000') {
            // Payment successful
            require_once 'includes/payment_handler.php';
            $payment = new PaymentHandler($db);
            
            $result = $payment->processBkashPayment(
                $_SESSION['user_id'],
                $_SESSION['plan_id'],
                $response['amount'],
                $response['trxID']
            );
            
            if ($result) {
                header('Location: checkout.php?success=1');
            } else {
                header('Location: checkout.php?error=payment_processing_failed');
            }
        } else {
            header('Location: checkout.php?error=payment_failed');
        }
    } catch (Exception $e) {
        header('Location: checkout.php?error=system_error');
    }
} else {
    header('Location: checkout.php?error=invalid_payment');
}

exit;