<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

// Get all manual movies
$stmt = $db->prepare("
    SELECT m.*, 
           (SELECT COUNT(*) FROM manual_movie_sources WHERE movie_id = m.id) as source_count 
    FROM manual_movies m 
    ORDER BY m.id DESC
");
$stmt->execute();
$movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <title>Manual Movies List</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-dark text-light">
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manual Movies</h1>
            <a href="add_manual_movie.php" class="btn btn-primary">Add New Movie</a>
        </div>
        
        <div class="table-responsive">
            <table class="table table-dark table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Release Date</th>
                        <th>Sources</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($movies as $movie): ?>
                        <tr>
                            <td><?php echo $movie['id']; ?></td>
                            <td><?php echo htmlspecialchars($movie['title']); ?></td>
                            <td><?php echo $movie['release_date']; ?></td>
                            <td><?php echo $movie['source_count']; ?></td>
                            <td><?php echo $movie['status']; ?></td>
                            <td>
                                <a href="edit_manual_movie.php?id=<?php echo $movie['id']; ?>" 
                                   class="btn btn-sm btn-primary">Edit</a>
                                <a href="../manual_player.php?id=<?php echo $movie['id']; ?>" 
                                   class="btn btn-sm btn-info" target="_blank">View</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>