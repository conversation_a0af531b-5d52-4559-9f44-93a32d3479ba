<?php
require_once 'includes/init.php';
require_once 'includes/auth.php';

$auth = new Auth($db);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);

    if ($auth->login($email, $password, $remember)) {
        // Set success message in session
        $_SESSION['login_success'] = true;
        
        // Check for redirect URL
        $redirect_to = $_SESSION['redirect_after_login'] ?? '/index.php';
        unset($_SESSION['redirect_after_login']); // Clear stored URL
        
        // Redirect to the stored URL or home
        header("Location: " . $redirect_to);
        exit();
    } else {
        // Set error message in session
        $_SESSION['login_error'] = "Invalid credentials";
        
        // Redirect back to login page
        header("Location: /login.php");
        exit();
    }
}

// Check if already logged in
if ($auth->isLoggedIn()) {
    header("Location: /index.php");
    exit();
}
?>

<?php require_once 'includes/header.php'; ?>

<style>
/* Add these new styles at the top */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a237e 0%, #0d47a1 100%);
    position: relative;
    overflow: hidden;
}

/* Add animated background elements */
.login-container::before,
.login-container::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(33, 150, 243, 0.1);
    animation: float 15s infinite linear;
}

.login-container::before {
    top: -150px;
    right: -150px;
}

.login-container::after {
    bottom: -150px;
    left: -150px;
}

@keyframes float {
    0% { transform: rotate(0deg) translate(0, 0) scale(1); }
    50% { transform: rotate(180deg) translate(50px, 50px) scale(1.2); }
    100% { transform: rotate(360deg) translate(0, 0) scale(1); }
}

.login-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.login-title {
    color: #fff;
    font-size: 2rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
    color: #fff;
}

.btn-login {
    background: linear-gradient(45deg, #2196f3, #1976d2);
    border: none;
    color: #fff;
    padding: 0.8rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.btn-login:hover {
    background: linear-gradient(45deg, #1976d2, #1565c0);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.input-group-text {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #90caf9;
}

.form-label {
    color: #e3f2fd;
    font-weight: 500;
}

/* Remove the social-login related styles */
</style>

<div class="login-container d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card p-4 p-md-5">
                    <h2 class="login-title text-center">Welcome Back</h2>

                    <?php if (!empty($error)): ?>
                        <div class="alert mb-4">
                            <p class="mb-0"><i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?></p>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="" class="needs-validation" novalidate>
                        <input type="hidden" name="redirect_url" value="<?php echo htmlspecialchars($redirect_url); ?>">
                        
                        <div class="mb-4">
                            <label for="email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input 
                                    type="email" 
                                    class="form-control border-start-0"
                                    id="email" 
                                    name="email" 
                                    value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                    required
                                    autocomplete="email"
                                    placeholder="Enter your email"
                                >
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text border-end-0">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input 
                                    type="password" 
                                    class="form-control border-start-0"
                                    id="password" 
                                    name="password" 
                                    required
                                    autocomplete="current-password"
                                    placeholder="Enter your password"
                                >
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-4 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                            <label class="form-check-label" for="remember">Keep me signed in</label>
                        </div>

                        <button type="submit" class="btn btn-login w-100 mb-4">
                            Sign In <i class="fas fa-arrow-right ms-2"></i>
                        </button>

                        <div class="login-links text-center">
                            <p class="mb-2">
                                <a href="forgot-password.php" class="text-light">
                                    <i class="fas fa-key me-1"></i> Forgot Password?
                                </a>
                            </p>
                            <p class="mb-0 text-light">
                                Don't have an account? 
                                <a href="register.php" class="text-primary fw-bold">Sign Up</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
