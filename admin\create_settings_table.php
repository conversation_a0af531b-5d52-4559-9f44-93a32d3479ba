<?php
require_once '../includes/db.php';

try {
    // Create settings table
    $db->exec("
        CREATE TABLE IF NOT EXISTS settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            site_name VARCHAR(100) NOT NULL DEFAULT 'CinePix32',
            site_description TEXT,
            site_keywords TEXT,
            tmdb_api_key VARCHAR(255),
            items_per_page INT DEFAULT 24,
            maintenance_mode TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // Insert default settings if not exists
    $stmt = $db->query("SELECT COUNT(*) FROM settings");
    if ($stmt->fetchColumn() == 0) {
        $db->exec("
            INSERT INTO settings (id, site_name, site_description, site_keywords, tmdb_api_key, items_per_page) 
            VALUES (
                1, 
                'CinePix32', 
                'Watch Movies and TV Shows Online', 
                'movies, tv shows, streaming, online movies',
                '3d36f64b789ec5484c76838f0ba11daf',
                24
            )
        ");
    }

    echo "Settings table created and initialized successfully!";
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}