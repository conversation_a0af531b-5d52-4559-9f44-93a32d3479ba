<?php
session_start();
ini_set('max_execution_time', 0);
ini_set('memory_limit', '256M');
set_time_limit(0);

require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    exit(json_encode(['error' => true, 'message' => 'Unauthorized']));
}

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

function sendProgress($message, $progress = null, $error = false) {
    $data = ['message' => $message];
    if ($progress !== null) $data['progress'] = $progress;
    if ($error) $data['error'] = true;
    echo json_encode($data) . "\n";
    ob_flush();
    flush();
}

try {
    $tmdb = new TMDBHandler();
    $movies_json = file_get_contents('../data/movie.json');
    $movies = json_decode($movies_json, true) ?? [];
    
    $total = count($movies);
    if ($total === 0) {
        sendProgress("No movies found to import", 100, true);
        exit;
    }

    // Process in batches of 5
    $batch_size = 5;
    $batches = array_chunk($movies, $batch_size);
    
    foreach ($batches as $batch_index => $batch) {
        foreach ($batch as $index => $movie) {
            $overall_index = ($batch_index * $batch_size) + $index;
            $progress = ($overall_index + 1) / $total * 100;
            
            try {
                // Check if movie already exists
                $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
                $stmt->execute([$movie['tmdb']]);
                if ($stmt->fetch()) {
                    sendProgress("Movie already exists: " . $movie['title'], $progress);
                    continue;
                }

                $result = importMovie($tmdb, $db, $movie);
                sendProgress($result, $progress);
                
            } catch (Exception $e) {
                sendProgress("Failed to import {$movie['title']}: {$e->getMessage()}", $progress, true);
            }
        }
        
        // Add delay between batches
        sleep(2);
    }

} catch (Exception $e) {
    sendProgress("Import process failed: " . $e->getMessage(), null, true);
}

function importMovie($tmdb, $db, $movie_data) {
    try {
        // Get detailed movie info from TMDB
        $tmdb_details = $tmdb->getMovieById($movie_data['tmdb']);
        
        if (!$tmdb_details) {
            return "Failed to fetch TMDB data for: " . $movie_data['title'];
        }

        // Check if movie already exists
        $stmt = $db->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
        $stmt->execute([$movie_data['tmdb']]);
        if ($stmt->fetch()) {
            return "Movie already exists: " . $movie_data['title'];
        }

        // Get release year
        $release_year = substr($tmdb_details['release_date'], 0, 4);
        
        // Generate slug with release year
        $base_slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $tmdb_details['title'])));
        $slug = $base_slug . '-' . $release_year;
        
        // Check if slug exists
        $stmt = $db->prepare("SELECT id FROM movies WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetch()) {
            // If slug exists, add a random number
            $slug = $base_slug . '-' . $release_year . '-' . rand(1, 999);
        }

        // Insert movie
        $sql = "INSERT INTO movies (
            tmdb_id, 
            title, 
            original_title, 
            slug, 
            overview, 
            poster_path, 
            backdrop_path, 
            release_date,
            runtime,
            rating,
            status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $tmdb_details['id'],
            $tmdb_details['title'],
            $tmdb_details['original_title'] ?? $tmdb_details['title'],
            $slug,
            $tmdb_details['overview'],
            $tmdb_details['poster_path'],
            $tmdb_details['backdrop_path'],
            $tmdb_details['release_date'],
            $tmdb_details['runtime'],
            $tmdb_details['vote_average']
        ]);

        $movie_id = $db->lastInsertId();

        // Add servers
        require_once '../includes/server_manager.php';
        $server_manager = new ServerManager($db);
        
        // Add Flixhq server (ID: 1)
        $server1_added = $server_manager->addMovieServer($movie_id, 1, $tmdb_details['id']);
        
        // Add AutoEmbed server (ID: 2)
        $server2_added = $server_manager->addMovieServer($movie_id, 2, $tmdb_details['id']);

        $servers_message = "";
        if ($server1_added) $servers_message .= "Flixhq, ";
        if ($server2_added) $servers_message .= "AutoEmbed, ";
        $servers_message = rtrim($servers_message, ", ");

        if (!empty($servers_message)) {
            return "Successfully imported: " . $movie_data['title'] . " (Added servers: " . $servers_message . ")";
        } else {
            return "Movie imported but no servers were added: " . $movie_data['title'];
        }

    } catch (Exception $e) {
        return "Error importing " . $movie_data['title'] . ": " . $e->getMessage();
    }
}
