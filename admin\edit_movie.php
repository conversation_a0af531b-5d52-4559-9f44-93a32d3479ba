<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);
require_once '../includes/db.php';
require_once '../includes/server_manager.php';
require_once '../includes/tmdb_handler.php';

// Add this code after session_start()
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['title'])) {
    try {
        $movie_id = isset($_POST['movie_id']) ? (int)$_POST['movie_id'] : 0;
        
        if (!$movie_id) {
            throw new Exception("Invalid movie ID");
        }

        // Generate slug if empty
        $slug = !empty($_POST['slug']) ? 
                trim($_POST['slug']) : 
                strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $_POST['title'])));
        
        // Remove any double hyphens and trim hyphens from start/end
        $slug = trim(preg_replace('/-+/', '-', $slug), '-');
        
        // Check if slug exists for other movies
        $check_stmt = $db->prepare("SELECT id FROM movies WHERE slug = ? AND id != ?");
        $check_stmt->execute([$slug, $movie_id]);
        if ($check_stmt->fetch()) {
            $slug = $slug . '-' . $movie_id;
        }

        // Debug information
        error_log("Updating movie ID: $movie_id");
        error_log("New slug: $slug");

        $stmt = $db->prepare("
            UPDATE movies 
            SET title = ?,
                slug = ?,
                overview = ?,
                poster_path = ?,
                backdrop_path = ?,
                release_date = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $result = $stmt->execute([
            $_POST['title'],
            $slug,
            $_POST['overview'],
            $_POST['poster_path'],
            $_POST['backdrop_path'],
            $_POST['release_date'],
            $movie_id
        ]);

        if ($result) {
            $_SESSION['success_message'] = "Movie updated successfully";
        } else {
            throw new Exception("Failed to update movie");
        }

        header("Location: " . $_SERVER['PHP_SELF'] . "?id=" . $movie_id);
        exit;
        
    } catch (Exception $e) {
        error_log("Error updating movie: " . $e->getMessage());
        $_SESSION['error_message'] = $e->getMessage();
    }
}

// Define current page before including header
$current_page = 'movies'; // or whatever page identifier you're using

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle server addition
if (isset($_POST['add_server'])) {
    try {
        // Debug information
        error_log("POST data: " . print_r($_POST, true));
        
        if (empty($_POST['movie_id']) || empty($_POST['server_id'])) {
            throw new Exception("Movie ID and Server ID are required");
        }

        $movie_id = (int)$_POST['movie_id'];
        $server_id = (int)$_POST['server_id'];
        
        // Debug information
        error_log("Processing server addition - Movie ID: $movie_id, Server ID: $server_id");

        // Get movie TMDB ID first
        $stmt = $db->prepare("SELECT tmdb_id FROM movies WHERE id = ?");
        $stmt->execute([$movie_id]);
        $movie = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$movie) {
            throw new Exception("Movie not found");
        }

        // Get server details
        $stmt = $db->prepare("SELECT * FROM servers WHERE id = ?");
        $stmt->execute([$server_id]);
        $server = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$server) {
            throw new Exception("Server not found");
        }

        // Check if server already exists
        $stmt = $db->prepare("SELECT id FROM movie_servers WHERE movie_id = ? AND server_id = ?");
        $stmt->execute([$movie_id, $server_id]);
        
        if ($stmt->fetch()) {
            throw new Exception("This server is already added to the movie");
        }

        // Get server pattern
        $stmt = $db->prepare("
            SELECT sp.* 
            FROM server_patterns sp 
            JOIN servers s ON s.pattern_id = sp.id 
            WHERE s.id = ?
        ");
        $stmt->execute([$server_id]);
        $pattern = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$pattern) {
            throw new Exception("Server pattern not found");
        }

        // Generate URL
        $url = rtrim($server['base_url'], '/') . '/' . ltrim($pattern['movie_pattern'], '/');
        $url = str_replace('{tmdb_id}', $movie['tmdb_id'], $url);

        // Insert new server
        $stmt = $db->prepare("
            INSERT INTO movie_servers (movie_id, server_id, url, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        
        if (!$stmt->execute([$movie_id, $server_id, $url])) {
            throw new Exception("Failed to insert server");
        }

        $_SESSION['success_message'] = "Server added successfully";
        
    } catch (Exception $e) {
        error_log("Error in add_server: " . $e->getMessage());
        $_SESSION['error_message'] = $e->getMessage();
    }

    // Redirect back
    header("Location: " . $_SERVER['PHP_SELF'] . "?id=" . $movie_id);
    exit;
}

// Add this code block for handling server updates
if (isset($_POST['edit_server'])) {
    try {
        if (empty($_POST['server_id']) || empty($_POST['movie_server_id'])) {
            throw new Exception("Server ID and Movie Server ID are required");
        }

        $movie_server_id = (int)$_POST['movie_server_id'];
        $server_id = (int)$_POST['server_id'];
        $movie_id = (int)$_POST['movie_id'];
        
        // Get movie TMDB ID
        $stmt = $db->prepare("SELECT tmdb_id FROM movies WHERE id = ?");
        $stmt->execute([$movie_id]);
        $movie = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$movie) {
            throw new Exception("Movie not found");
        }

        // Get server pattern
        $stmt = $db->prepare("
            SELECT sp.* 
            FROM server_patterns sp 
            JOIN servers s ON s.pattern_id = sp.id 
            WHERE s.id = ?
        ");
        $stmt->execute([$server_id]);
        $pattern = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get server details
        $stmt = $db->prepare("SELECT * FROM servers WHERE id = ?");
        $stmt->execute([$server_id]);
        $server = $stmt->fetch(PDO::FETCH_ASSOC);

        // Generate new URL
        $url = rtrim($server['base_url'], '/') . '/' . ltrim($pattern['movie_pattern'], '/');
        $url = str_replace('{tmdb_id}', $movie['tmdb_id'], $url);

        // Update server
        $stmt = $db->prepare("
            UPDATE movie_servers 
            SET server_id = ?, 
                url = ?,
                updated_at = NOW()
            WHERE id = ? AND movie_id = ?
        ");
        
        if (!$stmt->execute([$server_id, $url, $movie_server_id, $movie_id])) {
            throw new Exception("Failed to update server");
        }

        $_SESSION['success_message'] = "Server updated successfully";
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }

    header("Location: " . $_SERVER['PHP_SELF'] . "?id=" . $movie_id);
    exit;
}

// Add this code block for handling server deletion
if (isset($_POST['delete_server'])) {
    try {
        $movie_server_id = (int)$_POST['movie_server_id'];
        $movie_id = (int)$_POST['movie_id'];

        $stmt = $db->prepare("DELETE FROM movie_servers WHERE id = ? AND movie_id = ?");
        if (!$stmt->execute([$movie_server_id, $movie_id])) {
            throw new Exception("Failed to delete server");
        }

        $_SESSION['success_message'] = "Server deleted successfully";
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }

    header("Location: " . $_SERVER['PHP_SELF'] . "?id=" . $movie_id);
    exit;
}

// Get movie details
$movie_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$movie_id) {
    header('Location: movies.php');
    exit;
}

// Get movie details
$stmt = $db->prepare("SELECT * FROM movies WHERE id = ?");
$stmt->execute([$movie_id]);
$movie = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$movie) {
    header('Location: movies.php');
    exit;
}

// Get all active servers
$servers = $db->query("
    SELECT s.*, sp.movie_pattern 
    FROM servers s 
    LEFT JOIN server_patterns sp ON s.pattern_id = sp.id 
    WHERE s.status = 'active' 
    ORDER BY s.priority ASC
")->fetchAll(PDO::FETCH_ASSOC);

// Get existing movie servers
$stmt = $db->prepare("
    SELECT 
        ms.*,
        s.name as server_name,
        s.base_url,
        sp.movie_pattern
    FROM movie_servers ms 
    LEFT JOIN servers s ON ms.server_id = s.id 
    LEFT JOIN server_patterns sp ON s.pattern_id = sp.id
    WHERE ms.movie_id = ?
");
$stmt->execute([$movie_id]);
$movie_servers = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Edit Movie: ' . htmlspecialchars($movie['title']);
require_once 'includes/header.php';
?>

<!-- Display Messages -->
<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['success_message'];
        unset($_SESSION['success_message']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['error_message'];
        unset($_SESSION['error_message']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="main-content">
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Left Column - Movie Details -->
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Edit Movie Details</h5>
                            <a href="movies.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Movies
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="editMovieForm">
                            <input type="hidden" name="movie_id" value="<?php echo htmlspecialchars($movie['id']); ?>">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Title</label>
                                        <input type="text" name="title" class="form-control" 
                                               value="<?php echo htmlspecialchars($movie['title']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Slug</label>
                                        <input type="text" name="slug" class="form-control" 
                                               value="<?php echo htmlspecialchars($movie['slug']); ?>">
                                        <small class="text-muted">Leave empty to auto-generate from title</small>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Overview</label>
                                <textarea name="overview" class="form-control" rows="4"><?php echo htmlspecialchars($movie['overview']); ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Poster Path</label>
                                        <div class="input-group">
                                            <span class="input-group-text">tmdb.org/t/p/</span>
                                            <input type="text" name="poster_path" class="form-control" 
                                                   value="<?php echo htmlspecialchars($movie['poster_path']); ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Backdrop Path</label>
                                        <div class="input-group">
                                            <span class="input-group-text">tmdb.org/t/p/</span>
                                            <input type="text" name="backdrop_path" class="form-control" 
                                                   value="<?php echo htmlspecialchars($movie['backdrop_path']); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Release Date</label>
                                        <input type="date" name="release_date" class="form-control" 
                                               value="<?php echo $movie['release_date']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="active" <?php echo $movie['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $movie['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Server Management Section -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">Manage Servers</h5>
                    </div>
                    <div class="card-body">
                        <!-- Add Server Form -->
                        <form method="POST" class="mb-4">
                            <input type="hidden" name="movie_id" value="<?php echo $movie_id; ?>">
                            <div class="row align-items-end">
                                <div class="col">
                                    <label class="form-label">Add Server</label>
                                    <select name="server_id" class="form-select" required>
                                        <option value="">Select Server</option>
                                        <?php foreach ($servers as $server): ?>
                                            <option value="<?php echo $server['id']; ?>">
                                                <?php echo htmlspecialchars($server['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <button type="submit" name="add_server" class="btn btn-primary">
                                        Add Server
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Server List Section -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Movie Servers</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Server</th>
                                                <th>URL</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($movie_servers as $server): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($server['server_name']); ?></td>
                                                <td><?php echo htmlspecialchars($server['url']); ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#editServerModal<?php echo $server['id']; ?>">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </button>
                                                    <form method="POST" class="d-inline" 
                                                          onsubmit="return confirm('Are you sure you want to delete this server?');">
                                                        <input type="hidden" name="movie_server_id" value="<?php echo $server['id']; ?>">
                                                        <input type="hidden" name="movie_id" value="<?php echo $movie_id; ?>">
                                                        <button type="submit" name="delete_server" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>

                                            <!-- Edit Server Modal -->
                                            <div class="modal fade" id="editServerModal<?php echo $server['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <form method="POST">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title">Edit Server</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <input type="hidden" name="movie_server_id" value="<?php echo $server['id']; ?>">
                                                                <input type="hidden" name="movie_id" value="<?php echo $movie_id; ?>">
                                                                <div class="mb-3">
                                                                    <label class="form-label">Server</label>
                                                                    <select name="server_id" class="form-select" required>
                                                                        <?php foreach ($servers as $s): ?>
                                                                        <option value="<?php echo $s['id']; ?>" 
                                                                                <?php echo ($s['id'] == $server['server_id']) ? 'selected' : ''; ?>>
                                                                            <?php echo htmlspecialchars($s['name']); ?>
                                                                        </option>
                                                                        <?php endforeach; ?>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                <button type="submit" name="edit_server" class="btn btn-primary">Save Changes</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Movie Preview -->
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <img src="https://image.tmdb.org/t/p/w500<?php echo $movie['poster_path']; ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($movie['title']); ?>">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($movie['title']); ?></h5>
                        <p class="card-text">
                            <small class="text-muted">TMDB ID: <?php echo $movie['tmdb_id']; ?></small>
                        </p>
                        <div class="d-grid gap-2">
                            <a href="../movie.php?slug=<?php echo $movie['slug']; ?>" 
                               target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-eye"></i> View Movie Page
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Server Modal -->
<div class="modal fade" id="addServerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Server</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Server</label>
                        <select name="server_id" class="form-select" required>
                            <option value="">Choose a server...</option>
                            <?php foreach ($servers as $server): ?>
                            <option value="<?php echo $server['id']; ?>">
                                <?php echo htmlspecialchars($server['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_server" class="btn btn-primary">Add Server</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete server confirmation
    document.querySelectorAll('.delete-server').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('Are you sure you want to remove this server?')) {
                const serverId = this.dataset.id;
                // Add your delete logic here
            }
        });
    });

    // Form validation
    const form = document.getElementById('editMovieForm');
    form.addEventListener('submit', function(e) {
        const title = form.querySelector('input[name="title"]').value.trim();
        if (!title) {
            e.preventDefault();
            alert('Title is required');
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
