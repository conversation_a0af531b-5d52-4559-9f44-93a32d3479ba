<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';
require_once 'includes/server_manager.php';
require_once 'includes/custom_player.php';

$auth = new Auth($db);
$player = new CustomPlayer($db);

if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$episode_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$episode_id) {
    header('Location: series.php');
    exit;
}

try {
    // Get episode details
    $stmt = $db->prepare("
        SELECT e.*, s.title as series_title, s.id as series_id 
        FROM episodes e 
        JOIN series s ON e.series_id = s.id 
        WHERE e.id = ?
    ");
    $stmt->execute([$episode_id]);
    $episode = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$episode) {
        header('Location: series.php');
        exit;
    }

    // Get episode servers
    $server_manager = new ServerManager($db);
    $servers = $server_manager->getServers($episode_id, 'episode');

} catch(PDOException $e) {
    error_log($e->getMessage());
    header('Location: series.php');
    exit;
}

$page_title = $episode['series_title'] . ' - S' . $episode['season_number'] . 'E' . $episode['episode_number'];
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <a href="show.php?id=<?php echo $episode['series_id']; ?>" class="text-light text-decoration-none">
                    <?php echo htmlspecialchars($episode['series_title']); ?>
                </a>
                - Season <?php echo $episode['season_number']; ?> Episode <?php echo $episode['episode_number']; ?>
            </h2>

            <!-- Video Player -->
            <div class="card bg-dark">
                <div class="card-body p-0">
                    <div style="position: relative; padding-top: 56.25%;">
                        <div class="plyr__video-embed" id="player">
                            <iframe
                                src="<?php echo htmlspecialchars($servers[0]['url'] ?? ''); ?>"
                                allowfullscreen
                                allow="autoplay; encrypted-media"
                                referrerpolicy="no-referrer"
                                sandbox="allow-scripts allow-same-origin allow-forms"
                                loading="lazy"
                                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
                            ></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Server Selection -->
            <div class="server-buttons mb-3">
                <div class="server-list">
                    <?php foreach ($servers as $index => $server): ?>
                        <button class="btn btn-outline-light server-btn <?php echo $index === 0 ? 'active' : ''; ?>"
                                data-server-url="<?php echo htmlspecialchars($server['url']); ?>">
                            Server <?php echo $index + 1; ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.plyr__video-embed {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    max-width: 100%;
}

.plyr__video-embed iframe {
    border: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    z-index: 999999 !important;
}

/* Block unwanted elements */
.plyr__video-embed iframe > div:not([class*="player"]),
.plyr__video-embed iframe > script,
.plyr__video-embed iframe > style {
    display: none !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const playerFrame = document.querySelector('.plyr__video-embed iframe');
    const container = document.querySelector('.plyr__video-embed');
    
    // Function to handle fullscreen changes
    function onFullscreenChange() {
        if (document.fullscreenElement) {
            lockOrientation();
        } else {
            // Reset orientation and styles when exiting fullscreen
            unlockOrientation();
            if (playerFrame) {
                playerFrame.style.transform = '';
                playerFrame.style.width = '100%';
                playerFrame.style.height = '100%';
            }
        }
    }

    // Function to lock orientation
    async function lockOrientation() {
        try {
            await screen.orientation.lock('landscape');
        } catch (err) {
            console.log('Orientation lock failed:', err);
        }
    }

    // Function to unlock orientation
    async function unlockOrientation() {
        try {
            await screen.orientation.unlock();
        } catch (err) {
            console.log('Orientation unlock failed:', err);
        }
    }

    // Function to enter fullscreen
    async function enterFullscreen() {
        try {
            await screen.orientation.lock('landscape').catch(err => console.log(err));

            if (container.requestFullscreen) {
                await container.requestFullscreen();
            } else if (container.webkitRequestFullscreen) {
                await container.webkitRequestFullscreen();
            } else if (container.msRequestFullscreen) {
                await container.msRequestFullscreen();
            } else if (container.mozRequestFullScreen) {
                await container.mozRequestFullScreen();
            }
        } catch (err) {
            console.log('Fullscreen failed:', err);
        }
    }

    // Add event listeners
    document.addEventListener('fullscreenchange', onFullscreenChange);
    document.addEventListener('webkitfullscreenchange', onFullscreenChange);
    document.addEventListener('mozfullscreenchange', onFullscreenChange);
    document.addEventListener('MSFullscreenChange', onFullscreenChange);

    // Add click event for fullscreen
    container.addEventListener('click', function() {
        enterFullscreen();
    });

    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        if (document.fullscreenElement) {
            lockOrientation();
        } else {
            unlockOrientation();
        }
    });

    // Block redirect attempts and other security measures remain unchanged
    playerFrame.addEventListener('load', function() {
        try {
            const frameDoc = playerFrame.contentDocument || playerFrame.contentWindow.document;
            const unwantedElements = frameDoc.querySelectorAll('script[src*="ads"], div[id*="ads"]');
            unwantedElements.forEach(el => el.remove());
        } catch(e) {
            console.log('Frame access restricted due to same-origin policy');
        }
    });

    // Server switching code
    const serverButtons = document.querySelectorAll('.server-btn');
    serverButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            serverButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            // Update player source
            playerFrame.src = this.dataset.serverUrl;
        });
    });
});

// Disable form submission warning
window.onbeforeunload = null;

// Episode navigation without warning
document.querySelectorAll('.episode-link').forEach(link => {
    link.addEventListener('click', function(e) {
        // Remove any form data
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
