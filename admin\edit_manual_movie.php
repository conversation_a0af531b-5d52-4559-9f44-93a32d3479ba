<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$movie_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$success = $error = '';

// Get movie details
$stmt = $db->prepare("
    SELECT m.* 
    FROM manual_movies m 
    WHERE m.id = ?
");
$stmt->execute([$movie_id]);
$movie = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$movie) {
    header('Location: index.php');
    exit;
}

// Get sources
$stmt = $db->prepare("
    SELECT * FROM manual_movie_sources 
    WHERE movie_id = ?
");
$stmt->execute([$movie_id]);
$sources = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db->beginTransaction();
        
        // Update movie details
        $stmt = $db->prepare("
            UPDATE manual_movies SET 
                title = ?,
                overview = ?,
                poster_path = ?,
                backdrop_path = ?,
                release_date = ?,
                runtime = ?,
                rating = ?,
                slug = ?,
                is_bengali = ?
            WHERE id = ?
        ");

        // Generate slug from title
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $_POST['title'])));

        $stmt->execute([
            $_POST['title'],
            $_POST['overview'],
            $_POST['poster_path'],
            $_POST['backdrop_path'],
            $_POST['release_date'],
            $_POST['runtime'],
            $_POST['rating'],
            $slug,
            isset($_POST['is_bengali']) ? 1 : 0,
            $movie_id
        ]);
        
        // Delete existing sources
        $stmt = $db->prepare("DELETE FROM manual_movie_sources WHERE movie_id = ?");
        $stmt->execute([$movie_id]);
        
        // Insert updated sources
        $stmt = $db->prepare("
            INSERT INTO manual_movie_sources (
                movie_id, server_name, source_url, quality
            ) VALUES (?, ?, ?, ?)
        ");
        
        foreach ($_POST['sources'] as $source) {
            if (!empty($source['url'])) {
                $stmt->execute([
                    $movie_id,
                    $source['server_name'],
                    $source['url'],
                    $source['quality']
                ]);
            }
        }
        
        $db->commit();
        $success = 'Movie updated successfully!';
        
        // Refresh movie and sources data
        $stmt = $db->prepare("SELECT * FROM manual_movies WHERE id = ?");
        $stmt->execute([$movie_id]);
        $movie = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $db->prepare("SELECT * FROM manual_movie_sources WHERE movie_id = ?");
        $stmt->execute([$movie_id]);
        $sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = 'Error updating movie: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Manual Movie</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --text-color: #ecf0f1;
        }

        body {
            background: var(--primary-color);
            color: var(--text-color);
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        .movie-edit-container {
            max-width: 1200px;
            margin: 2rem auto;
            background: var(--secondary-color);
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
            padding: 2rem;
        }

        .form-section {
            background: rgba(0,0,0,0.2);
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .section-title {
            color: var(--accent-color);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .form-control {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: rgba(255,255,255,0.15);
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
            color: var(--text-color);
        }

        .preview-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-top: 10px;
        }

        .btn-primary {
            background: var(--accent-color);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .form-check-input:checked {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            .movie-edit-container {
                margin: 1rem;
                padding: 1rem;
            }

            .form-section {
                padding: 1rem;
            }

            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .preview-image {
                height: 150px;
            }
        }

        /* Custom Switch Style */
        .custom-switch {
            padding-left: 2.25rem;
        }

        .custom-switch .form-check-input {
            width: 3rem;
            height: 1.5rem;
            margin-left: -2.25rem;
        }

        /* Loading Spinner */
        .spinner-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="spinner-overlay" id="loadingSpinner">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="movie-edit-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-film me-2"></i>Edit Manual Movie
        </h1>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" class="needs-validation" novalidate id="movieForm">
            <!-- Basic Information -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle me-2"></i>Basic Information
                </h3>
                <div class="row g-3">
                    <div class="col-12 col-md-6">
                        <label class="form-label">Title</label>
                        <input type="text" name="title" class="form-control" required 
                               value="<?php echo htmlspecialchars($movie['title']); ?>">
                        <div class="invalid-feedback">Please provide a title.</div>
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label">Release Date</label>
                        <input type="date" name="release_date" class="form-control" 
                               value="<?php echo $movie['release_date']; ?>">
                    </div>
                </div>
            </div>

            <!-- Media Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-images me-2"></i>Media
                </h3>
                <div class="row g-3">
                    <div class="col-12 col-md-6">
                        <label class="form-label">Poster URL</label>
                        <input type="text" name="poster_path" class="form-control" id="posterUrl"
                               value="<?php echo htmlspecialchars($movie['poster_path']); ?>">
                        <img src="<?php echo htmlspecialchars($movie['poster_path']); ?>" 
                             class="preview-image mt-2" id="posterPreview" alt="Poster Preview">
                    </div>
                    <div class="col-12 col-md-6">
                        <label class="form-label">Backdrop URL</label>
                        <input type="text" name="backdrop_path" class="form-control" id="backdropUrl"
                               value="<?php echo htmlspecialchars($movie['backdrop_path']); ?>">
                        <img src="<?php echo htmlspecialchars($movie['backdrop_path']); ?>" 
                             class="preview-image mt-2" id="backdropPreview" alt="Backdrop Preview">
                    </div>
                </div>
            </div>

            <!-- Details Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-list-alt me-2"></i>Details
                </h3>
                <div class="mb-3">
                    <label class="form-label">Overview</label>
                    <textarea name="overview" class="form-control" rows="4"><?php echo htmlspecialchars($movie['overview']); ?></textarea>
                </div>
                <div class="row g-3">
                    <div class="col-6">
                        <label class="form-label">Runtime (minutes)</label>
                        <input type="number" name="runtime" class="form-control" 
                               value="<?php echo $movie['runtime']; ?>">
                    </div>
                    <div class="col-6">
                        <label class="form-label">Rating</label>
                        <input type="number" name="rating" class="form-control" step="0.1" min="0" max="10" 
                               value="<?php echo $movie['rating']; ?>">
                    </div>
                </div>
            </div>

            <!-- Options Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-cog me-2"></i>Options
                </h3>
                <div class="custom-switch form-check form-switch">
                    <input type="checkbox" name="is_bengali" class="form-check-input" id="is_bengali" 
                           value="1" <?php echo ($movie['is_bengali'] ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="is_bengali">Bengali Content</label>
                </div>
            </div>

            <!-- Sources Section -->
            <div class="form-section">
                <h4 class="section-title">Movie Sources</h4>
                <div id="sources-container">
                    <?php foreach ($sources as $index => $source): ?>
                    <div class="source-group mb-3">
                        <div class="row g-2">
                            <div class="col-md-3">
                                <input type="text" 
                                       name="sources[<?php echo $index; ?>][server_name]" 
                                       class="form-control" 
                                       placeholder="Server Name"
                                       value="<?php echo htmlspecialchars($source['server_name']); ?>">
                            </div>
                            <div class="col-md-6">
                                <input type="text" 
                                       name="sources[<?php echo $index; ?>][url]" 
                                       class="form-control" 
                                       placeholder="Source URL"
                                       value="<?php echo htmlspecialchars($source['source_url']); ?>">
                            </div>
                            <div class="col-md-3">
                                <select name="sources[<?php echo $index; ?>][quality]" class="form-select">
                                    <option value="HD" <?php echo $source['quality'] === 'HD' ? 'selected' : ''; ?>>HD</option>
                                    <option value="SD" <?php echo $source['quality'] === 'SD' ? 'selected' : ''; ?>>SD</option>
                                    <option value="CAM" <?php echo $source['quality'] === 'CAM' ? 'selected' : ''; ?>>CAM</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <button type="button" class="btn btn-secondary mt-2" onclick="addSource()">
                    <i class="fas fa-plus"></i> Add Source
                </button>
            </div>

            <!-- Submit Button -->
            <div class="text-center mt-4">
                <a href="manual_content.php" class="btn btn-secondary btn-lg me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back
                </a>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Save Changes
                </button>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Image Preview
        function updateImagePreview(urlInput, previewImg) {
            urlInput.addEventListener('input', function() {
                previewImg.src = this.value || 'placeholder-image.jpg';
            });
        }

        // Initialize image previews
        updateImagePreview(
            document.getElementById('posterUrl'),
            document.getElementById('posterPreview')
        );
        updateImagePreview(
            document.getElementById('backdropUrl'),
            document.getElementById('backdropPreview')
        );

        // Form validation
        document.getElementById('movieForm').addEventListener('submit', function(event) {
            if (!this.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            this.classList.add('was-validated');

            // Show loading spinner
            document.getElementById('loadingSpinner').style.display = 'flex';
        });

        function addSource() {
            const container = document.getElementById('sources-container');
            const sourceCount = container.children.length;
            
            const sourceGroup = document.createElement('div');
            sourceGroup.className = 'source-group mb-3';
            sourceGroup.innerHTML = `
                <div class="row g-2">
                    <div class="col-md-3">
                        <input type="text" 
                               name="sources[${sourceCount}][server_name]" 
                               class="form-control" 
                               placeholder="Server Name">
                    </div>
                    <div class="col-md-6">
                        <input type="text" 
                               name="sources[${sourceCount}][url]" 
                               class="form-control" 
                               placeholder="Source URL">
                    </div>
                    <div class="col-md-3">
                        <select name="sources[${sourceCount}][quality]" class="form-select">
                            <option value="HD">HD</option>
                            <option value="SD">SD</option>
                            <option value="CAM">CAM</option>
                        </select>
                    </div>
                </div>
            `;
            container.appendChild(sourceGroup);
        }
    </script>
</body>
</html>
