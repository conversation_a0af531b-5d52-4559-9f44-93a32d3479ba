<?php
class IdEncryption {
    private $key;
    private $method = 'AES-256-CBC';

    public function __construct($key = null) {
        $this->key = $key ?: getenv('ENCRYPTION_KEY') ?: 'your-secret-key-here';
    }

    public function encrypt($id) {
        $ivlen = openssl_cipher_iv_length($this->method);
        $iv = openssl_random_pseudo_bytes($ivlen);
        $encrypted = openssl_encrypt($id, $this->method, $this->key, 0, $iv);
        return urlencode(base64_encode($iv . $encrypted));
    }

    public function decrypt($encrypted) {
        $encrypted = base64_decode(urldecode($encrypted));
        $ivlen = openssl_cipher_iv_length($this->method);
        $iv = substr($encrypted, 0, $ivlen);
        $encrypted = substr($encrypted, $ivlen);
        return openssl_decrypt($encrypted, $this->method, $this->key, 0, $iv);
    }
}