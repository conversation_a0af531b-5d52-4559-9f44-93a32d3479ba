<?php
class DatabaseSessionHandler implements SessionHandlerInterface {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function open($savePath, $sessionName) {
        return true;
    }

    public function close() {
        return true;
    }

    public function read($id) {
        $stmt = $this->db->prepare("SELECT data FROM sessions WHERE id = ? AND expires > NOW()");
        $stmt->execute([$id]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? $row['data'] : '';
    }

    public function write($id, $data) {
        $expires = date('Y-m-d H:i:s', time() + 86400);
        $stmt = $this->db->prepare("REPLACE INTO sessions (id, data, expires) VALUES (?, ?, ?)");
        return $stmt->execute([$id, $data, $expires]);
    }

    public function destroy($id) {
        $stmt = $this->db->prepare("DELETE FROM sessions WHERE id = ?");
        return $stmt->execute([$id]);
    }

    public function gc($maxlifetime) {
        $stmt = $this->db->prepare("DELETE FROM sessions WHERE expires < NOW()");
        return $stmt->execute();
    }
}