<?php
require_once 'db.php';
require_once 'auth.php';
require_once 'ad_blocker.php';

$auth = new Auth($db);

// Debug URL parameter
error_log("Raw URL parameter: " . ($_GET['url'] ?? 'not set'));

// Check if URL parameter exists and is not empty
if (!isset($_GET['url']) || empty(trim($_GET['url']))) {
    error_log("Missing or empty URL parameter");
    header('HTTP/1.0 400 Bad Request');
    exit('Invalid URL - URL parameter is required');
}

// Get and decode URL
$url = urldecode(trim($_GET['url']));

// Debug decoded URL
error_log("Decoded URL: " . $url);

// Validate URL format
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    error_log("Invalid URL format: " . $url);
    header('HTTP/1.0 400 Bad Request');
    exit('Invalid URL format - Must be a valid HTTP/HTTPS URL');
}

// Ensure URL uses HTTPS
if (parse_url($url, PHP_URL_SCHEME) !== 'https') {
    error_log("Non-HTTPS URL detected");
    header('HTTP/1.0 400 Bad Request');
    exit('Invalid URL - Must use HTTPS');
}

// Whitelist allowed domains
$allowed_domains = [
    'flicky.host',
    'vidsrc.to',
    'vidsrc.me',
    'movieshd.watch',
    'autoembed.to',
    'player.autoembed.cc',
    'vidsrc.xyz',
    'superembed.stream'
];

$url_parts = parse_url($url);
$domain = isset($url_parts['host']) ? strtolower($url_parts['host']) : '';

// Debug domain check
error_log("Checking domain: " . $domain);

$domain_allowed = false;
foreach ($allowed_domains as $allowed) {
    if (strpos($domain, $allowed) !== false) {
        $domain_allowed = true;
        break;
    }
}

if (!$domain_allowed) {
    error_log("Domain not allowed: " . $domain);
    header('HTTP/1.0 403 Forbidden');
    exit('Domain not allowed - Must be from approved sources');
}

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For https requests
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// Get content
$content = curl_exec($ch);

if (curl_errno($ch)) {
    error_log('Curl error: ' . curl_error($ch));
    header('HTTP/1.0 500 Internal Server Error');
    exit('Failed to fetch content');
}

$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code !== 200) {
    header('HTTP/1.0 ' . $http_code);
    exit('Remote server error');
}

// Clean content
$content = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $content);
$content = preg_replace('/<iframe\b[^>]*>(.*?)<\/iframe>/is', '', $content);
$content = preg_replace('/onclick=["\'].*?["\']/i', '', $content);
$content = preg_replace('/<a[^>]*>/i', '<a target="_self">', $content);

// Remove ad-related elements
$content = preg_replace([
    '/<div[^>]*(?:ad|popup|banner|overlay|modal)[^>]*>.*?<\/div>/is',
    '/<img[^>]*(?:ad|banner)[^>]*>/is',
    '/<link[^>]*(?:ads?|banner)[^>]*>/is',
    '/<style[^>]*>[^<]*(?:ads?|popup|overlay|modal)[^<]*<\/style>/is'
], '', $content);

// Add anti-adblock protection
$content = str_replace('</head>', '
<style>
    .adb, .ads, .ad-box, .ad-container, [class*="ad-"], [id*="ad-"] { display: none !important; }
    #player-wrapper { position: relative; }
    .player-overlay { display: none !important; }
</style>
<script>
window.adsbygoogle = [];
window.googletag = {};
window.googletag.cmd = [];
window.google_ad_client = false;
window.google_ad_slot = false;
</script>
</head>', $content);

// Add anti-detection script
$inject_script = <<<EOT
<script>
    // Override adblock detection
    const noop = () => {};
    window.adBlockDetected = noop;
    window.adBlockNotDetected = noop;
    
    // Fake ad-related functions
    window.googletag = {
        cmd: [],
        pubads: function() { return this; },
        enableServices: noop,
        defineSlot: function() { return this; },
        addService: function() { return this; },
        display: noop
    };
    
    // Override common detection methods
    window.canRunAds = true;
    window.isAdBlockActive = false;
    window.adblockDetector = null;
    
    // Intercept and clean ads
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach((node) => {
                    if (node.tagName === 'IFRAME' || 
                        node.tagName === 'DIV' || 
                        node.tagName === 'INS') {
                        if (node.className.includes('ad') || 
                            node.id.includes('ad') ||
                            node.src?.includes('ads')) {
                            node.remove();
                        }
                    }
                });
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
</script>
EOT;

// Inject the script into the content before sending
$content = str_replace('</head>', $inject_script . '</head>', $content);

// Add before returning content
$inject_script = "<script>
    // Override sandbox detection more aggressively
    Object.defineProperty(HTMLIFrameElement.prototype, 'sandbox', {
        get: function() { return ''; },
        set: function() { return true; },
        configurable: false
    });

    // Disable sandbox checks
    window.isSandboxed = false;
    window.hasSandbox = false;

    // Create fake sandbox attributes
    const createElementOriginal = document.createElement;
    document.createElement = function(tag) {
        const element = createElementOriginal.call(document, tag);
        if (tag.toLowerCase() === 'iframe') {
            Object.defineProperty(element, 'sandbox', {
                get: function() { return ''; },
                set: function() { return true; }
            });
        }
        return element;
    };

    // Additional anti-detection
    window.adBlockEnabled = false;
    window.adBlockDetected = false;
    window.showAds = true;
    window.isAdBlockActive = false;
</script>";

// Add sandbox bypass styles
$inject_style = "<style>
    iframe[sandbox] {
        border: none !important;
    }
    .adb-enabled, .ad-block-enabled,
    .adblock-detected, .sandbox-detected {
        display: none !important;
    }
</style>";

// Inject both style and script
$content = str_replace('</head>', $inject_style . $inject_script . '</head>', $content);

// Block bad requests
$url = $_SERVER['REQUEST_URI'];
if (AdBlocker::shouldBlock($url)) {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Before returning content
$content = AdBlocker::cleanHtml($content);
$content = str_replace('</head>', AdBlocker::injectProtection() . '</head>', $content);

// Add before serving content
$content = preg_replace(
    '/window\.location|window\.open|window\.navigate|document\.location/',
    'void',
    $content
);

// Block known ad domains
$blocked_domains = ['ads.example.com', 'tracker.example.com'];
$current_domain = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
if (in_array($current_domain, $blocked_domains)) {
    header("HTTP/1.1 403 Forbidden");
    exit;
}

// Add sandbox bypass script
$sandbox_bypass = "<script>
    // Override sandbox detection
    (function() {
        const originalQuery = document.querySelector;
        document.querySelector = function(...args) {
            const element = originalQuery.apply(this, args);
            if (element && element.hasAttribute('sandbox')) {
                Object.defineProperty(element, 'sandbox', {
                    get: () => null,
                    set: () => true,
                    configurable: true
                });
            }
            return element;
        };

        // Override createElement
        const originalCreate = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreate.call(document, tagName);
            if (tagName.toLowerCase() === 'iframe') {
                Object.defineProperty(element, 'sandbox', {
                    get: () => null,
                    set: () => true,
                    configurable: true
                });
            }
            return element;
        };

        // Global overrides
        window.isSandboxed = false;
        window.hasSandbox = false;
        window.sandboxAllowed = true;
    })();
</script>";

$content = str_replace('</head>', $sandbox_bypass . '</head>', $content);

// Output cleaned content
header('Content-Type: text/html; charset=utf-8');
header('X-Frame-Options: SAMEORIGIN');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: no-referrer');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: *');
echo $content;
