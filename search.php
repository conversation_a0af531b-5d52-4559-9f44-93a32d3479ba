<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';
require_once 'includes/search.php';

$auth = new Auth($db);
$search = new Search($db);

$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';
$results = [];

if ($search_query) {
    $results = $search->searchContent($search_query);
}

$page_title = "Search Results - Cinepix32";
require_once 'includes/header.php';
?>

<div class="container mt-4">
    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <form action="search.php" method="GET" class="search-form">
                <div class="input-group">
                    <input type="text" 
                           name="q" 
                           class="form-control form-control-lg" 
                           placeholder="Search movies and series (e.g. Avatar or Avatar 2009)..." 
                           value="<?php echo htmlspecialchars($search_query); ?>"
                           required>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </form>
        </div>
    </div>

    <?php if ($search_query): ?>
        <!-- Movies Results -->
        <?php if (!empty($results['movies'])): ?>
            <div class="content-section mb-5">
                <h2 class="section-title mb-4">Movies</h2>
                <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-4">
                    <?php foreach ($results['movies'] as $movie): ?>
                        <div class="col">
                            <div class="card h-100 bg-dark text-white border-0">
                                <?php
                                // Generate correct URL based on source
                                $movie_url = ($movie['source'] === 'manual') 
                                    ? "manual_player.php?id=" . $movie['id']
                                    : "movie.php?id=" . $movie['id'];
                                
                                // Generate correct poster path
                                $poster_path = '';
                                if (!empty($movie['poster_path'])) {
                                    if (strpos($movie['poster_path'], 'http') === 0) {
                                        $poster_path = $movie['poster_path'];
                                    } else {
                                        $poster_path = 'https://image.tmdb.org/t/p/w500' . $movie['poster_path'];
                                    }
                                } else {
                                    $poster_path = 'assets/images/default-poster.jpg';
                                }
                                ?>
                                <a href="<?php echo $movie_url; ?>" class="text-decoration-none">
                                    <img src="<?php echo $poster_path; ?>" 
                                         class="card-img-top" 
                                         alt="<?php echo htmlspecialchars($movie['title']); ?>"
                                         loading="lazy">
                                    <div class="card-body">
                                        <h5 class="card-title text-white"><?php echo htmlspecialchars($movie['title']); ?></h5>
                                        <?php if (!empty($movie['release_date'])): ?>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    <?php echo date('Y', strtotime($movie['release_date'])); ?>
                                                </small>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Series Results -->
        <?php if (!empty($results['series'])): ?>
            <div class="content-section mb-5">
                <h2 class="section-title mb-4">TV Series</h2>
                <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-4">
                    <?php foreach ($results['series'] as $series): ?>
                        <div class="col">
                            <div class="card h-100 bg-dark text-white border-0">
                                <a href="series.php?id=<?php echo $series['id']; ?>" class="text-decoration-none">
                                    <img src="<?php echo !empty($series['poster_path']) ? 
                                        'https://image.tmdb.org/t/p/w500' . $series['poster_path'] : 
                                        'assets/images/default-poster.jpg'; ?>" 
                                         class="card-img-top" 
                                         alt="<?php echo htmlspecialchars($series['title']); ?>"
                                         loading="lazy">
                                    <div class="card-body">
                                        <h5 class="card-title text-white"><?php echo htmlspecialchars($series['title']); ?></h5>
                                    </div>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (empty($results['movies']) && empty($results['series'])): ?>
            <div class="alert alert-info text-center">
                No results found for "<?php echo htmlspecialchars($search_query); ?>"
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.card {
    transition: transform 0.2s;
    overflow: hidden;
}

.card:hover {
    transform: scale(1.05);
}

.card-img-top {
    aspect-ratio: 2/3;
    object-fit: cover;
}

.card-title {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.section-title {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 600;
}

.search-form .form-control {
    background-color: #333;
    border: 1px solid #444;
    color: #fff;
}

.search-form .form-control:focus {
    background-color: #444;
    border-color: #666;
    box-shadow: none;
}

.search-form .btn-primary {
    background-color: #e50914;
    border-color: #e50914;
}

.search-form .btn-primary:hover {
    background-color: #b2070f;
    border-color: #b2070f;
}
</style>

<!-- এখানে সার্চ রেজাল্ট দেখানোর HTML কোড লিখুন --> 
