<?php
session_start();
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/auth.php';

$auth = new Auth($db);

if (!$auth->isLoggedIn()) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    header('Location: login.php');
    exit;
}

// Get user's watchlist items
$stmt = $db->prepare("
    SELECT w.*, 
           CASE 
               WHEN w.content_type = 'movie' THEN m.title 
               ELSE s.title 
           END as title,
           CASE 
               WHEN w.content_type = 'movie' THEN 
                   CASE 
                       WHEN m.poster_path LIKE 'http%' THEN m.poster_path 
                       WHEN m.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', m.poster_path)
                       ELSE 'assets/images/default-poster.jpg'
                   END
               ELSE 
                   CASE 
                       WHEN s.poster_path LIKE 'http%' THEN s.poster_path 
                       WHEN s.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', s.poster_path)
                       ELSE 'assets/images/default-poster.jpg'
                   END
           END as poster_path,
           w.content_type,
           w.added_at
    FROM watchlist w
    LEFT JOIN movies m ON w.content_id = m.id AND w.content_type = 'movie'
    LEFT JOIN series s ON w.content_id = s.id AND w.content_type = 'series'
    WHERE w.user_id = ?
    ORDER BY w.added_at DESC
");

$stmt->execute([$_SESSION['user_id']]);
$watchlist = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'My Watchlist';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <h1 class="mb-4">My Watchlist</h1>

    <?php if (!empty($watchlist)): ?>
        <div class="row row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-lg-5 row-cols-xl-6 g-4">
            <?php foreach ($watchlist as $item): ?>
                <div class="col">
                    <div class="card h-100 bg-dark text-white border-0 content-card">
                        <a href="<?php echo $item['content_type'] === 'movie' ? 'movie.php?id=' : 'show.php?id='; ?><?php echo $item['content_id']; ?>" 
                           class="card-link">
                            <img src="<?php echo htmlspecialchars($item['poster_path']); ?>" 
                                 class="card-img-top" 
                                 alt="<?php echo htmlspecialchars($item['title']); ?>"
                                 loading="lazy">
                            <div class="card-body">
                                <h5 class="card-title text-truncate"><?php echo htmlspecialchars($item['title']); ?></h5>
                                <p class="card-text">
                                    <small class="text-muted">
                                        Added <?php echo date('M d, Y', strtotime($item['added_at'])); ?>
                                    </small>
                                </p>
                            </div>
                        </a>
                        <button class="btn btn-danger remove-watchlist" 
                                data-id="<?php echo $item['id']; ?>"
                                onclick="removeFromWatchlist(this)">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="text-center py-5">
            <h3>Your watchlist is empty</h3>
            <p class="text-muted">Start adding movies and series to your watchlist!</p>
            <a href="index.php" class="btn btn-primary mt-3">Browse Content</a>
        </div>
    <?php endif; ?>
</div>

<style>
.content-card {
    position: relative;
    transition: transform 0.2s;
}

.content-card:hover {
    transform: scale(1.05);
}

.content-card .card-link {
    color: inherit;
    text-decoration: none;
}

.content-card .card-img-top {
    height: 300px;
    object-fit: cover;
}

.remove-watchlist {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    opacity: 0;
    transition: opacity 0.2s;
}

.content-card:hover .remove-watchlist {
    opacity: 1;
}
</style>

<script>
function removeFromWatchlist(button) {
    if (!confirm('Are you sure you want to remove this item from your watchlist?')) {
        return;
    }

    const itemId = button.dataset.id;
    
    fetch('ajax/remove_watchlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: itemId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.closest('.col').remove();
            
            // Check if watchlist is empty
            const remainingItems = document.querySelectorAll('.content-card');
            if (remainingItems.length === 0) {
                location.reload();
            }
        } else {
            alert('Error removing item from watchlist');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error removing item from watchlist');
    });
}
</script>