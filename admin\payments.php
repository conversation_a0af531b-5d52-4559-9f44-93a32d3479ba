<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = "Manage Payments";
require_once 'includes/header.php';

// Handle payment status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_payment'])) {
    $payment_id = (int)$_POST['payment_id'];
    $new_status = $_POST['status'];
    $valid_statuses = ['pending', 'completed', 'failed', 'refunded'];

    if (in_array($new_status, $valid_statuses)) {
        try {
            $stmt = $db->prepare("UPDATE payments SET status = ? WHERE id = ?");
            $result = $stmt->execute([$new_status, $payment_id]);

            if ($result && $new_status === 'completed') {
                // Fetch payment details
                $stmt = $db->prepare("
                    SELECT user_id, subscription_id, amount 
                    FROM payments 
                    WHERE id = ?
                ");
                $stmt->execute([$payment_id]);
                $payment = $stmt->fetch(PDO::FETCH_ASSOC);

                // Get subscription duration
                $stmt = $db->prepare("
                    SELECT duration_days 
                    FROM subscriptions 
                    WHERE id = ?
                ");
                $stmt->execute([$payment['subscription_id']]);
                $subscription = $stmt->fetch(PDO::FETCH_ASSOC);

                // Add or extend subscription
                $end_date = date('Y-m-d H:i:s', strtotime("+{$subscription['duration_days']} days"));
                
                // Check if user has active subscription
                $stmt = $db->prepare("
                    SELECT id, end_date 
                    FROM user_subscriptions 
                    WHERE user_id = ? AND end_date > NOW()
                ");
                $stmt->execute([$payment['user_id']]);
                $existing_sub = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($existing_sub) {
                    // Extend existing subscription
                    $new_end_date = date('Y-m-d H:i:s', strtotime("+{$subscription['duration_days']} days", strtotime($existing_sub['end_date'])));
                    $stmt = $db->prepare("
                        UPDATE user_subscriptions 
                        SET end_date = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$new_end_date, $existing_sub['id']]);
                } else {
                    // Create new subscription
                    $stmt = $db->prepare("
                        INSERT INTO user_subscriptions (user_id, subscription_id, start_date, end_date) 
                        VALUES (?, ?, NOW(), ?)
                    ");
                    $stmt->execute([$payment['user_id'], $payment['subscription_id'], $end_date]);
                }
            }

            $success = "Payment status updated successfully";
        } catch (PDOException $e) {
            $error = "Error updating payment status: " . $e->getMessage();
        }
    }
}

// Fetch payments with user and subscription details
$sql = "
    SELECT 
        p.*,
        u.username,
        u.email,
        s.name as subscription_name
    FROM payments p
    JOIN users u ON p.user_id = u.id
    JOIN subscriptions s ON p.subscription_id = s.id
    ORDER BY p.created_at DESC
";
$payments = $db->query($sql)->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6>Payment Management</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Transaction ID</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payments as $payment): ?>
                                    <tr>
                                        <td><?php echo $payment['id']; ?></td>
                                        <td>
                                            <?php echo htmlspecialchars($payment['username']); ?><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($payment['email']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($payment['subscription_name']); ?></td>
                                        <td>৳<?php echo number_format($payment['amount'], 2); ?></td>
                                        <td><?php echo ucfirst($payment['payment_method']); ?></td>
                                        <td><?php echo htmlspecialchars($payment['transaction_id']); ?></td>
                                        <td>
                                            <span class="badge badge-sm bg-<?php 
                                                echo match($payment['status']) {
                                                    'completed' => 'success',
                                                    'pending' => 'warning',
                                                    'failed' => 'danger',
                                                    'refunded' => 'info',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php echo ucfirst($payment['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($payment['created_at'])); ?></td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#updatePayment<?php echo $payment['id']; ?>">
                                                Update Status
                                            </button>
                                        </td>
                                    </tr>

                                    <!-- Status Update Modal -->
                                    <div class="modal fade" id="updatePayment<?php echo $payment['id']; ?>" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <form method="POST">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Update Payment Status</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <input type="hidden" name="payment_id" value="<?php echo $payment['id']; ?>">
                                                        <div class="mb-3">
                                                            <label class="form-label">New Status</label>
                                                            <select name="status" class="form-select" required>
                                                                <option value="pending" <?php echo $payment['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                                <option value="completed" <?php echo $payment['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                                                <option value="failed" <?php echo $payment['status'] === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                                                <option value="refunded" <?php echo $payment['status'] === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="submit" name="update_payment" class="btn btn-primary">Update Status</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>