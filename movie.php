<?php
session_start();
header("Content-Security-Policy: frame-ancestors 'self'; frame-src 'self' * https:;");
require_once 'includes/init.php';
require_once 'includes/db.php';
require_once 'includes/server_manager.php';
require_once 'includes/auth.php';

$auth = new Auth($db);
if (!$auth->isLoggedIn()) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

function checkContentAccess($db, $user_id, $content_type) {
    $sql = "SELECT access_type FROM user_content_access 
            WHERE user_id = ? AND content_type = ? AND access_type = 'blocked'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id, $content_type]);
    return $stmt->rowCount() === 0; // Returns true if access is not blocked
}

$movie_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$server_manager = new ServerManager($db);
$auth = new Auth($db);
$user_id = $auth->getCurrentUserId();

if (!checkContentAccess($db, $user_id, 'movies')) {
    ?>
    <div class="modal fade" id="movieBlockedModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark">
                <div class="modal-header border-bottom border-danger">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-lock-alt text-danger me-2"></i>
                        <span class="text-danger">অ্যাক্সেস ব্লক করা হয়েছে!</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="blocked-animation mb-4">
                        <i class="fas fa-film text-danger fa-3x mb-3"></i>
                        <div class="lock-icon">
                            <i class="fas fa-lock text-danger"></i>
                        </div>
                    </div>
                    
                    <div class="alert custom-alert">
                        <h4 class="alert-heading mb-3 text-white">দুঃখিত!</h4>
                        <p class="mb-2 text-light">আপনার অ্যাকাউন্টের মুভি দেখার অ্যাক্সেস সাময়িকভাবে বন্ধ করা হয়েছে।</p>
                        <p class="mb-0 text-light">কারণ জানতে অথবা অ্যাক্সেস পুনরায় চালু করতে অনুগ্রহ করে সাপোর্টে যোগাযোগ করুন।</p>
                    </div>

                    <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                        <a href="support.php" class="btn custom-btn-danger btn-lg px-4">
                            <i class="fas fa-headset me-2"></i>সাপোর্টে যোগাযোগ করুন
                        </a>
                        <a href="index.php" class="btn custom-btn-outline btn-lg px-4">
                            <i class="fas fa-home me-2"></i>হোম পেজে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    #movieBlockedModal .modal-content {
        background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
        color: #fff;
        box-shadow: 0 0 30px rgba(229, 9, 20, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .custom-alert {
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 10px;
        backdrop-filter: blur(10px);
    }

    .blocked-animation {
        position: relative;
        display: inline-block;
        animation: float 3s ease-in-out infinite;
    }

    .blocked-animation .lock-icon {
        position: absolute;
        bottom: -10px;
        right: -10px;
        background: #2d2d2d;
        border-radius: 50%;
        padding: 10px;
        border: 2px solid #dc3545;
        animation: pulse 2s infinite;
        box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
    }

    .custom-btn-danger {
        background: linear-gradient(145deg, #dc3545, #c82333);
        border: none;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .custom-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        background: linear-gradient(145deg, #c82333, #dc3545);
    }

    .custom-btn-outline {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: #fff;
        transition: all 0.3s ease;
    }

    .custom-btn-outline:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: #fff;
        transform: translateY(-2px);
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
    }

    @media (max-width: 576px) {
        .d-sm-flex {
            flex-direction: column;
        }
        
        .btn-lg {
            width: 100%;
            margin: 0.5rem 0;
        }
    }
    </style>
    <?php
    exit();
}

// Get movie details
$stmt = $db->prepare("
    SELECT m.* 
    FROM movies m 
    WHERE m.id = ? AND m.status = 'active'
");
$stmt->execute([$movie_id]);
$movie = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$movie) {
    header('Location: 404.php');
    exit;
}

// Debug: Print movie details
error_log("Movie ID: " . $movie_id . ", TMDB ID: " . $movie['tmdb_id']);
    
// Initialize ServerManager
$server_manager = new ServerManager($db);
    
// Get available servers
$servers = $server_manager->getMovieServers($movie_id);
    
// Debug: Print servers
error_log("Found servers: " . print_r($servers, true));
    
// If no servers found, add them
if (empty($servers)) {
    // Add default servers
    $stmt = $db->prepare("
        INSERT INTO movie_servers (movie_id, server_id, url, status)
        SELECT 
            ?, -- movie_id
            s.id, -- server_id
            CONCAT(s.base_url, REPLACE(sp.movie_pattern, '{tmdb_id}', ?)), -- url
            'active' -- status
        FROM servers s
        JOIN server_patterns sp ON s.pattern_id = sp.id
        WHERE s.status = 'active'
    ");
    
    $stmt->execute([$movie_id, $movie['tmdb_id']]);
    
    // Get servers again
    $servers = $server_manager->getMovieServers($movie_id);
    
    // Debug: Print new servers
    error_log("Added new servers: " . print_r($servers, true));
}

// Get current server
$current_server = isset($_GET['server']) ? (int)$_GET['server'] : null;
if (!$current_server && !empty($servers)) {
    $current_server = $servers[0]['server_id'];
}

// Get current server URL
$current_url = '';
if ($current_server && !empty($servers)) {
    foreach ($servers as $server) {
        if ($server['server_id'] == $current_server) {
            $current_url = $server['url'];
            break;
        }
    }
}

$page_title = $movie['title'];
require_once 'includes/header.php'; ?>

<div class="movie-details-page">
    <!-- Player Section First -->
    <div class="container-fluid py-4">
        <div class="player-section">
            <!-- Server Selection -->
            <?php if (!empty($servers)): ?>
            <div class="server-buttons mb-4">
                <div class="d-flex align-items-center">
                    <span class="server-label me-3">Select Server:</span>
                    <div class="server-list">
                        <?php foreach ($servers as $server): ?>
                            <a href="?id=<?php echo $movie_id; ?>&server=<?php echo $server['server_id']; ?>" 
                               class="server-btn <?php echo ($server['server_id'] == $current_server) ? 'active' : ''; ?>">
                                <?php echo htmlspecialchars($server['server_name']); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Video Player -->
            <?php if ($current_url): ?>
                <div class="player-wrapper">
                    <div id="player-container">
                        <iframe src="<?php echo htmlspecialchars($current_url); ?>"
                                id="movie-player"
                                allowfullscreen
                                webkitallowfullscreen
                                mozallowfullscreen
                                sandbox="allow-forms allow-scripts allow-same-origin allow-presentation"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen; orientation-lock; remote-playback"
                                class="movie-player"
                                playsinline
                                onload="initializePlayer(this)">
                        </iframe>
                        
                        <!-- Add loading indicator -->
                        <div id="player-loader" class="text-center p-4">
                            <div class="spinner-border text-light" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-light">Loading player...</p>
                        </div>
                    </div>
                </div>

                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const player = document.getElementById('movie-player');
                    const loader = document.getElementById('player-loader');
                    const container = document.getElementById('player-container');
                    let currentServer = '<?php echo $current_server; ?>';
                    let retryCount = 0;
                    const MAX_RETRIES = 3;

                    // Hide loader when player loads
                    player.addEventListener('load', function() {
                        loader.style.display = 'none';
                    });

                    // Auto-switch server on error
                    player.addEventListener('error', function() {
                        if (retryCount < MAX_RETRIES) {
                            retryCount++;
                            switchToNextServer();
                        }
                    });

                    function switchToNextServer() {
                        const servers = <?php echo json_encode($servers); ?>;
                        let currentIndex = servers.findIndex(s => s.server_id == currentServer);
                        let nextIndex = (currentIndex + 1) % servers.length;
                        
                        if (nextIndex !== currentIndex) {
                            window.location.href = `?id=<?php echo $movie_id; ?>&server=${servers[nextIndex].server_id}`;
                        }
                    }

                    // Optimize fullscreen handling
                    let lastTap = 0;
                    
                    function enterFullscreen() {
                        if (container.requestFullscreen) {
                            container.requestFullscreen();
                        } else if (container.webkitRequestFullscreen) {
                            container.webkitRequestFullscreen();
                        } else if (container.msRequestFullscreen) {
                            container.msRequestFullscreen();
                        }
                    }

                    // Double tap/click for fullscreen
                    container.addEventListener('touchend', function(e) {
                        const currentTime = new Date().getTime();
                        const tapLength = currentTime - lastTap;
                        if (tapLength < 500 && tapLength > 0) {
                            e.preventDefault();
                            enterFullscreen();
                        }
                        lastTap = currentTime;
                    });

                    // Single click for fullscreen on desktop
                    container.addEventListener('click', enterFullscreen);
                });
                </script>

                <style>
                .player-wrapper {
                    position: relative;
                    width: 100%;
                    padding-top: 56.25%; /* 16:9 Aspect Ratio */
                    background: #000;
                }

                #player-container {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }

                .movie-player {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border: none;
                }

                #player-loader {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 1;
                }

                @media (max-width: 768px) {
                    .player-wrapper {
                        padding-top: 75%; /* 4:3 Aspect Ratio for mobile */
                    }
                }
                </style>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No available server found for this movie.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Download Section - Placed directly under the player -->
    <div class="download-section mt-4 p-4 bg-dark rounded">
        <div class="download-instructions mb-3">
            <h5 class="text-white mb-3"><i class="fas fa-download me-2"></i>ডাউনলোড নির্দেশনা</h5>
            <ol class="text-light mb-4">
                <li>নিচের "ডাউনলোড করুন" বাটনে ক্লিক করুন</li>
                <li>টেলিগ্রাম বটে /start কমান্ড দিন</li>
                <li>মুভির নাম টাইপ করে সার্চ করুন</li>
                <li>আপনার কাঙ্ক্ষিত মুভি সিলেক্ট করে ডাউনলোড করুন</li>
            </ol>
        </div>
        
        <div class="text-center">
            <a href="https://t.me/cinepix_premium_bot" 
               class="btn btn-danger btn-lg download-btn" 
               target="_blank">
                <i class="fab fa-telegram me-2"></i>ডাউনলোড করুন
            </a>
        </div>
    </div>

    <!-- Movie Details Section -->
    <div class="movie-details-section">
        <div class="container-fluid">
            <div class="row align-items-start">
                <div class="col-md-3 col-sm-6 col-8 mx-auto">
                    <div class="poster-wrapper">
                        <img src="https://image.tmdb.org/t/p/w500<?php echo htmlspecialchars($movie['poster_path']); ?>" 
                             class="movie-poster" 
                             alt="<?php echo htmlspecialchars($movie['title']); ?>">
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="movie-info text-light">
                        <h1 class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></h1>
                        <div class="movie-meta">
                            <span class="release-year"><?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                            <?php if (!empty($movie['runtime'])): ?>
                                <span class="runtime"><?php echo $movie['runtime']; ?> min</span>
                            <?php endif; ?>
                            <?php if (!empty($movie['rating'])): ?>
                                <span class="rating">
                                    <i class="fas fa-star text-warning"></i>
                                    <?php echo number_format($movie['rating'], 1); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="movie-overview"><?php echo htmlspecialchars($movie['overview']); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.movie-details-page {
    background-color: #141414;
    min-height: 100vh;
    padding-top: 20px;
}

.player-section {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
}

/* Mobile optimization for player section */
@media (max-width: 768px) {
    .movie-details-page {
        padding-top: 0;  /* Remove top padding on mobile */
    }

    .player-section {
        padding: 0;      /* Remove padding */
        margin: 0;       /* Remove margin */
        border-radius: 0; /* Remove border radius */
        width: 100vw;    /* Full viewport width */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }

    .container-fluid {
        padding-left: 0;  /* Remove container padding */
        padding-right: 0;
    }

    #player-container,
    #movie-player {
        width: 100% !important;
        height: 100% !important;
    }

    .player-wrapper {
        width: 100%;
        margin: 0 !important;  /* Remove any margin */
        position: relative;
        padding-top: 56.25%;   /* 16:9 Aspect Ratio */
        border-radius: 0;      /* Remove border radius */
    }

    .player-wrapper iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
    }

    /* Adjust server buttons for mobile */
    .server-buttons {
        padding: 10px;
        margin-bottom: 0;
    }

    .server-list {
        display: flex;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 5px;
    }

    .server-btn {
        white-space: nowrap;
        margin-right: 8px;
    }
}

.server-label {
    color: #fff;
    font-weight: 500;
}

.server-list {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Mobile optimization for server buttons */
@media (max-width: 768px) {
    .server-buttons {
        margin-bottom: 15px;
    }
    
    .server-buttons .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .server-label {
        margin-bottom: 10px;
    }
    
    .server-btn {
        padding: 6px 12px;
        font-size: 14px;
    }
}

.server-btn {
    padding: 8px 16px;
    background: #2d2d2d;
    color: #fff;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.server-btn:hover {
    background: #3a3a3a;
    color: #fff;
}

.server-btn.active {
    background: #e50914;
}

.player-wrapper {
    position: relative;
    padding-top: 56.25%;
    background: #000;
    margin-top: 20px;
    width: 100%;
}

#player-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.movie-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* Movie Details Section Responsive Design */
.movie-details-section {
    padding: 20px 0;
}

.poster-wrapper {
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
}

.movie-poster {
    width: 100%;
    height: auto;
    border-radius: 12px;
}

/* Mobile optimization for movie details */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 12px;
        padding-right: 12px;
    }
    
    .poster-wrapper {
        max-width: 200px;
        margin: 0 auto 20px auto;
    }
    
    .movie-info {
        text-align: center;
    }
    
    .movie-title {
        font-size: 24px;
        margin-bottom: 10px;
    }
    
    .movie-meta {
        justify-content: center;
        margin-bottom: 15px;
    }
    
    .movie-overview {
        font-size: 14px;
        line-height: 1.5;
        text-align: justify;
    }
}

.movie-meta {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    flex-wrap: wrap;
}

.movie-meta span {
    color: #aaa;
    font-size: 14px;
}

.rating i {
    margin-right: 5px;
}

/* Additional mobile optimizations */
@media (max-width: 768px) {
    .row.align-items-start {
        margin-left: 0;
        margin-right: 0;
    }
    
    .col-md-3, .col-md-9 {
        padding-left: 0;
        padding-right: 0;
    }
}

/* Player container styles */
.player-section {
    max-width: 1200px; /* Maximum width for desktop */
    margin: 0 auto;    /* Center the player */
    padding: 0 15px;   /* Add some padding on sides */
}

.player-wrapper {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect ratio */
    background: #000;
    margin: 20px auto;  /* Center with margin */
    width: 100%;
    max-width: 900px;   /* Limit maximum width for desktop */
    border-radius: 8px;
    overflow: hidden;
}

#player-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.movie-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Mobile optimization */
@media (max-width: 768px) {
    .player-section {
        padding: 0;
        margin: 0;
        max-width: none; /* Remove max-width for mobile */
        width: 100vw;    /* Full viewport width */
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }

    .player-wrapper {
        margin: 0;
        border-radius: 0;
        max-width: none; /* Remove max-width for mobile */
        width: 100%;    /* Full width */
    }

    #player-container,
    .movie-player {
        width: 100% !important;
        height: 100% !important;
    }
}

/* For very large screens */
@media (min-width: 1400px) {
    .player-wrapper {
        max-width: 1000px; /* Slightly larger for big screens */
    }
}

/* Ensure player is visible and accessible on Android TV */
@media (min-width: 960px) {
    .player-wrapper {
        position: relative;
        padding-top: 56.25%; /* 16:9 aspect ratio */
        background: #000;
    }
}
</style>

<?php require_once __DIR__ . '/includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const player = document.getElementById('movie-player');
    const container = document.getElementById('player-container');

    // Function to handle fullscreen changes
    function onFullscreenChange() {
        if (document.fullscreenElement) {
            lockOrientation();
        } else {
            // Reset orientation and styles when exiting fullscreen
            unlockOrientation();
            if (player) {
                player.style.transform = '';
                player.style.width = '100%';
                player.style.height = '100%';
            }
        }
    }

    // Function to lock orientation
    async function lockOrientation() {
        try {
            await screen.orientation.lock('landscape');
        } catch (err) {
            console.log('Orientation lock failed:', err);
        }
    }

    // Function to unlock orientation
    async function unlockOrientation() {
        try {
            await screen.orientation.unlock();
        } catch (err) {
            console.log('Orientation unlock failed:', err);
        }
    }

    // Function to enter fullscreen
    async function enterFullscreen() {
        try {
            await screen.orientation.lock('landscape').catch(err => console.log(err));

            if (container.requestFullscreen) {
                await container.requestFullscreen();
            } else if (container.webkitRequestFullscreen) {
                await container.webkitRequestFullscreen();
            } else if (container.msRequestFullscreen) {
                await container.msRequestFullscreen();
            } else if (container.mozRequestFullScreen) {
                await container.mozRequestFullScreen();
            }
        } catch (err) {
            console.log('Fullscreen failed:', err);
        }
    }

    // Add event listeners
    document.addEventListener('fullscreenchange', onFullscreenChange);
    document.addEventListener('webkitfullscreenchange', onFullscreenChange);
    document.addEventListener('mozfullscreenchange', onFullscreenChange);
    document.addEventListener('MSFullscreenChange', onFullscreenChange);

    // Double tap to fullscreen
    let lastTap = 0;
    container.addEventListener('touchend', function(e) {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;
        if (tapLength < 500 && tapLength > 0) {
            e.preventDefault();
            enterFullscreen();
        }
        lastTap = currentTime;
    });

    // Single click/tap to fullscreen
    container.addEventListener('click', function(e) {
        enterFullscreen();
    });

    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        if (document.fullscreenElement) {
            lockOrientation();
        }
    });
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const player = document.getElementById('movie-player');
    
    // Check if Remote Playback is supported
    if (window.RemotePlayback) {
        player.remote = new RemotePlayback();
        
        // Listen for remote connection
        player.remote.addEventListener('connect', function() {
            console.log('Connected to remote device');
        });
        
        // Listen for remote disconnect
        player.remote.addEventListener('disconnect', function() {
            console.log('Disconnected from remote device');
        });
    }
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const movieId = <?php echo json_encode($movie_id); ?>;
    const storageKey = `movie_position_${movieId}`;
    const player = document.getElementById('movie-player');
    let currentTime = 0;
    let isTracking = true;
    
    // Check for saved position
    const savedPosition = localStorage.getItem(storageKey);
    if (savedPosition) {
        const minutes = Math.floor(savedPosition / 60);
        const seconds = Math.floor(savedPosition % 60);
        const timeText = minutes > 0 ? 
            `${minutes} মিনিট ${seconds} সেকেন্ড` : 
            `${seconds} সেকেন্ড`;
        
        // Create resume popup with improved Bengali text and styling
        const popup = document.createElement('div');
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 25px;
            border-radius: 15px;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            font-size: 16px;
            max-width: 400px;
            width: 90%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        `;
        
        popup.innerHTML = `
            <div style="
                background: #e50914;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">
                <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polygon points="10 8 16 12 10 16 10 8"></polygon>
                </svg>
            </div>
            <div style="
                margin-bottom: 20px;
                line-height: 1.6;
                font-size: 18px;
            ">
                <div style="font-weight: bold; margin-bottom: 10px;">
                    আপনার দেখা শেষ হয়েছিল
                </div>
                <div style="color: #e50914; font-size: 22px; font-weight: bold; margin: 10px 0;">
                    ${timeText}
                </div>
                <div style="opacity: 0.8; font-size: 16px;">
                    আপনি কোথা থেকে দেখতে চান?
                </div>
            </div>
            <div style="
                display: flex;
                gap: 15px;
                justify-content: center;
            ">
                <button id="resumeBtn" style="
                    background: #e50914;
                    border: none;
                    color: white;
                    padding: 12px 25px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-weight: bold;
                    transition: all 0.2s;
                    font-size: 15px;
                ">
                    আগের অংশ থেকে
                </button>
                <button id="startOverBtn" style="
                    background: rgba(255,255,255,0.1);
                    border: 1px solid rgba(255,255,255,0.2);
                    color: white;
                    padding: 12px 25px;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.2s;
                    font-size: 15px;
                ">
                    শুরু থেকে
                </button>
            </div>
        `;
        
        // Add hover effects
        const style = document.createElement('style');
        style.textContent = `
            #resumeBtn:hover {
                transform: scale(1.05);
                background: #ff0a16;
                box-shadow: 0 0 20px rgba(229,9,20,0.3);
            }
            #startOverBtn:hover {
                transform: scale(1.05);
                background: rgba(255,255,255,0.2);
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translate(-50%, -48%); }
                to { opacity: 1; transform: translate(-50%, -50%); }
            }
        `;
        document.head.appendChild(style);
        
        // Add fade-in animation
        popup.style.animation = 'fadeIn 0.3s ease-out forwards';
        
        document.body.appendChild(popup);
        
        // Handle resume button
        document.getElementById('resumeBtn').addEventListener('click', () => {
            const seekTo = parseFloat(savedPosition);
            player.contentWindow.postMessage({
                type: 'seek',
                time: seekTo
            }, '*');
            popup.remove();
            
            // Show confirmation notification
            showMiniNotification(`আগের অংশ থেকে চালু করা হয়েছে`, 'success');
        });
        
        // Handle start over button
        document.getElementById('startOverBtn').addEventListener('click', () => {
            localStorage.removeItem(storageKey);
            popup.remove();
            
            // Show confirmation notification
            showMiniNotification(`মুভি শুরু থেকে চালু করা হয়েছে`, 'info');
        });

        // Add click outside to close
        document.addEventListener('click', function(event) {
            if (!popup.contains(event.target) && document.body.contains(popup)) {
                popup.remove();
            }
        });
    }
    
    // Track video progress
    window.addEventListener('message', function(event) {
        if (!isTracking) return;

        if (event.data.type === 'timeupdate') {
            currentTime = event.data.currentTime;
            
            // Save position every 5 seconds
            if (Math.floor(currentTime) % 5 === 0) {
                localStorage.setItem(storageKey, currentTime.toString());
                
                // Show mini notification
                showMiniNotification(`প্রগ্রেস সেভ করা হয়েছে (${Math.floor(currentTime / 60)} মিনিট)`);
            }
        } 
        else if (event.data.type === 'ended') {
            localStorage.removeItem(storageKey);
            isTracking = false;
            showMiniNotification('মুভি শেষ হয়েছে', 'success');
        }
    });
});

// Mini notification function
function showMiniNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        bottom: 10px;
        left: 10px;
        background: ${type === 'success' ? 'rgba(0, 128, 0, 0.9)' : 'rgba(0, 0, 0, 0.8)'};
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 9999;
        animation: fadeInOut 3s ease-in-out;
        pointer-events: none;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Add this CSS to your existing styles
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(20px); }
        10% { opacity: 1; transform: translateY(0); }
        90% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-20px); }
    }
    
    #resumeBtn:hover, #startOverBtn:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }
`;
document.head.appendChild(style);
</script>

<script>
function initializePlayer(iframe) {
    // Initialize communication with iframe
    let lastUpdateTime = 0;
    
    // Function to send message to parent
    function sendTimeUpdate(currentTime) {
        window.parent.postMessage({
            type: 'timeupdate',
            currentTime: currentTime
        }, '*');
    }
    
    // Add event listener for messages from parent
    window.addEventListener('message', function(event) {
        if (event.data.type === 'seek') {
            // Handle seeking in the video player
            const video = iframe.contentDocument.querySelector('video');
            if (video) {
                video.currentTime = event.data.time;
            }
        }
    });
    
    // Monitor video progress
    setInterval(() => {
        const video = iframe.contentDocument.querySelector('video');
        if (video) {
            const currentTime = video.currentTime;
            // Only update every 5 seconds
            if (currentTime - lastUpdateTime >= 5) {
                sendTimeUpdate(currentTime);
                lastUpdateTime = currentTime;
            }
            
            // Check if video ended
            if (video.ended) {
                window.parent.postMessage({
                    type: 'ended'
                }, '*');
            }
        }
    }, 1000);
}
</script>
