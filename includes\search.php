<?php
class Search {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function searchContent($query) {
        $results = [
            'movies' => [],
            'series' => []
        ];

        // Extract year if present in search query
        $year = null;
        $title = $query;
        
        if (preg_match('/^(.*?)\s*(\d{4})(?:\s*)?$/', $query, $matches)) {
            $title = trim($matches[1]);
            $year = $matches[2];
        }

        // Create search terms for flexible matching
        $titleTerm = '%' . str_replace(' ', '%', trim($title)) . '%';

        // Base movie query with proper title and year matching
        $movieQuery = "
            SELECT 
                m.*,
                :source as source,
                CASE 
                    WHEN (m.title LIKE :title_term OR m.original_title LIKE :title_term) 
                        AND YEAR(m.release_date) = :year THEN 1
                    WHEN (m.title LIKE :title_term OR m.original_title LIKE :title_term) THEN 2
                END as match_relevance
            FROM :table m
            WHERE m.status = 'active' 
            AND (
                (m.title LIKE :title_term OR m.original_title LIKE :title_term)
                AND (YEAR(m.release_date) = :year OR :year = 0)
            )
            ORDER BY match_relevance ASC, m.release_date DESC 
            LIMIT 12";

        try {
            // Search in TMDB movies
            $stmt = $this->db->prepare(str_replace(':table', 'movies', $movieQuery));
            $stmt->bindValue(':title_term', $titleTerm, PDO::PARAM_STR);
            $stmt->bindValue(':source', 'tmdb', PDO::PARAM_STR);
            $stmt->bindValue(':year', $year ?: 0, PDO::PARAM_INT);
            $stmt->execute();
            $tmdb_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Search in manual movies
            $stmt = $this->db->prepare(str_replace(':table', 'manual_movies', $movieQuery));
            $stmt->bindValue(':title_term', $titleTerm, PDO::PARAM_STR);
            $stmt->bindValue(':source', 'manual', PDO::PARAM_STR);
            $stmt->bindValue(':year', $year ?: 0, PDO::PARAM_INT);
            $stmt->execute();
            $manual_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Combine and sort movies by relevance
            $results['movies'] = array_merge($tmdb_movies, $manual_movies);
            usort($results['movies'], function($a, $b) {
                if ($a['match_relevance'] !== $b['match_relevance']) {
                    return $a['match_relevance'] - $b['match_relevance'];
                }
                return strtotime($b['release_date']) - strtotime($a['release_date']);
            });

            // Remove match_relevance from final results
            foreach ($results['movies'] as &$movie) {
                unset($movie['match_relevance']);
            }

            // Search in series with similar relevance logic
            $seriesQuery = "
                SELECT 
                    s.*,
                    'tmdb' as source,
                    CASE 
                        WHEN (s.title LIKE :title_term OR s.original_title LIKE :title_term) 
                            AND YEAR(s.first_air_date) = :year THEN 1
                        WHEN (s.title LIKE :title_term OR s.original_title LIKE :title_term) THEN 2
                    END as match_relevance
                FROM series s
                WHERE s.status = 'active' 
                AND (
                    (s.title LIKE :title_term OR s.original_title LIKE :title_term)
                    AND (YEAR(s.first_air_date) = :year OR :year = 0)
                )
                ORDER BY match_relevance ASC, s.first_air_date DESC 
                LIMIT 12";

            $stmt = $this->db->prepare($seriesQuery);
            $stmt->bindValue(':title_term', $titleTerm, PDO::PARAM_STR);
            $stmt->bindValue(':year', $year ?: 0, PDO::PARAM_INT);
            $stmt->execute();
            $results['series'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Remove match_relevance from series results
            foreach ($results['series'] as &$series) {
                unset($series['match_relevance']);
            }

            // Debug logging
            error_log("Search query: " . $query);
            error_log("Title term: " . $titleTerm);
            error_log("Year: " . ($year ?? 'none'));
            error_log("Total movies found: " . count($results['movies']));
            error_log("Total series found: " . count($results['series']));

            return $results;

        } catch (PDOException $e) {
            error_log("Search error: " . $e->getMessage());
            return $results;
        }
    }
}
