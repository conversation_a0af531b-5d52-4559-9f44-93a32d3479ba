<?php
session_start();
require_once '../../includes/db.php';
require_once '../../includes/auth.php';

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

header('Content-Type: application/json');

try {
    $data = json_decode(file_get_contents('php://input'), true);
    $movie_id = $data['id'] ?? 0;

    if (!$movie_id) {
        throw new Exception('Invalid movie ID');
    }

    // Begin transaction
    $db->beginTransaction();

    // Delete associated direct sources first
    $stmt = $db->prepare("DELETE FROM direct_sources WHERE content_type = 'manual' AND content_id = ?");
    $stmt->execute([$movie_id]);

    // Delete the movie
    $stmt = $db->prepare("DELETE FROM manual_movies WHERE id = ?");
    $stmt->execute([$movie_id]);

    $db->commit();
    
    echo json_encode(['success' => true]);
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}