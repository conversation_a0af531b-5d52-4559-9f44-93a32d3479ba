<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$action = $_GET['action'] ?? 'list';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add']) || isset($_POST['edit'])) {
        $name = $_POST['name'];
        $slug = strtolower(str_replace(' ', '-', $name));
        $stream_url = $_POST['stream_url'];
        $logo = $_POST['logo'];
        $category = $_POST['category'];
        $description = $_POST['description'];
        $status = $_POST['status'];
        $sort_order = (int)$_POST['sort_order'];

        if (isset($_POST['add'])) {
            $stmt = $db->prepare("INSERT INTO live_channels (name, slug, stream_url, logo, category, description, status, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$name, $slug, $stream_url, $logo, $category, $description, $status, $sort_order]);
        } else {
            $id = $_POST['id'];
            $stmt = $db->prepare("UPDATE live_channels SET name=?, slug=?, stream_url=?, logo=?, category=?, description=?, status=?, sort_order=? WHERE id=?");
            $stmt->execute([$name, $slug, $stream_url, $logo, $category, $description, $status, $sort_order, $id]);
        }
        header('Location: live-channels.php');
        exit;
    }

    if (isset($_POST['delete'])) {
        $stmt = $db->prepare("DELETE FROM live_channels WHERE id = ?");
        $stmt->execute([$_POST['id']]);
        header('Location: live-channels.php');
        exit;
    }
}

// Get channels list
$channels = $db->query("SELECT * FROM live_channels ORDER BY sort_order, name")->fetchAll(PDO::FETCH_ASSOC);

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Live TV Channels</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addChannelModal">
            Add New Channel
        </button>
    </div>

    <div class="bulk-actions mb-3">
        <div class="d-flex gap-2">
            <select id="bulkAction" class="form-select" style="width: auto;">
                <option value="">Bulk Actions</option>
                <option value="delete">Delete Selected</option>
                <option value="activate">Mark as Active</option>
                <option value="deactivate">Mark as Inactive</option>
            </select>
            <button id="applyBulkAction" class="btn btn-primary">Apply</button>
            
            <div class="ms-auto">
                <span id="selectedCount" class="text-muted"></span>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Logo</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($channels as $channel): ?>
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input channel-checkbox" 
                                       value="<?php echo $channel['id']; ?>">
                            </td>
                            <td>
                                <img src="<?php echo htmlspecialchars($channel['logo']); ?>" 
                                     alt="<?php echo htmlspecialchars($channel['name']); ?>" 
                                     style="height: 40px;">
                            </td>
                            <td><?php echo htmlspecialchars($channel['name']); ?></td>
                            <td><?php echo htmlspecialchars($channel['category']); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $channel['status'] === 'active' ? 'success' : 'danger'; ?>">
                                    <?php echo ucfirst($channel['status']); ?>
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-channel" 
                                        data-channel="<?php echo htmlspecialchars(json_encode($channel)); ?>"
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editChannelModal">
                                    Edit
                                </button>
                                <button class="btn btn-sm btn-danger delete-channel"
                                        data-id="<?php echo $channel['id']; ?>"
                                        data-name="<?php echo htmlspecialchars($channel['name']); ?>">
                                    Delete
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Channel Modal -->
<div class="modal fade" id="addChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Channel</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Channel Name</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stream URL</label>
                        <input type="url" name="stream_url" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Logo URL</label>
                        <input type="url" name="logo" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <input type="text" name="category" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-control"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Sort Order</label>
                        <input type="number" name="sort_order" class="form-control" value="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="add" class="btn btn-primary">Add Channel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Channel Modal -->
<div class="modal fade" id="editChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Channel</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="id" id="edit_id">
                    <div class="mb-3">
                        <label class="form-label">Channel Name</label>
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stream URL</label>
                        <input type="url" name="stream_url" id="edit_stream_url" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Logo URL</label>
                        <input type="url" name="logo" id="edit_logo" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <input type="text" name="category" id="edit_category" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" id="edit_description" class="form-control"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" id="edit_status" class="form-control">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Sort Order</label>
                        <input type="number" name="sort_order" id="edit_sort_order" class="form-control">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="edit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Delete Confirmation Modal -->
<div class="modal fade" id="deleteChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="id" id="delete_channel_id">
                <div class="modal-header">
                    <h5 class="modal-title">Delete Channel</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete "<span id="delete_channel_name"></span>"?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete" class="btn btn-danger">Delete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add bulk delete confirmation modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Bulk Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete <span id="deleteCount"></span> selected channels?
                This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDelete">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Add this JavaScript code before closing body tag -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bulk actions handler
    document.getElementById('applyBulkAction').addEventListener('click', function() {
        const selectedIds = Array.from(document.querySelectorAll('.channel-checkbox:checked'))
            .map(checkbox => checkbox.value);
            
        if (selectedIds.length === 0) {
            alert('Please select at least one channel');
            return;
        }

        const action = document.getElementById('bulkAction').value;
        
        if (!action) {
            alert('Please select an action');
            return;
        }

        // Add confirmation for delete
        if (action === 'delete' && !confirm('Are you sure you want to delete the selected channels?')) {
            return;
        }

        // Show loading state
        const button = document.getElementById('applyBulkAction');
        button.disabled = true;
        button.innerHTML = 'Processing...';

        // Make the AJAX request
        fetch('ajax/bulk_actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                ids: selectedIds
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'An error occurred');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while processing your request');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = 'Apply';
        });
    });

    // Select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        document.querySelectorAll('.channel-checkbox').forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // Individual checkbox handler
    document.querySelectorAll('.channel-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Update selected count
    function updateSelectedCount() {
        const count = document.querySelectorAll('.channel-checkbox:checked').length;
        document.getElementById('selectedCount').textContent = 
            count > 0 ? `${count} items selected` : '';
    }

    // Edit channel functionality
    document.querySelectorAll('.edit-channel').forEach(button => {
        button.addEventListener('click', function() {
            const channel = JSON.parse(this.dataset.channel);
            
            // Fill the edit form with channel data
            document.getElementById('edit_id').value = channel.id;
            document.getElementById('edit_name').value = channel.name;
            document.getElementById('edit_stream_url').value = channel.stream_url;
            document.getElementById('edit_logo').value = channel.logo;
            document.getElementById('edit_category').value = channel.category;
            document.getElementById('edit_description').value = channel.description;
            document.getElementById('edit_status').value = channel.status;
            document.getElementById('edit_sort_order').value = channel.sort_order;
        });
    });

    // Delete channel functionality
    document.querySelectorAll('.delete-channel').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm(`Are you sure you want to delete "${this.dataset.name}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="delete" value="1">
                    <input type="hidden" name="id" value="${this.dataset.id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    });
});
</script>

<style>
/* Modal Styling */
.modal-content {
    background-color: #fff !important;
    color: #333 !important;
}

.modal-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6;
}

.modal-body {
    background-color: #fff !important;
}

.modal-footer {
    background-color: #f8f9fa !important;
    border-top: 1px solid #dee2e6;
}

/* Form Controls */
.form-control {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #ced4da;
}

.form-control:focus {
    background-color: #fff !important;
    color: #333 !important;
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Labels */
.form-label {
    color: #333 !important;
    font-weight: 500;
}

/* Select Dropdown */
select.form-control {
    background-color: #fff !important;
    color: #333 !important;
}

/* Modal Close Button */
.btn-close {
    opacity: 0.5;
}

.btn-close:hover {
    opacity: 1;
}

/* Add & Edit Channel Modals */
#addChannelModal .modal-content,
#editChannelModal .modal-content {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#deleteChannelModal .modal-content {
    background-color: #fff !important;
    color: #333 !important;
}

#deleteChannelModal .modal-body {
    padding: 1.5rem;
}

#deleteChannelModal .text-danger {
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Improve delete button styling */
.delete-channel {
    margin-left: 0.5rem;
    transition: all 0.2s;
}

.delete-channel:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    transform: translateY(-1px);
}
</style>

<?php require_once 'includes/footer.php'; ?>
