<?php
// At the top of the file
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Also check if database connection is successful
try {
    $db = new PDO("mysql:host=".DB_HOST.";dbname=".DB_NAME, DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Connection failed: " . $e->getMessage());
}

session_start();
require_once '../includes/db.php';
require_once '../includes/auth.php';

// Add page title
$page_title = "Admin Dashboard";

// Include header
require_once 'includes/header.php';

// Debug line to check session
error_log('Admin ID: ' . ($_SESSION['admin_id'] ?? 'Not set'));

$auth = new Auth($db);
if (!$auth->isAdmin()) {
    error_log('Admin auth failed - redirecting to login');
    header('Location: ../login.php');
    exit;
}

// Get counts for dashboard
$stmt = $db->query("SELECT 
    (SELECT COUNT(*) FROM manual_movies WHERE status = 'active') as total_manual_movies,
    (SELECT COUNT(*) FROM manual_movies WHERE status = 'active' AND is_bengali = 1) as total_bengali_movies,
    (SELECT COUNT(*) FROM support_tickets WHERE status = 'open') as open_tickets,
    (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users
");
$counts = $stmt->fetch(PDO::FETCH_ASSOC);

// Get latest manual movies
$stmt = $db->query("SELECT * FROM manual_movies ORDER BY created_at DESC LIMIT 5");
$latest_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .movie-action-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-light">
    <?php include 'includes/navbar.php'; ?>

    <div class="container py-4">
        <h1 class="mb-4">Dashboard</h1>

        <!-- Stats Row -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white stats-card">
                    <div class="card-body">
                        <h5 class="card-title">Total Manual Movies</h5>
                        <p class="card-text display-6"><?= $counts['total_manual_movies'] ?></p>
                        <a href="manage_manual_movies.php" class="btn btn-light btn-sm">View All</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white stats-card">
                    <div class="card-body">
                        <h5 class="card-title">Bengali Movies</h5>
                        <p class="card-text display-6"><?= $counts['total_bengali_movies'] ?></p>
                        <a href="manage_manual_movies.php?type=bengali" class="btn btn-light btn-sm">View All</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white stats-card">
                    <div class="card-body">
                        <h5 class="card-title">Open Tickets</h5>
                        <p class="card-text display-6"><?= $counts['open_tickets'] ?></p>
                        <a href="support_tickets.php" class="btn btn-light btn-sm">View All</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark stats-card">
                    <div class="card-body">
                        <h5 class="card-title">Active Users</h5>
                        <p class="card-text display-6"><?= $counts['active_users'] ?></p>
                        <a href="manage_users.php" class="btn btn-light btn-sm">View All</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2">
                            <a href="add_manual_movie.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Manual Movie
                            </a>
                            <a href="bulk_import.php" class="btn btn-success">
                                <i class="fas fa-file-import"></i> Bulk Import
                            </a>
                            <a href="manage_direct_sources.php" class="btn btn-info">
                                <i class="fas fa-server"></i> Manage Sources
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Manual Movies -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Latest Manual Movies</h5>
                        <a href="manage_manual_movies.php" class="btn btn-primary btn-sm">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Added Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($latest_movies as $movie): ?>
                                    <tr>
                                        <td>
                                            <?php if (!empty($movie['poster_path'])): ?>
                                                <img src="<?= htmlspecialchars($movie['poster_path']) ?>" 
                                                     alt="<?= htmlspecialchars($movie['title']) ?>" 
                                                     style="width: 30px; height: 45px; object-fit: cover; margin-right: 10px;">
                                            <?php endif; ?>
                                            <?= htmlspecialchars($movie['title']) ?>
                                        </td>
                                        <td><?= $movie['is_bengali'] ? 'Bengali' : 'Other' ?></td>
                                        <td><?= date('Y-m-d', strtotime($movie['created_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $movie['status'] === 'active' ? 'success' : 'danger' ?>">
                                                <?= ucfirst($movie['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="edit_manual_movie.php?id=<?= $movie['id'] ?>" 
                                                   class="btn btn-primary btn-sm movie-action-btn">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="custom_player.php?id=<?= $movie['id'] ?>" 
                                                   class="btn btn-info btn-sm movie-action-btn">
                                                    <i class="fas fa-play"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-danger btn-sm movie-action-btn"
                                                        onclick="deleteMovie(<?= $movie['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Logs Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom border-light d-flex justify-content-between align-items-center py-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                            <h5 class="card-title mb-0">System Error Logs</h5>
                            <span class="badge bg-danger ms-2" id="errorCount">0</span>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshLogs()">
                                <i class="fas fa-sync-alt me-1"></i> Refresh
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="clearErrorLogs()">
                                <i class="fas fa-trash-alt me-1"></i> Clear Logs
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="px-3" style="width: 160px;">Time</th>
                                        <th style="width: 100px;">Type</th>
                                        <th>Message</th>
                                        <th style="width: 150px;">File</th>
                                        <th style="width: 80px;">Line</th>
                                        <th style="width: 100px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="errorLogsTable">
                                    <?php
                                    $log_file = '../logs/error.log';
                                    if (file_exists($log_file)) {
                                        $logs = array_reverse(array_slice(file($log_file), -10));
                                        foreach ($logs as $log) {
                                            if (preg_match('/\[(.*?)\] (\w+): (.*?) in (.*?):(\d+)/', $log, $matches)) {
                                                $type = $matches[2];
                                                $typeClass = $type == 'ERROR' ? 'danger' : ($type == 'WARNING' ? 'warning' : 'info');
                                                ?>
                                                <tr class="align-middle">
                                                    <td class="px-3">
                                                        <div class="small text-muted">
                                                            <i class="far fa-clock me-1"></i>
                                                            <?= htmlspecialchars($matches[1]) ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?= $typeClass ?> bg-opacity-75">
                                                            <?= htmlspecialchars($type) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="text-wrap" style="max-width: 500px;">
                                                            <?= htmlspecialchars($matches[3]) ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="far fa-file-code text-muted me-1"></i>
                                                            <span class="small"><?= htmlspecialchars(basename($matches[4])) ?></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary bg-opacity-50">
                                                            <?= htmlspecialchars($matches[5]) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-link btn-sm text-danger p-0" 
                                                                onclick="deleteSingleLog(this)" 
                                                                data-log="<?= htmlspecialchars($log) ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <?php
                                            }
                                        }
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-white border-top border-light py-2">
                        <div class="d-flex justify-content-between align-items-center small text-muted">
                            <div>
                                <i class="fas fa-info-circle me-1"></i>
                                Showing last 10 error logs
                            </div>
                            <div>
                                Auto-refresh: <span id="refreshCounter">30</span>s
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteMovie(id) {
            if (confirm('Are you sure you want to delete this movie?')) {
                fetch('ajax/delete_manual_movie.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error deleting movie: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the movie');
                });
            }
        }

        function clearErrorLogs() {
            if (confirm('Are you sure you want to clear all error logs?')) {
                fetch('ajax/clear_error_logs.php', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('errorLogsTable').innerHTML = '';
                        alert('Error logs cleared successfully');
                    } else {
                        alert('Error clearing logs: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while clearing the logs');
                });
            }
        }

        // Auto refresh error logs every 30 seconds
        setInterval(() => {
            fetch('ajax/get_error_logs.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('errorLogsTable').innerHTML = data.html;
                }
            })
            .catch(error => console.error('Error refreshing logs:', error));
        }, 30000);
    </script>
</body>

<style>
.table > :not(caption) > * > * {
    padding: 0.75rem;
}
.badge {
    font-weight: 500;
}
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.02);
}
.text-wrap {
    word-break: break-word;
}
</style>

<script>
let refreshInterval;
let refreshCounter = 30;

function updateErrorCount() {
    const count = document.querySelectorAll('#errorLogsTable tr').length;
    document.getElementById('errorCount').textContent = count;
}

function refreshLogs() {
    fetch('ajax/get_error_logs.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('errorLogsTable').innerHTML = data.html;
            updateErrorCount();
        }
    })
    .catch(error => console.error('Error refreshing logs:', error));
}

function clearErrorLogs() {
    Swal.fire({
        title: 'Clear Error Logs?',
        text: 'Are you sure you want to clear all error logs?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, clear it!'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('ajax/clear_error_logs.php', {
                method: 'POST',
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('errorLogsTable').innerHTML = '';
                    updateErrorCount();
                    Swal.fire('Cleared!', 'Error logs have been cleared.', 'success');
                } else {
                    Swal.fire('Error!', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('Error!', 'An error occurred while clearing the logs', 'error');
            });
        }
    });
}

function deleteSingleLog(button) {
    const logData = button.dataset.log;
    Swal.fire({
        title: 'Delete Log Entry?',
        text: 'Are you sure you want to delete this log entry?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('ajax/delete_single_log.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ log: logData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    button.closest('tr').remove();
                    updateErrorCount();
                    Swal.fire('Deleted!', 'Log entry has been deleted.', 'success');
                } else {
                    Swal.fire('Error!', data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('Error!', 'An error occurred while deleting the log', 'error');
            });
        }
    });
}

function updateRefreshCounter() {
    document.getElementById('refreshCounter').textContent = refreshCounter;
    refreshCounter--;
    if (refreshCounter < 0) {
        refreshCounter = 30;
        refreshLogs();
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateErrorCount();
    refreshInterval = setInterval(updateRefreshCounter, 1000);
});
</script>
</body>

