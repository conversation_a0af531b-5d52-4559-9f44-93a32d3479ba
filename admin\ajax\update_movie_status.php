<?php
session_start();
require_once '../../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

$data = json_decode(file_get_contents('php://input'), true);
$movie_id = $data['movie_id'] ?? null;
$status = $data['status'] ?? null;

if ($movie_id && $status) {
    $stmt = $db->prepare("UPDATE movies SET status = ? WHERE id = ?");
    $success = $stmt->execute([$status, $movie_id]);
    echo json_encode(['success' => $success]);
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
}