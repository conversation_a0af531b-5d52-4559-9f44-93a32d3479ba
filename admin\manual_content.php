<?php
session_start();
require_once '../includes/init.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';
require_once '../includes/tmdb_handler.php';

$auth = new Auth($db);
$tmdb = new TMDBHandler();

if (!$auth->isAdmin()) {
    header('Location: ../login.php');
    exit;
}

$success = $error = '';

// Handle TMDB Import
if (isset($_POST['import_tmdb'])) {
    try {
        $tmdb_id = $_POST['tmdb_id'];
        $custom_title = trim($_POST['custom_title']); // Optional custom title
        $content_type = $_POST['content_type']; // 'movie' or 'series'
        
        if ($content_type === 'movie') {
            // Get movie details from TMDB
            $movie_data = $tmdb->getMovieById($tmdb_id);
            
            if (!$movie_data) {
                throw new Exception("Movie not found on TMDB");
            }

            $db->beginTransaction();
            
            // Check if movie already exists
            $stmt = $db->prepare("SELECT id FROM manual_movies WHERE tmdb_id = ?");
            $stmt->execute([$tmdb_id]);
            if ($stmt->fetch()) {
                throw new Exception("Movie already exists in database");
            }

            // Generate slug from title (using custom title if provided)
            $title_to_use = !empty($custom_title) ? $custom_title : $movie_data['title'];
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title_to_use)));

            // Insert movie
            $stmt = $db->prepare("
                INSERT INTO manual_movies (
                    tmdb_id, title, original_title, overview, poster_path, 
                    backdrop_path, release_date, runtime, rating, slug, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $tmdb_id,
                $title_to_use,
                $movie_data['original_title'] ?? $movie_data['title'],
                $movie_data['overview'],
                $movie_data['poster_path'],
                $movie_data['backdrop_path'],
                $movie_data['release_date'],
                $movie_data['runtime'] ?? 0,
                $movie_data['vote_average'] ?? 0,
                $slug,
                'active'
            ]);
            
            $db->commit();
            $success = 'Movie imported successfully from TMDB!';
        }
        // TODO: Add series import logic here
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = 'Error importing content: ' . $e->getMessage();
    }
}

// Handle TMDB Search
if (isset($_POST['search_tmdb'])) {
    try {
        $search_query = trim($_POST['search_query']);
        $search_type = $_POST['search_type']; // 'movie' or 'series'
        
        if (empty($search_query)) {
            throw new Exception("Please enter a search term");
        }
        
        $search_results = $search_type === 'movie' ? 
            $tmdb->searchMovies($search_query) : 
            $tmdb->searchTVShows($search_query);
        
    } catch (Exception $e) {
        $error = 'Search error: ' . $e->getMessage();
    }
}

$page_title = "Manual Content Management";
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Manual Content Management</h5>
                    <div>
                        <a href="add_manual_movie.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add Movie
                        </a>
                        <a href="add_manual_series.php" class="btn btn-success btn-sm">
                            <i class="fas fa-plus"></i> Add Series
                        </a>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <?php if ($success): ?>
                    <div class="alert alert-success m-3"><?php echo $success; ?></div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger m-3"><?php echo $error; ?></div>
                <?php endif; ?>

                <!-- TMDB Import Section -->
                <div class="card-body border-bottom">
                    <div class="row">
                        <!-- TMDB ID Import Form -->
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Import by TMDB ID</h6>
                                    <form method="POST">
                                        <div class="mb-2">
                                            <label class="form-label">TMDB ID</label>
                                            <input type="number" name="tmdb_id" id="tmdb_id" class="form-control form-control-sm" required>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label">Content Type</label>
                                            <select name="content_type" class="form-select form-select-sm">
                                                <option value="movie">Movie</option>
                                                <option value="series">TV Series</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label">Custom Title (Optional)</label>
                                            <input type="text" name="custom_title" class="form-control form-control-sm">
                                        </div>
                                        <button type="submit" name="import_tmdb" class="btn btn-primary btn-sm">Import</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- TMDB Search Form -->
                        <div class="col-md-8">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Search TMDB</h6>
                                    <form method="POST" class="mb-3">
                                        <div class="row g-2">
                                            <div class="col">
                                                <input type="text" name="search_query" class="form-control form-control-sm" placeholder="Enter title to search..." required>
                                            </div>
                                            <div class="col-auto">
                                                <select name="search_type" class="form-select form-select-sm">
                                                    <option value="movie">Movie</option>
                                                    <option value="series">TV Series</option>
                                                </select>
                                            </div>
                                            <div class="col-auto">
                                                <button type="submit" name="search_tmdb" class="btn btn-primary btn-sm">Search</button>
                                            </div>
                                        </div>
                                    </form>

                                    <!-- Search Results -->
                                    <?php if (isset($search_results) && !empty($search_results)): ?>
                                        <div class="search-results" style="max-height: 300px; overflow-y: auto;">
                                            <div class="list-group">
                                                <?php foreach ($search_results as $result): ?>
                                                    <div class="list-group-item list-group-item-action d-flex align-items-center gap-2 py-2">
                                                        <img src="https://image.tmdb.org/t/p/w92<?php echo $result['poster_path']; ?>" 
                                                             class="movie-poster-small" 
                                                             alt="<?php echo htmlspecialchars($result['title'] ?? $result['name']); ?>"
                                                             onerror="this.src='../assets/images/default-poster.jpg'"
                                                             style="width: 45px; height: 68px; object-fit: cover;">
                                                        <div class="flex-grow-1">
                                                            <h6 class="mb-0" style="font-size: 0.9rem;">
                                                                <?php echo htmlspecialchars($result['title'] ?? $result['name']); ?>
                                                            </h6>
                                                            <small class="text-muted">
                                                                TMDB ID: <?php echo $result['id']; ?> | 
                                                                <?php echo $result['release_date'] ?? $result['first_air_date']; ?>
                                                            </small>
                                                        </div>
                                                        <button type="button" class="btn btn-sm btn-success" 
                                                                onclick="fillTmdbId(<?php echo $result['id']; ?>)">
                                                            Select
                                                        </button>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Tabs -->
                <div class="card-body">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#movies">Movies</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#series">Series</a>
                        </li>
                    </ul>

                    <div class="tab-content mt-3">
                        <!-- Movies Tab -->
                        <div id="movies" class="tab-pane active">
                            <div class="table-responsive">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th>Actions</th>
                                            <th>Title</th>
                                            <th>Release Date</th>
                                            <th>Status</th>
                                            <th>Created At</th>
                                        </tr>
                                    </thead>
                                    <tbody id="manualMoviesTable"></tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Series Tab -->
                        <div id="series" class="tab-pane fade">
                            <div class="table-responsive">
                                <table class="table align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th>Actions</th>
                                            <th>Title</th>
                                            <th>Seasons</th>
                                            <th>Status</th>
                                            <th>Created At</th>
                                        </tr>
                                    </thead>
                                    <tbody id="manualSeriesTable"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function fillTmdbId(id) {
    document.getElementById('tmdb_id').value = id;
    // Optionally scroll to the TMDB ID input
    document.getElementById('tmdb_id').scrollIntoView({ behavior: 'smooth' });
}
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadManualContent('movies');
    loadManualContent('series');

    // Tab change event
    document.querySelectorAll('a[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('href').replace('#', '');
            loadManualContent(target);
        });
    });
});

function showLoading(tableId) {
    const table = document.getElementById(tableId);
    table.innerHTML = `
        <tr>
            <td colspan="5" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </td>
        </tr>
    `;
}

function loadManualContent(type) {
    const tableId = `manual${type.charAt(0).toUpperCase() + type.slice(1)}Table`;
    showLoading(tableId);
    
    fetch(`ajax/get_manual_content.php?type=${type}`)
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';
            
            if (data.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            No ${type} found
                        </td>
                    </tr>
                `;
                return;
            }
            
            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td data-label="Actions" class="action-buttons">
                        <button onclick="editContent('${type}', ${item.id})" class="btn btn-info btn-sm" title="Edit">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button onclick="deleteContent('${type}', ${item.id})" class="btn btn-danger btn-sm" title="Delete">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </td>
                    <td data-label="Title">${item.title}</td>
                    <td data-label="Release Date">${item.release_date || ''}</td>
                    <td data-label="Status">
                        <span class="badge bg-${item.status === 'active' ? 'success' : 'danger'}">${item.status}</span>
                    </td>
                    <td data-label="Created At">${item.created_at}</td>
                `;
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById(tableId).innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4 text-danger">
                        Error loading content. Please try again.
                    </td>
                </tr>
            `;
        });
}

function editContent(type, id) {
    window.location.href = `edit_manual_${type.slice(0, -1)}.php?id=${id}`;
}

function deleteContent(type, id) {
    if (confirm('Are you sure you want to delete this content?')) {
        fetch('ajax/delete_manual_content.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: type,
                id: id
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadManualContent(type);
            } else {
                alert('Error deleting content: ' + data.message);
            }
        })
        .catch(error => console.error('Error:', error));
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
