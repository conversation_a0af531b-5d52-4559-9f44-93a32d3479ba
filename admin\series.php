<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';

// Enable error reporting at the top
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering
ob_start();

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$tmdb = new TMDBHandler();
$error = '';
$success = '';
$tmdb_results = [];
$search_query = '';

// Handle TMDB Search
if (isset($_POST['tmdb_search'])) {
    try {
        $search_query = trim($_POST['tmdb_query']);
        
        if (empty($search_query)) {
            throw new Exception("Search query cannot be empty");
        }

        if (strlen($search_query) < 2) {
            throw new Exception("Search query must be at least 2 characters long");
        }

        // Log the search attempt
        error_log("Attempting TMDB search for: " . $search_query);
        
        // Perform the search with error checking
        $tmdb_results = $tmdb->searchTV($search_query);
        
        if ($tmdb_results === false || !is_array($tmdb_results)) {
            throw new Exception("Invalid response from TMDB API");
        }
        
        // Log the results count
        error_log("Found " . count($tmdb_results) . " results from TMDB");
        
    } catch (Exception $e) {
        $error = "Search error: " . $e->getMessage();
        error_log("TMDB Search Error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
    }
}

// Handle series import
if (isset($_POST['import_tmdb'])) {
    $tmdb_id = $_POST['tmdb_id'];
    try {
        $show_data = $tmdb->getTVShowById($tmdb_id);
        
        if ($show_data) {
            // Debug show data
            error_log('Show Data: ' . print_r($show_data, true));
            
            // Check if series already exists
            $stmt = $db->prepare("SELECT id FROM series WHERE tmdb_id = ?");
            $stmt->execute([$tmdb_id]);
            
            if (!$stmt->fetch()) {
                $db->beginTransaction();
                
                try {
                    // Generate slug from title
                    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $show_data['name'])));
                    
                    // Insert series
                    $sql = "INSERT INTO series (tmdb_id, title, original_title, slug, overview, poster_path, backdrop_path, first_air_date, status) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([
                        $show_data['id'],
                        $show_data['name'],
                        $show_data['original_name'],
                        $slug,
                        $show_data['overview'],
                        $show_data['poster_path'],
                        $show_data['backdrop_path'],
                        $show_data['first_air_date']
                    ]);
                    
                    $series_id = $db->lastInsertId();
                    error_log('Inserted Series ID: ' . $series_id);
                    
                    // Import all seasons
                    if (isset($show_data['number_of_seasons'])) {
                        error_log('Number of seasons: ' . $show_data['number_of_seasons']);
                        
                        for ($season_num = 1; $season_num <= $show_data['number_of_seasons']; $season_num++) {
                            $season_data = $tmdb->getTVSeasonById($tmdb_id, $season_num);
                            error_log('Season ' . $season_num . ' Data: ' . print_r($season_data, true));
                            
                            if ($season_data && isset($season_data['episodes'])) {
                                // Insert season
                                $stmt = $db->prepare("
                                    INSERT INTO seasons (series_id, tmdb_id, season_number, name, overview, poster_path, air_date) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?)
                                ");
                                
                                try {
                                    $stmt->execute([
                                        $series_id,
                                        $season_data['id'],
                                        $season_num,
                                        $season_data['name'],
                                        $season_data['overview'],
                                        $season_data['poster_path'],
                                        $season_data['air_date']
                                    ]);
                                    
                                    $season_id = $db->lastInsertId();
                                    error_log('Inserted Season ID: ' . $season_id);
                                    
                                    // Insert episodes
                                    foreach ($season_data['episodes'] as $episode) {
                                        error_log('Processing Episode: ' . print_r($episode, true));
                                        
                                        try {
                                            $stmt = $db->prepare("
                                                INSERT INTO episodes (
                                                    series_id, season_id, tmdb_id, season_number, 
                                                    episode_number, title, overview, still_path, 
                                                    air_date, status
                                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
                                            ");
                                            
                                            $stmt->execute([
                                                $series_id,
                                                $season_id,
                                                $episode['id'],
                                                $season_num,
                                                $episode['episode_number'],
                                                $episode['name'],
                                                $episode['overview'],
                                                $episode['still_path'],
                                                $episode['air_date']
                                            ]);
                                            
                                            $episode_id = $db->lastInsertId();
                                            error_log('Inserted Episode ID: ' . $episode_id);
                                            
                                        } catch (Exception $e) {
                                            error_log('Episode Insert Error: ' . $e->getMessage());
                                            throw $e;
                                        }
                                    }
                                } catch (Exception $e) {
                                    error_log('Season Insert Error: ' . $e->getMessage());
                                    throw $e;
                                }
                            } else {
                                error_log('No episodes found for season ' . $season_num);
                            }
                        }
                    } else {
                        error_log('No seasons found in show data');
                    }
                    
                    $db->commit();
                    $success = "Series '{$show_data['name']}' imported successfully with all seasons and episodes";
                    
                } catch (Exception $e) {
                    $db->rollBack();
                    error_log('Import Error: ' . $e->getMessage());
                    $error = "Import error: " . $e->getMessage();
                }
            } else {
                $error = "Series already exists in database";
            }
        } else {
            $error = "Could not fetch series data from TMDB";
        }
    } catch (Exception $e) {
        error_log('TMDB API Error: ' . $e->getMessage());
        $error = "TMDB API error: " . $e->getMessage();
    }
}

// Get existing series with error handling
try {
    $stmt = $db->query("SELECT * FROM series ORDER BY created_at DESC");
    $series = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    error_log("Database Error: " . $e->getMessage());
    $series = [];
}

$page_title = 'Manage Series';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Search & Import Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">Import New Series</h5>
                        </div>
                        <div class="col-md-6">
                            <form method="get" class="mb-0">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" placeholder="Search TMDB..." value="<?php echo htmlspecialchars($search_query ?? ''); ?>">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($tmdb_results)): ?>
                <div class="card-body border-top">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 80px;">Poster</th>
                                    <th>Title</th>
                                    <th>Release Date</th>
                                    <th style="width: 100px;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tmdb_results as $result): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($result['poster_path'])): ?>
                                            <img src="https://image.tmdb.org/t/p/w92<?php echo htmlspecialchars($result['poster_path']); ?>" 
                                                 class="img-fluid rounded shadow-sm"
                                                 alt="<?php echo htmlspecialchars($result['name']); ?>">
                                        <?php else: ?>
                                            <div class="no-poster">
                                                <i class="fas fa-film"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($result['name']); ?></h6>
                                        <?php if (!empty($result['overview'])): ?>
                                            <small class="text-muted d-block text-truncate" style="max-width: 500px;">
                                                <?php echo htmlspecialchars($result['overview']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($result['first_air_date'] ?? 'N/A'); ?></td>
                                    <td>
                                        <form method="post">
                                            <input type="hidden" name="tmdb_id" value="<?php echo htmlspecialchars($result['id']); ?>">
                                            <button type="submit" name="import_tmdb" class="btn btn-sm btn-success">
                                                <i class="fas fa-download me-1"></i> Import
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Existing Series Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">Existing Series</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="seriesSearch" class="form-control" placeholder="Search existing series...">
                                <span class="input-group-text bg-white">
                                    <i class="fas fa-search"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0" id="seriesTable">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 80px;">Poster</th>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Added Date</th>
                                    <th style="width: 150px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($series as $show): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($show['poster_path'])): ?>
                                            <img src="https://image.tmdb.org/t/p/w92<?php echo htmlspecialchars($show['poster_path']); ?>" 
                                                 class="img-fluid rounded shadow-sm"
                                                 alt="<?php echo htmlspecialchars($show['title']); ?>">
                                        <?php else: ?>
                                            <div class="no-poster rounded bg-light">
                                                <i class="fas fa-film"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($show['title']); ?></h6>
                                    </td>
                                    <td>
                                        <span class="badge rounded-pill bg-<?php echo $show['status'] === 'active' ? 'success' : 'danger'; ?>">
                                            <?php echo ucfirst($show['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($show['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="edit_series.php?id=<?php echo $show['id']; ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-outline-danger"
                                                    onclick="deleteSeries(<?php echo $show['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --primary-color: #4e73df;
    --success-color: #1cc88a;
    --danger-color: #e74a3b;
}

.card {
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,.125);
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.no-poster {
    width: 60px;
    height: 90px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.no-poster i {
    font-size: 1.5rem;
    color: #adb5bd;
}

.img-fluid {
    width: 60px;
    height: 90px;
    object-fit: cover;
}

.badge {
    padding: 0.5em 1em;
    font-weight: 500;
}

.btn-group-sm > .btn {
    padding: 0.4rem 0.8rem;
}

.input-group {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.input-group .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(78,115,223,0.05);
}

@media (max-width: 768px) {
    .card-header .row > div:not(:last-child) {
        margin-bottom: 1rem;
    }
}
</style>

<script>
function deleteSeries(seriesId) {
    if (confirm('Are you sure you want to delete this series?')) {
        window.location.href = `delete_series.php?id=${seriesId}`;
    }
}

document.getElementById('seriesSearch').addEventListener('keyup', function() {
    const searchText = this.value.toLowerCase();
    const table = document.getElementById('seriesTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let row of rows) {
        const titleCell = row.getElementsByTagName('td')[1];
        const title = titleCell.textContent || titleCell.innerText;
        
        row.style.display = title.toLowerCase().includes(searchText) ? '' : 'none';
    }
});
</script>

<?php 
// Flush output buffer
ob_end_flush();
require_once 'includes/footer.php'; 
require_once 'includes/footer.php'; ?>
