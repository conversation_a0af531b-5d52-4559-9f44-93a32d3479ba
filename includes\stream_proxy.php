<?php
session_start();
require_once 'auth.php';
require_once 'db.php';

$auth = new Auth($db);
if (!$auth->isLoggedIn()) {
    die('Unauthorized');
}

// Get target URL from request
$url = isset($_GET['url']) ? base64_decode($_GET['url']) : '';
if (empty($url)) {
    die('Invalid URL');
}

// Initialize cURL
$ch = curl_init();

// Set Edgard DNS headers
$edgard_headers = [
    'Accept: */*',
    'Accept-Language: en-US,en;q=0.9',
    'Origin: https://edgard.pro',
    'Referer: https://edgard.pro/',
    'sec-ch-ua: "Google Chrome";v="119"',
    'sec-ch-ua-mobile: ?0',
    'sec-ch-ua-platform: "Windows"',
    'Sec-Fetch-Dest: empty',
    'Sec-Fetch-Mode: cors',
    'Sec-Fetch-Site: cross-site',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
];

// Add Range header if present in request
if (isset($_SERVER['HTTP_RANGE'])) {
    $edgard_headers[] = 'Range: ' . $_SERVER['HTTP_RANGE'];
}

// Set cURL options
curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HEADER => true,
    CURLOPT_NOBODY => false,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_HTTPHEADER => $edgard_headers,
    CURLOPT_ENCODING => 'gzip, deflate',
    CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
    CURLOPT_DNS_SERVERS => '*******,*******' // Cloudflare and Google DNS
]);

// Execute cURL request
$response = curl_exec($ch);

if (curl_errno($ch)) {
    error_log('Curl error: ' . curl_error($ch));
    die('Stream error occurred');
}

// Get response info
$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $header_size);
$body = substr($response, $header_size);
$status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

curl_close($ch);

// Parse and forward necessary headers
$header_lines = explode("\n", $headers);
foreach ($header_lines as $header) {
    $header = trim($header);
    if (empty($header)) continue;
    
    // Forward important headers
    if (preg_match('/^(Content-Type|Content-Range|Content-Length|Accept-Ranges|Cache-Control):/i', $header)) {
        header($header);
    }
}

// Set CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Range');

// Set response code
http_response_code($status_code);

// Output content
echo $body;
