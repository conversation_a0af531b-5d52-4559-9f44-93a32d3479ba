<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = "Import from JSON";
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6>Import Movies from JSON</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        This tool will import movies from movie.json file using TMDB IDs.
                    </div>
                    
                    <button id="startImport" class="btn btn-primary">
                        Start Import
                    </button>

                    <div id="progress" class="mt-4 d-none">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="status" class="mt-2"></div>
                    </div>

                    <div id="results" class="mt-4">
                        <div id="successList"></div>
                        <div id="skippedList"></div>
                        <div id="failedList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('startImport').addEventListener('click', async function() {
    const button = this;
    const progress = document.getElementById('progress');
    const status = document.getElementById('status');
    const results = document.getElementById('results');

    button.disabled = true;
    progress.classList.remove('d-none');
    status.textContent = 'Importing movies...';

    try {
        const response = await fetch('scripts/import_from_json.php');
        const data = await response.json();

        if (data.error) {
            throw new Error(data.message);
        }

        // Display results
        status.innerHTML = `<div class="alert alert-success">${data.message}</div>`;

        // Show detailed results
        let html = '';
        
        if (data.details.success.length > 0) {
            html += '<h6 class="text-success">Successfully Imported:</h6>';
            html += '<ul>';
            data.details.success.forEach(movie => {
                html += `<li>${movie.title} (TMDB: ${movie.tmdb_id})</li>`;
            });
            html += '</ul>';
        }

        if (data.details.skipped.length > 0) {
            html += '<h6 class="text-warning">Skipped:</h6>';
            html += '<ul>';
            data.details.skipped.forEach(movie => {
                html += `<li>${movie.title} - ${movie.reason}</li>`;
            });
            html += '</ul>';
        }

        if (data.details.failed.length > 0) {
            html += '<h6 class="text-danger">Failed:</h6>';
            html += '<ul>';
            data.details.failed.forEach(movie => {
                html += `<li>${movie.title} - ${movie.reason}</li>`;
            });
            html += '</ul>';
        }

        results.innerHTML = html;

    } catch (error) {
        status.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
    } finally {
        button.disabled = false;
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>