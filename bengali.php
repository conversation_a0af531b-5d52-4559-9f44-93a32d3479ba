<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';
$auth = new Auth($db);

$page_title = 'বাংলা মুভি ও সিরিজ';
require_once 'includes/header.php';

// Fetch Bengali content with pagination
$items_per_page = 24;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $items_per_page;

try {
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM manual_movies WHERE is_bengali = 1 AND status = 'active'";
    $total_items = $db->query($count_query)->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_items / $items_per_page);

    // Get Bengali movies
    $bengali_query = "SELECT *,
        CASE 
            WHEN poster_path LIKE 'http%' THEN poster_path 
            WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
            ELSE 'assets/images/default-poster.jpg'
        END as poster_path
        FROM manual_movies 
        WHERE is_bengali = 1 
        AND status = 'active'
        ORDER BY created_at DESC 
        LIMIT :limit OFFSET :offset";

    $stmt = $db->prepare($bengali_query);
    $stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $bengali_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching Bengali content: " . $e->getMessage());
    $bengali_movies = [];
}
?>

<div class="container py-4">
    <div class="section-header d-flex justify-content-between align-items-center mb-4">
        <h1 class="section-title">বাংলা মুভি ও সিরিজ</h1>
    </div>

    <?php if (!empty($bengali_movies)): ?>
        <div class="row row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-lg-6 g-4">
            <?php foreach($bengali_movies as $movie): ?>
                <div class="col">
                    <div class="movie-card">
                        <span class="content-type-badge">Movie</span>
                        <a href="manual_player.php?id=<?php echo $movie['id']; ?>" class="poster-link">
                            <div class="poster-wrapper">
                                <img src="<?php echo htmlspecialchars($movie['poster_path']); ?>" 
                                     class="movie-poster" 
                                     alt="<?php echo htmlspecialchars($movie['title']); ?>"
                                     loading="lazy"
                                     onerror="this.src='assets/images/default-poster.jpg'">
                                <div class="poster-overlay">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                            </div>
                            <div class="movie-info">
                                <h3 class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></h3>
                                <?php if($movie['release_date']): ?>
                                    <div class="movie-year"><?php echo date('Y', strtotime($movie['release_date'])); ?></div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page - 1; ?>">Previous</a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page + 1; ?>">Next</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php else: ?>
        <div class="alert alert-info">
            কোন বাংলা কনটেন্ট পাওয়া যায়নি।
        </div>
    <?php endif; ?>
</div>

<style>
.movie-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
    background: #333;
    height: 100%;
}

.movie-card:hover {
    transform: scale(1.05);
}

.poster-wrapper {
    position: relative;
    aspect-ratio: 2/3;
    overflow: hidden;
}

.movie-poster {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.poster-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-card:hover .poster-overlay {
    opacity: 1;
}

.poster-overlay i {
    font-size: 3rem;
    color: white;
}

.movie-info {
    padding: 10px;
}

.movie-title {
    color: white;
    font-size: 0.9rem;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.movie-year {
    color: #aaa;
    font-size: 0.8rem;
}

.content-type-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1;
}

.section-title {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 0;
}

.pagination .page-link {
    background-color: #333;
    border-color: #444;
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--netflix-red);
    border-color: var(--netflix-red);
}

.pagination .page-link:hover {
    background-color: #444;
    border-color: #555;
    color: white;
}
</style>

<?php require_once 'includes/footer.php'; ?>
