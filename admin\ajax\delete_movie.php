<?php
// Clear any previous output
ob_clean();

// Start fresh session
session_start();

// Include database
require_once '../../includes/db.php';

// Set JSON header
header('Content-Type: application/json');

// Check admin session
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get POST data
$input = file_get_contents('php://input');
$data = json_decode($input, true);
$movie_id = $data['movie_id'] ?? null;

// Validate movie_id
if (!$movie_id || !is_numeric($movie_id)) {
    echo json_encode(['success' => false, 'message' => 'Invalid movie ID']);
    exit;
}

try {
    // Start transaction
    $db->beginTransaction();
    
    // Delete from movie_servers
    $stmt = $db->prepare("DELETE FROM movie_servers WHERE movie_id = ?");
    $stmt->execute([$movie_id]);
    
    // Delete from movies
    $stmt = $db->prepare("DELETE FROM movies WHERE id = ?");
    $stmt->execute([$movie_id]);
    
    // Commit transaction
    $db->commit();
    
    echo json_encode(['success' => true, 'message' => 'Movie deleted successfully']);
    
} catch (Exception $e) {
    // Rollback on error
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Error deleting movie: ' . $e->getMessage()
    ]);
}

// Ensure no additional output
exit;

