<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
    header('Location: users.php');
    exit;
}

// Get user details with admin info
$stmt = $db->prepare("
    SELECT 
        u.*,
        a.id as admin_id,
        a.role as admin_role,
        COALESCE(
            (SELECT end_date 
             FROM user_subscriptions 
             WHERE user_id = u.id 
             AND end_date > NOW() 
             ORDER BY end_date DESC 
             LIMIT 1
            ), 
            NULL
        ) as subscription_end
    FROM users u 
    LEFT JOIN admins a ON u.id = a.user_id 
    WHERE u.id = ?
");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    header('Location: users.php');
    exit;
}

// Get subscription plans
$plans = $db->query("SELECT * FROM subscriptions WHERE status = 'active'")->fetchAll(PDO::FETCH_ASSOC);

// Handle user update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_user'])) {
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $status = $_POST['status'];
        $new_password = trim($_POST['new_password']);

        try {
            $db->beginTransaction();

            // Update basic info
            $sql = "UPDATE users SET username = ?, email = ?, status = ? WHERE id = ?";
            $params = [$username, $email, $status, $user_id];

            // If new password is provided
            if (!empty($new_password)) {
                $sql = "UPDATE users SET username = ?, email = ?, status = ?, password = ? WHERE id = ?";
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $params = [$username, $email, $status, $hashed_password, $user_id];
            }

            $stmt = $db->prepare($sql);
            $stmt->execute($params);

            // Handle admin role
            if (isset($_POST['is_admin']) && $_POST['is_admin'] == '1') {
                if (!$user['admin_id']) {
                    // Create new admin
                    $stmt = $db->prepare("
                        INSERT INTO admins (user_id, username, email, password, role, status)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user_id,
                        $username,
                        $email,
                        $hashed_password ?? $user['password'],
                        $_POST['admin_role'],
                        'active'
                    ]);
                } else {
                    // Update existing admin
                    $stmt = $db->prepare("
                        UPDATE admins 
                        SET username = ?, email = ?, role = ?
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$username, $email, $_POST['admin_role'], $user_id]);
                }
            } else if ($user['admin_id']) {
                // Remove admin role
                $stmt = $db->prepare("DELETE FROM admins WHERE user_id = ?");
                $stmt->execute([$user_id]);
            }

            $db->commit();
            $success = "User updated successfully";
            
            // Refresh user data
            $stmt = $db->prepare("
                SELECT 
                    u.*,
                    a.id as admin_id,
                    a.role as admin_role,
                    COALESCE(
                        (SELECT end_date 
                         FROM user_subscriptions 
                         WHERE user_id = u.id 
                         AND end_date > NOW() 
                         ORDER BY end_date DESC 
                         LIMIT 1
                        ), 
                        NULL
                    ) as subscription_end
                FROM users u 
                LEFT JOIN admins a ON u.id = a.user_id 
                WHERE u.id = ?
            ");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            $db->rollBack();
            $error = "Error updating user: " . $e->getMessage();
        }
    }

    // Handle subscription update
    if (isset($_POST['update_subscription'])) {
        $plan_id = (int)$_POST['plan_id'];
        $duration_days = (int)$_POST['duration_days'];

        try {
            $end_date = date('Y-m-d H:i:s', strtotime("+{$duration_days} days"));
            
            $stmt = $db->prepare("
                INSERT INTO user_subscriptions (user_id, plan_id, start_date, end_date, status)
                VALUES (?, ?, NOW(), ?, 'active')
            ");
            $stmt->execute([$user_id, $plan_id, $end_date]);
            
            $success = "Subscription updated successfully";
            
            // Refresh subscription end date
            $user['subscription_end'] = $end_date;
        } catch (PDOException $e) {
            $error = "Error updating subscription: " . $e->getMessage();
        }
    }
}

$page_title = 'Edit User';
require_once 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Edit User</h1>
    
    <?php if (isset($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    
    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-user me-1"></i>
                    User Information
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-control" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">New Password</label>
                            <input type="password" class="form-control" name="new_password" placeholder="Leave blank to keep current password">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status">
                                <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="banned" <?php echo $user['status'] === 'banned' ? 'selected' : ''; ?>>Banned</option>
                                <option value="pending" <?php echo $user['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_admin" value="1" id="isAdmin" 
                                    <?php echo $user['admin_id'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="isAdmin">
                                    Is Admin
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="adminRoleDiv" style="display: <?php echo $user['admin_id'] ? 'block' : 'none'; ?>">
                            <label class="form-label">Admin Role</label>
                            <select class="form-select" name="admin_role">
                                <option value="admin" <?php echo $user['admin_role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                <option value="super_admin" <?php echo $user['admin_role'] === 'super_admin' ? 'selected' : ''; ?>>Super Admin</option>
                                <option value="moderator" <?php echo $user['admin_role'] === 'moderator' ? 'selected' : ''; ?>>Moderator</option>
                            </select>
                        </div>
                        
                        <button type="submit" name="update_user" class="btn btn-primary">Update User</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-crown me-1"></i>
                    Subscription Information
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Subscription:</strong>
                        <?php if ($user['subscription_end']): ?>
                            Active until <?php echo date('M d, Y', strtotime($user['subscription_end'])); ?>
                        <?php else: ?>
                            No active subscription
                        <?php endif; ?>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Subscription Plan</label>
                            <select class="form-select" name="plan_id" required>
                                <?php foreach ($plans as $plan): ?>
                                    <option value="<?php echo $plan['id']; ?>" data-duration="<?php echo $plan['duration_days']; ?>">
                                        <?php echo htmlspecialchars($plan['name']); ?> 
                                        (<?php echo $plan['duration_days']; ?> days)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Duration (days)</label>
                            <input type="number" class="form-control" name="duration_days" value="30" min="1" required>
                        </div>
                        
                        <button type="submit" name="update_subscription" class="btn btn-success">Update Subscription</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('isAdmin').addEventListener('change', function() {
    document.getElementById('adminRoleDiv').style.display = this.checked ? 'block' : 'none';
});

// Update duration based on selected plan
document.querySelector('select[name="plan_id"]').addEventListener('change', function() {
    const duration = this.options[this.selectedIndex].getAttribute('data-duration');
    document.querySelector('input[name="duration_days"]').value = duration;
});
</script>

<?php require_once 'includes/footer.php'; ?>