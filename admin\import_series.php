<?php
session_start();
ini_set('max_execution_time', 0);
ini_set('memory_limit', '256M');
set_time_limit(0);

require_once '../includes/db.php';
require_once '../includes/tmdb_handler.php';

if (!isset($_SESSION['admin_id'])) {
    exit(json_encode(['error' => true, 'message' => 'Unauthorized']));
}

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

function sendProgress($message, $progress = null, $error = false) {
    $data = ['message' => $message];
    if ($progress !== null) $data['progress'] = $progress;
    if ($error) $data['error'] = true;
    echo json_encode($data) . "\n";
    ob_flush();
    flush();
}

function importSeries($tmdb, $db, $series_data) {
    try {
        // First, check if this is actually a TV show
        $check_media = $tmdb->getMediaType($series_data['tmdb']);
        if ($check_media !== 'tv') {
            return "Skipped: " . $series_data['title'] . " - Not a TV show";
        }

        // Get detailed series info from TMDB
        $tmdb_details = $tmdb->getTVShowById($series_data['tmdb']);
        
        if (!$tmdb_details) {
            return "Failed to fetch TMDB data for: " . $series_data['title'];
        }

        // Debug log
        error_log("Processing series: " . $series_data['title']);

        $db->beginTransaction();

        try {
            // Generate slug
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $tmdb_details['name'])));
            
            // Insert series
            $stmt = $db->prepare("
                INSERT INTO series (
                    tmdb_id, 
                    title,
                    original_title,
                    slug,
                    overview,
                    poster_path,
                    backdrop_path,
                    first_air_date,
                    status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $tmdb_details['id'],
                $tmdb_details['name'],
                $tmdb_details['original_name'] ?? $tmdb_details['name'],
                $slug,
                $tmdb_details['overview'],
                $tmdb_details['poster_path'] ?? null,
                $tmdb_details['backdrop_path'] ?? null,
                $tmdb_details['first_air_date'],
                $tmdb_details['status'] ?? 'Released'
            ]);

            $series_id = $db->lastInsertId();

            // Filter out season 0 and sort seasons
            $valid_seasons = array_filter($tmdb_details['seasons'], function($season) {
                return isset($season['season_number']) && $season['season_number'] > 0;
            });

            usort($valid_seasons, function($a, $b) {
                return $a['season_number'] - $b['season_number'];
            });

            if (empty($valid_seasons)) {
                $db->rollBack();
                return "No valid seasons found for: " . $series_data['title'];
            }

            foreach ($valid_seasons as $season) {
                error_log("Processing season " . $season['season_number']);
                
                // Get detailed season info
                $season_details = $tmdb->getTVSeasonById($tmdb_details['id'], $season['season_number']);
                
                if (!$season_details || empty($season_details['episodes'])) {
                    error_log("Skipping season " . $season['season_number'] . " - No valid data");
                    continue;
                }

                // Insert season
                $stmt = $db->prepare("
                    INSERT INTO seasons (
                        series_id,
                        season_number,
                        name,
                        overview,
                        poster_path,
                        air_date
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $series_id,
                    $season['season_number'],
                    $season['name'] ?? "Season " . $season['season_number'],
                    $season['overview'] ?? null,
                    $season['poster_path'] ?? null,
                    $season['air_date'] ?? null
                ]);

                $season_id = $db->lastInsertId();

                // Filter and validate episodes
                $valid_episodes = array_filter($season_details['episodes'], function($episode) {
                    return isset($episode['episode_number']) && 
                           $episode['episode_number'] > 0 && 
                           !empty($episode['name']);
                });

                // Sort episodes by episode number
                usort($valid_episodes, function($a, $b) {
                    return $a['episode_number'] - $b['episode_number'];
                });

                foreach ($valid_episodes as $episode) {
                    // Check for duplicate episode
                    $check_stmt = $db->prepare("
                        SELECT id FROM episodes 
                        WHERE series_id = ? 
                        AND season_number = ? 
                        AND episode_number = ?
                    ");
                    
                    $check_stmt->execute([
                        $series_id,
                        $season['season_number'],
                        $episode['episode_number']
                    ]);

                    if ($check_stmt->fetch()) {
                        error_log("Skipping duplicate episode: Season " . $season['season_number'] . " Episode " . $episode['episode_number']);
                        continue;
                    }

                    // Insert episode
                    $stmt = $db->prepare("
                        INSERT INTO episodes (
                            series_id,
                            season_id,
                            tmdb_id,
                            season_number,
                            episode_number,
                            title,
                            overview,
                            still_path,
                            air_date,
                            status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
                    ");

                    $stmt->execute([
                        $series_id,
                        $season_id,
                        $episode['id'],
                        $season['season_number'],
                        $episode['episode_number'],
                        $episode['name'],
                        $episode['overview'] ?? null,
                        $episode['still_path'] ?? null,
                        $episode['air_date'] ?? null
                    ]);
                }
            }

            $db->commit();
            return "Successfully imported: " . $series_data['title'];

        } catch (Exception $e) {
            $db->rollBack();
            error_log("Import error for " . $series_data['title'] . ": " . $e->getMessage());
            throw $e;
        }
    } catch (Exception $e) {
        error_log("Import error: " . $e->getMessage());
        throw $e;
    }
}

try {
    $tmdb = new TMDBHandler();
    $series_json = file_get_contents('../data/series.json');
    $series = json_decode($series_json, true) ?? [];
    
    $total = count($series);
    if ($total === 0) {
        sendProgress("No series found to import", 100, true);
        exit;
    }

    $stats = [
        'success' => 0,
        'skipped' => 0,
        'failed' => 0
    ];

    // Process in batches of 3
    $batch_size = 3;
    $batches = array_chunk($series, $batch_size);
    
    foreach ($batches as $batch_index => $batch) {
        foreach ($batch as $index => $show) {
            $overall_index = ($batch_index * $batch_size) + $index;
            $progress = ($overall_index + 1) / $total * 100;
            
            try {
                $result = importSeries($tmdb, $db, $show);
                
                if (strpos($result, "Successfully") === 0) {
                    $stats['success']++;
                } elseif (strpos($result, "Skipped") === 0) {
                    $stats['skipped']++;
                } else {
                    $stats['failed']++;
                }
                
                sendProgress($result, $progress);
                
            } catch (Exception $e) {
                $stats['failed']++;
                sendProgress("Error importing " . $show['title'] . ": " . $e->getMessage(), $progress, true);
            }
        }
        
        // Add delay between batches
        sleep(2);
    }

    // Send final statistics
    sendProgress("Import completed. Success: {$stats['success']}, Skipped: {$stats['skipped']}, Failed: {$stats['failed']}", 100);

} catch (Exception $e) {
    sendProgress("Import process failed: " . $e->getMessage(), null, true);
}
