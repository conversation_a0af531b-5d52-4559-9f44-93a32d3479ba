<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$error = $success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_subscription'])) {
        $name = trim($_POST['name']);
        $slug = strtolower(str_replace(' ', '-', $name));
        $description = trim($_POST['description']);
        $price = (float)$_POST['price'];
        $duration_days = (int)$_POST['duration_days'];
        $features = json_encode($_POST['features']);
        $status = $_POST['status'];

        try {
            $stmt = $db->prepare("INSERT INTO subscriptions (name, slug, description, price, duration_days, features, status) 
                                 VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$name, $slug, $description, $price, $duration_days, $features, $status]);
            $success = "Subscription plan added successfully";
        } catch (PDOException $e) {
            $error = "Error: " . $e->getMessage();
        }
    }

    if (isset($_POST['update_subscription'])) {
        $id = (int)$_POST['id'];
        $name = trim($_POST['name']);
        $slug = strtolower(str_replace(' ', '-', $name));
        $description = trim($_POST['description']);
        $price = (float)$_POST['price'];
        $duration_days = (int)$_POST['duration_days'];
        $features = json_encode($_POST['features']);
        $status = $_POST['status'];

        try {
            $stmt = $db->prepare("UPDATE subscriptions SET name=?, slug=?, description=?, price=?, 
                                 duration_days=?, features=?, status=? WHERE id=?");
            $stmt->execute([$name, $slug, $description, $price, $duration_days, $features, $status, $id]);
            $success = "Subscription plan updated successfully";
        } catch (PDOException $e) {
            $error = "Error: " . $e->getMessage();
        }
    }

    if (isset($_POST['delete_subscription'])) {
        $id = (int)$_POST['id'];
        try {
            $stmt = $db->prepare("DELETE FROM subscriptions WHERE id = ?");
            $stmt->execute([$id]);
            $success = "Subscription plan deleted successfully";
        } catch (PDOException $e) {
            $error = "Error: " . $e->getMessage();
        }
    }
}

// Get all subscription plans
$subscriptions = $db->query("SELECT * FROM subscriptions ORDER BY price ASC")->fetchAll(PDO::FETCH_ASSOC);

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">Subscription Plans</h3>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubscriptionModal">
                            <i class="fas fa-plus"></i> Add New Plan
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Price</th>
                                    <th>Duration</th>
                                    <th>Features</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subscriptions as $sub): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($sub['name']); ?></td>
                                        <td>৳<?php echo number_format($sub['price'], 2); ?></td>
                                        <td><?php echo $sub['duration_days']; ?> days</td>
                                        <td>
                                            <?php 
                                            $features = json_decode($sub['features'], true);
                                            if ($features) {
                                                echo '<ul class="mb-0">';
                                                foreach ($features as $feature) {
                                                    echo '<li>' . htmlspecialchars($feature) . '</li>';
                                                }
                                                echo '</ul>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $sub['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($sub['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary edit-subscription" 
                                                    data-subscription='<?php echo json_encode($sub); ?>'
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#editSubscriptionModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger delete-subscription"
                                                    data-id="<?php echo $sub['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($sub['name']); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Subscription Modal -->
<div class="modal fade" id="addSubscriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>Add New Subscription Plan
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <!-- Name Field -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" name="name" class="form-control" id="planName" placeholder="Plan Name" required>
                                <label for="planName">Plan Name</label>
                                <div class="invalid-feedback">Please enter a plan name</div>
                            </div>
                        </div>
                        
                        <!-- Price Field -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" name="price" class="form-control" id="planPrice" placeholder="Price" min="0" step="0.01" required>
                                <label for="planPrice">Price (৳)</label>
                                <div class="invalid-feedback">Please enter a valid price</div>
                            </div>
                        </div>

                        <!-- Duration Field -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" name="duration_days" class="form-control" id="planDuration" placeholder="Duration" min="1" required>
                                <label for="planDuration">Duration (Days)</label>
                                <div class="invalid-feedback">Please enter valid duration</div>
                            </div>
                        </div>

                        <!-- Status Field -->
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select name="status" class="form-select" id="planStatus" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                                <label for="planStatus">Status</label>
                            </div>
                        </div>

                        <!-- Description Field -->
                        <div class="col-12">
                            <div class="form-floating mb-3">
                                <textarea name="description" class="form-control" id="planDescription" style="height: 100px" placeholder="Description"></textarea>
                                <label for="planDescription">Description</label>
                            </div>
                        </div>

                        <!-- Features Field -->
                        <div class="col-12">
                            <label class="form-label fw-bold mb-2">Features</label>
                            <div id="featuresContainer">
                                <div class="feature-input d-flex mb-2">
                                    <input type="text" name="features[]" class="form-control me-2" placeholder="Enter a feature" required>
                                    <button type="button" class="btn btn-danger remove-feature" onclick="removeFeature(this)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-success btn-sm mt-2" onclick="addFeature()">
                                <i class="fas fa-plus me-1"></i>Add Feature
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" name="add_subscription" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Save Plan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Subscription Modal -->
<div class="modal fade" id="editSubscriptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Subscription Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" name="name" id="edit_name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Price (৳)</label>
                        <input type="number" name="price" id="edit_price" class="form-control" step="0.01" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Duration (Days)</label>
                        <input type="number" name="duration_days" id="edit_duration_days" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Features (one per line)</label>
                        <textarea name="features[]" id="edit_features" class="form-control" rows="4"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" id="edit_status" class="form-control">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="update_subscription" class="btn btn-primary">Update Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteSubscriptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="id" id="delete_subscription_id">
                <div class="modal-header">
                    <h5 class="modal-title">Delete Subscription Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the subscription plan: <strong id="delete_subscription_name"></strong>?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_subscription" class="btn btn-danger">Delete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit button click
    const editButtons = document.querySelectorAll('.edit-subscription');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const subscription = JSON.parse(this.dataset.subscription);
            
            document.getElementById('edit_id').value = subscription.id;
            document.getElementById('edit_name').value = subscription.name;
            document.getElementById('edit_description').value = subscription.description;
            document.getElementById('edit_price').value = subscription.price;
            document.getElementById('edit_duration_days').value = subscription.duration_days;
            document.getElementById('edit_status').value = subscription.status;
            
            // Handle features array
            const features = JSON.parse(subscription.features);
            document.getElementById('edit_features').value = features.join('\n');
        });
    });

    // Handle delete button click
    const deleteButtons = document.querySelectorAll('.delete-subscription');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteSubscriptionModal'));
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const subscriptionId = this.dataset.id;
            const subscriptionName = this.dataset.name;
            
            document.getElementById('delete_subscription_id').value = subscriptionId;
            document.getElementById('delete_subscription_name').textContent = subscriptionName;
            
            deleteModal.show();
        });
    });
});

// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Features management
function addFeature() {
    const container = document.getElementById('featuresContainer');
    const newFeature = document.createElement('div');
    newFeature.className = 'feature-input d-flex mb-2';
    newFeature.innerHTML = `
        <input type="text" name="features[]" class="form-control me-2" placeholder="Enter a feature" required>
        <button type="button" class="btn btn-danger remove-feature" onclick="removeFeature(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(newFeature);
}

function removeFeature(button) {
    const featureInputs = document.getElementsByClassName('feature-input');
    if (featureInputs.length > 1) {
        button.parentElement.remove();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
