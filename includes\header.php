<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/auth.php';

// All header() calls should be here, before ANY output
header('X-Frame-Options: SAMEORIGIN');
header('X-Content-Type-Options: nosniff');
// ... other headers

// Initialize auth
$auth = new Auth($db);
$isLoggedIn = $auth->isLoggedIn();
$username = isset($_SESSION['username']) ? $_SESSION['username'] : '';
$userInitial = $username ? substr($username, 0, 1) : '';

// Now start HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? 'Cinepix'; ?></title>
    
    <!-- Move Bootstrap CSS to top -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
    <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
    
    <!-- Add Bootstrap JS in head with defer -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" defer></script>
    <script src="https://cdn.plyr.io/3.7.8/plyr.js" defer></script>
    
    <!-- Rest of your styles remain same -->
    <style>
        :root {
            --netflix-red: #e50914;
            --netflix-black: #141414;
            --netflix-dark: #181818;
        }
        
        body {
            background-color: var(--netflix-black);
            color: #fff;
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            padding-top: 80px;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(180deg, rgba(0,0,0,0.9) 0%, rgba(20,20,20,0.9) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding: 0.5rem 0;
        }

        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 800;
            letter-spacing: 1px;
            text-transform: uppercase;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #fff !important;
            text-shadow: 0 0 10px rgba(229,9,20,0.5);
        }

        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: #fff !important;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background-color: var(--netflix-red);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 80%;
        }

        /* Search Bar */
        .search-form {
            position: relative;
        }

        .search-input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            color: #fff;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            width: 250px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            background: rgba(255,255,255,0.15);
            border-color: var(--netflix-red);
            box-shadow: 0 0 0 2px rgba(229,9,20,0.2);
            width: 300px;
        }

        .search-input::placeholder {
            color: rgba(255,255,255,0.5);
        }

        /* User Menu */
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--netflix-red);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: bold;
        }

        .dropdown-menu {
            background: rgba(20,20,20,0.95);
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            margin-top: 0.5rem;
        }

        .dropdown-item {
            color: rgba(255,255,255,0.8);
            transition: all 0.3s ease;
            padding: 0.7rem 1.5rem;
        }

        .dropdown-item:hover {
            background: rgba(229,9,20,0.1);
            color: #fff;
        }

        .dropdown-divider {
            border-color: rgba(255,255,255,0.1);
        }

        /* Auth Buttons */
        .auth-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-login {
            background-color: var(--netflix-red);
            color: #fff;
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .btn-register {
            background-color: transparent;
            color: #fff;
            border: 1px solid rgba(255,255,255,0.5);
            padding: 0.5rem 1.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .btn-login:hover, .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Mobile Styles */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background: rgba(20, 20, 20, 0.95);
                backdrop-filter: blur(10px);
                padding: 1rem;
                border-radius: 8px;
                margin-top: 10px;
            }

            .navbar-nav {
                gap: 10px;
            }

            .nav-link {
                padding: 0.8rem 1rem !important;
                border-radius: 6px;
            }

            .nav-link:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .search-form {
                margin: 1rem 0;
            }

            .search-input {
                width: 100% !important;
            }

            .auth-buttons {
                flex-direction: column;
                width: 100%;
                gap: 0.5rem;
            }

            .auth-buttons .btn {
                width: 100%;
                text-align: center;
            }

            .user-menu {
                width: 100%;
            }

            .user-menu .dropdown {
                width: 100%;
            }

            .dropdown-toggle {
                width: 100%;
                justify-content: space-between;
                padding: 0.8rem 1rem;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.1);
            }
        }
    </style>
    <script>
        // Disable right click
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
        document.addEventListener('keydown', function(e) {
            if (
                e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i' || e.key === 'J' || e.key === 'j')) ||
                (e.ctrlKey && (e.key === 'U' || e.key === 'u'))
            ) {
                e.preventDefault();
            }
        });

        // Disable DevTools
        setInterval(function() {
            if (window.devtools.isOpen) {
                window.location.href = 'about:blank';
            }
        }, 1000);

        // Clear console
        setInterval(function() {
            console.clear();
            console.log('%cConsole is disabled!', 'color: red; font-size: 50px; font-weight: bold;');
        }, 100);

        // Disable source view
        document.onkeypress = function(event) {
            event = (event || window.event);
            if (event.keyCode == 123) {
                return false;
            }
        }
    </script>
    <style>
        /* Disable text selection */
        * {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid px-2 px-lg-3">
            <!-- Logo -->
            <a class="navbar-brand me-2" href="/">
                <span style="color: #e50914;">CINE</span>PIX
            </a>

            <!-- Mobile Actions -->
            <div class="mobile-actions d-flex d-lg-none align-items-center gap-2">
                <a href="search.php" class="btn btn-link text-white p-1">
                    <i class="fas fa-search"></i>
                </a>
                <?php if ($isLoggedIn): ?>
                    <div class="dropdown">
                        <a class="btn btn-link text-white p-1 dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <?php if ($auth->isAdmin()): ?>
                                <a class="dropdown-item" href="admin/index.php">
                                    <i class="fas fa-user-shield me-2"></i>Admin
                                </a>
                                <div class="dropdown-divider"></div>
                            <?php endif; ?>
                            <a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user me-2"></i>Profile
                            </a>
                            <a class="dropdown-item" href="watchlist.php">
                                <i class="fas fa-list me-2"></i>Watchlist
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </ul>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="btn btn-link text-white p-1">
                        <i class="fas fa-sign-in-alt"></i>
                    </a>
                <?php endif; ?>
                <button class="navbar-toggler p-1" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
            </div>

            <!-- Desktop Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="movies.php">Movies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="series.php">Series</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="latest.php">New</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="live-tv.php">Live</a>
                    </li>
                </ul>

                <!-- Desktop Search & User Menu -->
                <div class="d-none d-lg-flex align-items-center gap-3">
                    <form class="search-form" action="search.php" method="GET">
                        <input class="search-input" type="search" name="q" placeholder="Search...">
                    </form>
                    <?php if ($isLoggedIn): ?>
                        <div class="user-menu">
                            <div class="dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" data-bs-toggle="dropdown">
                                    <div class="user-avatar"><?php echo $userInitial; ?></div>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <?php if ($auth->isAdmin()): ?>
                                        <a class="dropdown-item" href="admin/index.php">
                                            <i class="fas fa-user-shield me-2"></i>Admin Panel
                                        </a>
                                        <div class="dropdown-divider"></div>
                                    <?php endif; ?>
                                    <a class="dropdown-item" href="profile.php">
                                        <i class="fas fa-user me-2"></i>Profile
                                    </a>
                                    <a class="dropdown-item" href="watchlist.php">
                                        <i class="fas fa-list me-2"></i>Watchlist
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="logout.php">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </a>
                                </ul>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="auth-buttons">
                            <a href="login.php" class="btn btn-login">Login</a>
                            <a href="register.php" class="btn btn-register">Register</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Add Bootstrap and other necessary scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
        var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        s1.async=true;
        s1.src='https://embed.tawk.to/6504aa910f2b18434fd8c364/1had3kiu9';
        s1.charset='UTF-8';
        s1.setAttribute('crossorigin','*');
        s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');

        // Toggle menu when clicking on navbar toggler
        navbarToggler.addEventListener('click', function() {
            const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
            
            if (!isExpanded) {
                navbarCollapse.classList.add('show');
                navbarToggler.setAttribute('aria-expanded', 'true');
            } else {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });

        // Close menu when clicking on nav links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const isClickInside = navbarCollapse.contains(event.target) || navbarToggler.contains(event.target);
            
            if (!isClickInside && navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });

        // Prevent menu from staying open when switching from mobile to desktop view
        window.addEventListener('resize', function() {
            if (window.innerWidth > 991.98) {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });
    });
    </script>
</body>
</html>
