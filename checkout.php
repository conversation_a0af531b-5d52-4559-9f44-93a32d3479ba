<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/auth.php';
require_once 'includes/bkash_config.php';
require_once 'includes/bkash_handler.php';

$auth = new Auth($db);
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Get plan details
$plan_id = isset($_GET['plan']) ? (int)$_GET['plan'] : 0;
if (!$plan_id) {
    header('Location: subscription.php');
    exit;
}

// Fetch plan details
$stmt = $db->prepare("SELECT * FROM subscriptions WHERE id = ? AND status = 'active'");
$stmt->execute([$plan_id]);
$plan = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$plan) {
    header('Location: subscription.php');
    exit;
}

$page_title = 'Checkout - ' . $plan['name'];
require_once 'includes/header.php';

// Initialize bKash handler
$bkash = new BkashHandler();

// Generate unique invoice number
$invoice_number = 'INV' . time() . rand(100, 999);

// Process payment
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_method = $_POST['payment_method'] ?? '';
    
    if ($payment_method === 'manual') {
        $transaction_id = $_POST['transaction_id'] ?? '';
        
        if (empty($transaction_id)) {
            $error = 'Transaction ID is required';
        } else {
            require_once 'includes/payment_handler.php';
            $payment = new PaymentHandler($db);
            
            try {
                $result = $payment->processBkashPayment(
                    $_SESSION['user_id'],
                    $plan_id,
                    $plan['price'],
                    $transaction_id
                );
                
                if ($result) {
                    $success = 'Payment submitted successfully. We will verify and activate your subscription soon.';
                } else {
                    $error = 'Payment processing failed. Please try again.';
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } elseif ($payment_method === 'automatic') {
        try {
            $response = $bkash->createPayment($plan['price'], $invoice_number);
            if (isset($response['paymentID'])) {
                $_SESSION['payment_id'] = $response['paymentID'];
                header('Location: ' . $response['bkashURL']);
                exit;
            } else {
                $error = 'Failed to initialize bKash payment';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card checkout-card">
                <div class="card-body">
                    <h2 class="text-center mb-4">Checkout</h2>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>

                    <!-- Plan Summary -->
                    <div class="plan-summary mb-4">
                        <h3>Order Summary</h3>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4><?php echo htmlspecialchars($plan['name']); ?></h4>
                                <p class="text-muted"><?php echo $plan['duration_days']; ?> days subscription</p>
                            </div>
                            <div class="plan-price">
                                <h3>৳<?php echo number_format($plan['price'], 0); ?></h3>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="payment-methods mb-4">
                        <h3>Select Payment Method</h3>
                        
                        <!-- Payment Method Tabs -->
                        <ul class="nav nav-tabs mb-4" id="paymentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="automatic-tab" data-bs-toggle="tab" 
                                        data-bs-target="#automatic" type="button" role="tab">
                                    Automatic bKash Payment
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="manual-tab" data-bs-toggle="tab" 
                                        data-bs-target="#manual" type="button" role="tab">
                                    Manual Payment
                                </button>
                            </li>
                        </ul>
                        
                        <!-- Tab Content -->
                        <div class="tab-content" id="paymentTabContent">
                            <!-- Automatic Payment -->
                            <div class="tab-pane fade show active" id="automatic" role="tabpanel">
                                <div class="text-center">
                                    <img src="assets/images/bkash-payment-logo.png" alt="bKash" class="mb-4" style="height: 60px;">
                                    <p class="mb-4">Pay securely using bKash payment gateway</p>
                                    <form method="POST">
                                        <input type="hidden" name="payment_method" value="automatic">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            Pay with bKash
                                        </button>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- Manual Payment -->
                            <div class="tab-pane fade" id="manual" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-12 mb-4">
                                        <!-- Payment Method Selection -->
                                        <div class="payment-method-select mb-4">
                                            <select class="form-select" id="manualPaymentMethod">
                                                <option value="bkash">bKash</option>
                                                <option value="nagad">Nagad</option>
                                                <option value="rocket">Rocket</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <!-- QR Codes for different payment methods -->
                                        <div class="payment-qr text-center mb-4">
                                            <div id="bkashQR" class="qr-container">
                                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=bkash://merchantpay/01712345678/<?php echo $plan['price']; ?>" 
                                                     alt="bKash QR Code" 
                                                     class="img-fluid mb-2">
                                                <p class="text-muted">Scan with bKash app</p>
                                            </div>
                                            <div id="nagadQR" class="qr-container d-none">
                                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=nagad://merchantpay/01712345679/<?php echo $plan['price']; ?>" 
                                                     alt="Nagad QR Code" 
                                                     class="img-fluid mb-2">
                                                <p class="text-muted">Scan with Nagad app</p>
                                            </div>
                                            <div id="rocketQR" class="qr-container d-none">
                                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=rocket://merchantpay/01712345670/<?php echo $plan['price']; ?>" 
                                                     alt="Rocket QR Code" 
                                                     class="img-fluid mb-2">
                                                <p class="text-muted">Scan with Rocket app</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="payment-instructions">
                                            <div class="alert alert-info">
                                                <h4>Manual Payment Instructions:</h4>
                                                <div id="bkashInstructions">
                                                    <p>Send money to: <strong>01712345678</strong> (bKash Merchant)</p>
                                                    <ol>
                                                        <li>Open your bKash app</li>
                                                        <li>Select "Send Money"</li>
                                                        <li>Enter amount: ৳<?php echo number_format($plan['price'], 0); ?></li>
                                                        <li>Add reference: <?php echo $invoice_number; ?></li>
                                                        <li>Complete payment</li>
                                                    </ol>
                                                </div>
                                                <div id="nagadInstructions" class="d-none">
                                                    <p>Send money to: <strong>01712345679</strong> (Nagad Merchant)</p>
                                                    <ol>
                                                        <li>Open your Nagad app</li>
                                                        <li>Select "Send Money"</li>
                                                        <li>Enter amount: ৳<?php echo number_format($plan['price'], 0); ?></li>
                                                        <li>Add reference: <?php echo $invoice_number; ?></li>
                                                        <li>Complete payment</li>
                                                    </ol>
                                                </div>
                                                <div id="rocketInstructions" class="d-none">
                                                    <p>Send money to: <strong>01712345670</strong> (Rocket Merchant)</p>
                                                    <ol>
                                                        <li>Open your Rocket app</li>
                                                        <li>Select "Send Money"</li>
                                                        <li>Enter amount: ৳<?php echo number_format($plan['price'], 0); ?></li>
                                                        <li>Add reference: <?php echo $invoice_number; ?></li>
                                                        <li>Complete payment</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Transaction ID Form -->
                                        <form method="POST" class="mt-4">
                                            <input type="hidden" name="payment_method" value="manual">
                                            <div class="mb-3">
                                                <label for="transaction_id" class="form-label">Enter Transaction ID</label>
                                                <input type="text" class="form-control" id="transaction_id" name="transaction_id" required>
                                            </div>
                                            <button type="submit" class="btn btn-primary">Submit Payment</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Checkout Page Styles */
.checkout-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.plan-summary {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 30px;
}

.payment-option label {
    padding: 15px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.payment-option label:hover {
    background: rgba(255, 255, 255, 0.1);
}

.btn-check:checked + .btn-outline-primary {
    background: rgba(13, 110, 253, 0.2);
    border-color: #0d6efd;
}

.form-control {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: #0d6efd;
    color: #fff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.alert-info {
    background: rgba(13, 202, 240, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.9);
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0dcaf0);
    border: none;
    padding: 12px 24px;
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0aa2c0);
}

/* Text Colors */
h2, h3, h4 {
    color: #fff;
}

.text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

.form-text {
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
}

.nav-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-tabs .nav-link {
    color: rgba(255, 255, 255, 0.7);
    border: none;
    border-bottom: 2px solid transparent;
    padding: 1rem 1.5rem;
}

.nav-tabs .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
}

.nav-tabs .nav-link.active {
    color: #e2136e;
    background: none;
    border-bottom: 2px solid #e2136e;
}

.tab-content {
    padding: 2rem 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('payment-form');
    
    form.addEventListener('submit', function(e) {
        const transactionId = document.getElementById('transaction_id').value.trim();
        
        if (!transactionId) {
            e.preventDefault();
            alert('Please enter the Transaction ID');
        }
    });
});

document.getElementById('manualPaymentMethod').addEventListener('change', function() {
    // Hide all QR codes and instructions
    document.querySelectorAll('.qr-container, .payment-instructions > div').forEach(el => {
        el.classList.add('d-none');
    });
    
    // Show selected payment method's QR and instructions
    document.getElementById(this.value + 'QR').classList.remove('d-none');
    document.getElementById(this.value + 'Instructions').classList.remove('d-none');
});
</script>
