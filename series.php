<?php
session_start();
require_once 'includes/db.php';
require_once 'includes/tmdb_handler.php';

// Pagination settings
$items_per_page = 24;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $items_per_page;

// Initialize variables
$series = [];
$error = null;

try {
    // Get total count of active series
    $total_count = $db->query("SELECT COUNT(*) FROM series WHERE status = 'active'")->fetchColumn();
    $total_pages = ceil($total_count / $items_per_page);
    
    // Ensure current page is within valid range
    $current_page = max(1, min($current_page, $total_pages));
    
    // Get paginated series
    $stmt = $db->prepare("
        SELECT * FROM series 
        WHERE status = 'active' 
        ORDER BY id DESC 
        LIMIT :limit OFFSET :offset
    ");
    
    $stmt->bindValue(':limit', $items_per_page, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $series = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    error_log($e->getMessage());
    $error = "An error occurred while fetching data";
}

$page_title = 'TV Series';
require_once 'includes/header.php';
?>

<div class="container-fluid px-4 py-5">
    <h1 class="mb-4">TV Series</h1>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="row g-4">
        <?php foreach ($series as $show): ?>
        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
            <div class="movie-card">
                <a href="show.php?id=<?php echo $show['id']; ?>" class="poster-link">
                    <div class="poster-wrapper">
                        <img src="https://image.tmdb.org/t/p/w500<?php echo htmlspecialchars($show['poster_path']); ?>" 
                             class="movie-poster" 
                             alt="<?php echo htmlspecialchars($show['title']); ?>">
                        <div class="poster-overlay">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                </a>
                <div class="movie-info">
                    <h5 class="movie-title text-white"><?php echo htmlspecialchars($show['title']); ?></h5>
                    <a href="show.php?id=<?php echo $show['id']; ?>" 
                       class="btn btn-netflix btn-sm">
                        <i class="fas fa-play me-1"></i> Watch Now
                    </a>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <?php if ($total_pages > 1): ?>
    <nav aria-label="Series pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($current_page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=1" title="First page">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $current_page - 1; ?>">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            <?php endif; ?>
            
            <?php
            $range = 2;
            $start = max(1, $current_page - $range);
            $end = min($total_pages, $current_page + $range);
            
            if ($start > 1) {
                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            
            for ($i = $start; $i <= $end; $i++): ?>
                <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                </li>
            <?php endfor;
            
            if ($end < $total_pages) {
                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            ?>
            
            <?php if ($current_page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $current_page + 1; ?>">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $total_pages; ?>" title="Last page">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<style>
.poster-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.poster-link {
    display: block;
    text-decoration: none;
}

.poster-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.poster-overlay i {
    color: white;
    font-size: 3rem;
}

.poster-wrapper:hover {
    transform: scale(1.05);
}

.poster-wrapper:hover .poster-overlay {
    opacity: 1;
}

.movie-poster {
    width: 100%;
    height: auto;
    display: block;
}

/* Pagination Styles */
.pagination {
    margin-top: 2rem;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    min-width: 38px;
    text-align: center;
    background-color: #222;
    border-color: #444;
    color: #fff;
}

.pagination .page-link:hover {
    background-color: #333;
    border-color: #666;
    color: #fff;
}

.pagination .page-item.active .page-link {
    background-color: #e50914;
    border-color: #e50914;
}

.pagination .page-link:focus {
    box-shadow: none;
    outline: none;
}

.pagination .disabled .page-link {
    background-color: transparent;
    border-color: #444;
    color: #666;
}

.pagination .fas {
    font-size: 12px;
}
</style>

<?php require_once 'includes/footer.php'; ?>
