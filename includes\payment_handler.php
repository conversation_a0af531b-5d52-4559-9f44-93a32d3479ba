<?php
class PaymentHandler {
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    public function processBkashPayment($user_id, $subscription_id, $amount, $transaction_id) {
        // Verify bKash payment here
        // Add your bKash API integration code
        
        return $this->createPayment($user_id, $subscription_id, $amount, 'bkash', $transaction_id);
    }

    public function processOfflinePayment($user_id, $subscription_id, $amount, $transaction_id) {
        return $this->createPayment($user_id, $subscription_id, $amount, 'offline', $transaction_id);
    }

    private function createPayment($user_id, $subscription_id, $amount, $method, $transaction_id) {
        $sql = "INSERT INTO payments (user_id, subscription_id, amount, payment_method, transaction_id, status) 
                VALUES (?, ?, ?, ?, ?, 'pending')";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$user_id, $subscription_id, $amount, $method, $transaction_id]);
    }
}