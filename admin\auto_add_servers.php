<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/server_manager.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$serverManager = new ServerManager($db);
$message = '';

if (isset($_POST['auto_add'])) {
    $result = $serverManager->autoAddServersToContent();
    $message = $result 
        ? "Successfully added servers to all content" 
        : "Error occurred while adding servers";
}

// Get statistics
$stats = [
    'total_movies' => $db->query("SELECT COUNT(*) FROM movies WHERE status = 'active'")->fetchColumn(),
    'total_series' => $db->query("SELECT COUNT(*) FROM series WHERE status = 'active'")->fetchColumn(),
    'total_servers' => $db->query("SELECT COUNT(*) FROM servers WHERE status = 'active'")->fetchColumn(),
    'movie_servers' => $db->query("SELECT COUNT(*) FROM movie_servers WHERE status = 'active'")->fetchColumn(),
    'series_servers' => $db->query("SELECT COUNT(*) FROM series_servers WHERE status = 'active'")->fetchColumn()
];

?>
<!DOCTYPE html>
<html>
<head>
    <title>Auto Add Servers - Admin Panel</title>
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <?php include 'includes/admin_header.php'; ?>
    
    <div class="container">
        <h2>Auto Add Servers to Content</h2>
        
        <?php if ($message): ?>
            <div class="alert <?php echo strpos($message, 'Error') !== false ? 'alert-danger' : 'alert-success'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <div class="stats-container">
            <h3>Current Statistics</h3>
            <ul>
                <li>Total Active Movies: <?php echo $stats['total_movies']; ?></li>
                <li>Total Active Series: <?php echo $stats['total_series']; ?></li>
                <li>Total Active Servers: <?php echo $stats['total_servers']; ?></li>
                <li>Total Movie Servers: <?php echo $stats['movie_servers']; ?></li>
                <li>Total Series Servers: <?php echo $stats['series_servers']; ?></li>
            </ul>
        </div>

        <form method="post" class="form">
            <button type="submit" name="auto_add" class="btn btn-primary">
                Auto Add Servers to All Content
            </button>
        </form>
    </div>
</body>
</html>