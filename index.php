<?php
// Start output buffering at the very beginning
ob_start();

// Session and authentication
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);
require_once 'includes/db.php';
require_once 'includes/auth.php';
require_once 'includes/components/content_card.php';
$auth = new Auth($db);

// Check remember token if user is not already logged in
if (!isset($_SESSION['user_id'])) {
    $auth->checkRememberToken();
}

$page_title = 'Home';
require_once 'includes/header.php';

// Count total movies using PDO
$movie_count = $db->query("SELECT COUNT(*) as total FROM movies")->fetch(PDO::FETCH_ASSOC);
echo "<!-- Total movies in database: " . $movie_count['total'] . " -->";

// Get current hour of the day (0-23)
$current_hour = date('G');

$featured_query = "SELECT *,
                  CASE 
                    WHEN backdrop_path LIKE 'http%' THEN backdrop_path
                    WHEN backdrop_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/original', backdrop_path)
                    ELSE 'assets/images/default-backdrop.jpg'
                  END as backdrop_path,
                  CASE 
                    WHEN poster_path LIKE 'http%' THEN poster_path 
                    WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                  END as poster_path
                  FROM movies 
                  WHERE status = 'active'
                  AND backdrop_path IS NOT NULL 
                  ORDER BY id
                  LIMIT 1 OFFSET " . ($current_hour % 10); // Changes every hour, cycles through first 10 movies

$featured = $db->query($featured_query)->fetch(PDO::FETCH_ASSOC);

// Fallback content if no movies exist
if(!$featured) {
    $featured = array(
        'id' => 0,
        'title' => 'Welcome to CinePix',
        'overview' => 'Start watching the best movies and TV shows.',
        'backdrop_path' => 'assets/images/default-backdrop.jpg',
        'poster_path' => 'assets/images/default-poster.jpg'
    );
}

// Get random movies using PDO
$movies_query = "SELECT *, 
                CASE 
                    WHEN poster_path LIKE 'http%' THEN poster_path 
                    WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END as poster_path
                FROM movies 
                WHERE status = 'active' 
                ORDER BY RAND() 
                LIMIT 24";
$movies_result = $db->query($movies_query)->fetchAll(PDO::FETCH_ASSOC);

// Get popular series using PDO
$series_query = "SELECT s.*, 
                CASE 
                    WHEN s.poster_path LIKE 'http%' THEN s.poster_path 
                    WHEN s.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', s.poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END as poster_path,
                (SELECT COUNT(*) FROM seasons WHERE series_id = s.id) as season_count 
                FROM series s 
                WHERE s.status = 'active' 
                ORDER BY s.views DESC, s.id DESC 
                LIMIT 24";
$series_result = $db->query($series_query)->fetchAll(PDO::FETCH_ASSOC);

// Get latest movies using PDO
$latest_query = "SELECT *, 
                CASE 
                    WHEN poster_path LIKE 'http%' THEN poster_path 
                    WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END as poster_path
                FROM movies 
                WHERE status = 'active' 
                ORDER BY created_at DESC, id DESC 
                LIMIT 24";
$latest_result = $db->query($latest_query)->fetchAll(PDO::FETCH_ASSOC);

// Add this function at the top of the file
function formatPosterPath($path) {
    if (empty($path)) {
        return 'assets/images/default-poster.jpg';
    }
    
    if (str_starts_with($path, 'http')) {
        return $path;
    }
    
    return 'https://image.tmdb.org/t/p/w500' . $path;
}

// Get all genres with their movies and series
$genres_query = "
    SELECT 
        g.name as genre_name,
        m.id,
        m.title,
        CASE 
            WHEN m.poster_path LIKE 'http%' THEN m.poster_path
            WHEN m.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', m.poster_path)
            ELSE 'assets/images/default-poster.jpg'
        END as poster_path,
        m.release_date as date,
        'movie' as content_type
    FROM genres g
    JOIN movie_genres mg ON g.id = mg.genre_id
    JOIN movies m ON mg.movie_id = m.id
    WHERE m.status = 'active'
    
    UNION
    
    SELECT 
        g.name as genre_name,
        s.id,
        s.title,
        CASE 
            WHEN s.poster_path LIKE 'http%' THEN s.poster_path
            WHEN s.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', s.poster_path)
            ELSE 'assets/images/default-poster.jpg'
        END as poster_path,
        s.first_air_date as date,
        'series' as content_type
    FROM genres g
    JOIN series_genres sg ON g.id = sg.genre_id
    JOIN series s ON sg.series_id = s.id
    WHERE s.status = 'active'
    ORDER BY genre_name, date DESC";

$genres_stmt = $db->query($genres_query);
$all_content = $genres_stmt->fetchAll(PDO::FETCH_ASSOC);

// Group content by genre
$genres_result = [];
foreach ($all_content as $item) {
    $genres_result[$item['genre_name']][] = $item;
}

// Get Bengali content
$bengali_query = "SELECT m.*,
    CASE 
        WHEN poster_path LIKE 'http%' THEN poster_path 
        WHEN poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', poster_path)
        ELSE 'assets/images/default-poster.jpg'
    END as poster_path
    FROM manual_movies m 
    WHERE m.is_bengali = 1 
    AND m.status = 'active'
    ORDER BY m.created_at DESC 
    LIMIT 12";

try {
    $bengali_movies = $db->query($bengali_query)->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching Bengali content: " . $e->getMessage());
    $bengali_movies = [];
}

// Get continue watching content
$continue_watching_query = "
    SELECT 
        wh.content_type,
        wh.content_id,
        wh.progress,
        CASE 
            WHEN wh.content_type = 'movie' THEN m.title
            ELSE s.title
        END as title,
        CASE 
            WHEN wh.content_type = 'movie' THEN 
                CASE 
                    WHEN m.poster_path LIKE 'http%' THEN m.poster_path 
                    WHEN m.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', m.poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END
            ELSE 
                CASE 
                    WHEN s.poster_path LIKE 'http%' THEN s.poster_path 
                    WHEN s.poster_path IS NOT NULL THEN CONCAT('https://image.tmdb.org/t/p/w500', s.poster_path)
                    ELSE 'assets/images/default-poster.jpg'
                END
        END as poster_path
    FROM watch_history wh
    LEFT JOIN movies m ON wh.content_type = 'movie' AND wh.content_id = m.id
    LEFT JOIN series s ON wh.content_type = 'series' AND wh.content_id = s.id
    WHERE wh.user_id = ? 
    AND wh.progress > 0 
    AND wh.progress < 95
    ORDER BY wh.watched_at DESC 
    LIMIT 12";

$continue_watching = [];
if (isset($_SESSION['user_id'])) {
    $stmt = $db->prepare($continue_watching_query);
    $stmt->execute([$_SESSION['user_id']]);
    $continue_watching = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Debug information
if (isset($_SESSION['user_id'])) {
    $debug_query = "SELECT COUNT(*) as count FROM watch_history WHERE user_id = ?";
    $debug_stmt = $db->prepare($debug_query);
    $debug_stmt->execute([$_SESSION['user_id']]);
    $debug_count = $debug_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    error_log("Watch history entries for user {$_SESSION['user_id']}: $debug_count");
}
?>

<!-- Hero Section -->
<div class="hero-section" style="background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.9)), url('<?php echo htmlspecialchars($featured['backdrop_path']); ?>')">
    <div class="container-fluid">
        <div class="row align-items-center min-vh-75"> <!-- Added min-height -->
            <div class="col-md-8"> <!-- Changed from col-md-9 for better spacing -->
                <div class="hero-info text-light p-4"> <!-- Added padding -->
                    <?php if($featured['id'] > 0): ?>
                        <span class="badge bg-danger mb-2">Featured</span>
                    <?php endif; ?>
                    
                    <h1 class="show-title display-4 fw-bold mb-3">
                        <?php echo htmlspecialchars($featured['title']); ?>
                    </h1>
                    
                    <div class="show-meta mb-3">
                        <?php if(isset($featured['release_date']) && $featured['release_date']): ?>
                            <span class="release-date me-3">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <?php echo date('Y', strtotime($featured['release_date'])); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if(isset($featured['rating']) && $featured['rating']): ?>
                            <span class="rating me-3">
                                <i class="fas fa-star text-warning me-2"></i>
                                <?php echo number_format($featured['rating'], 1); ?>
                            </span>
                        <?php endif; ?>
                        
                        <?php if(isset($featured['duration']) && $featured['duration']): ?>
                            <span class="duration me-3">
                                <i class="fas fa-clock me-2"></i>
                                <?php echo $featured['duration']; ?> min
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <p class="show-overview lead mb-4">
                        <?php 
                        $overview = isset($featured['overview']) ? $featured['overview'] : '';
                        echo htmlspecialchars(substr($overview, 0, 200)) . (strlen($overview) > 200 ? '...' : '');
                        ?>
                    </p>
                    
                    <?php if($featured['id'] > 0): ?>
                        <div class="hero-buttons">
                            <a href="movie.php?id=<?php echo $featured['id']; ?>" 
                               class="btn btn-danger btn-lg me-2">
                                <i class="fas fa-play me-2"></i>Watch Now
                            </a>
                            <a href="movie.php?id=<?php echo $featured['id']; ?>" 
                               class="btn btn-outline-light btn-lg">
                                <i class="fas fa-info-circle me-2"></i>More Info
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Continue Watching Section -->
<?php if (!empty($continue_watching)): ?>
<section class="continue-watching py-4">
    <div class="container-fluid">
        <h2 class="section-title mb-4">Continue Watching</h2>
        <div class="row">
            <?php foreach ($continue_watching as $item): ?>
            <div class="col-6 col-sm-4 col-md-3 col-lg-2 mb-4">
                <div class="content-card">
                    <a href="<?= $item['content_type'] === 'movie' ? 'movie.php?id=' . $item['content_id'] : 'show.php?id=' . $item['content_id'] ?>">
                        <div class="card-poster">
                            <img src="<?= htmlspecialchars($item['poster_path']) ?>" alt="<?= htmlspecialchars($item['title']) ?>">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: <?= $item['progress'] ?>%" aria-valuenow="<?= $item['progress'] ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <h3 class="card-title"><?= htmlspecialchars($item['title']) ?></h3>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Content Sections -->
<div class="container"> <!-- container-fluid থেকে container এ পরিবর্তন -->
    <div class="content-section">
        <h2>Popular Movies</h2>
        <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-3">
            <?php 
            if(!empty($movies_result)):
                foreach(array_slice($movies_result, 0, 12) as $movie): 
                    $movie['content_type'] = 'movie';
            ?>
                <div class="col">
                    <?php render_content_card($movie); ?>
                </div>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
    </div>
</div>

<style>
.movie-slider-container {
    margin: 0 -20px; /* Negative margin to allow arrows to sit outside */
}

.movie-slider {
    overflow: hidden;
    position: relative;
    padding: 0 20px;
}

.movie-row-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.movie-row {
    transition: transform 0.5s ease;
    gap: 15px;
    flex: 1;
}

.movie-item {
    width: calc((100% - 15px * 5) / 6); /* 6 items per row with gap */
}

.slider-arrow {
    flex-shrink: 0;
    width: 40px;
    height: 150px; /* Match height with posters */
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #fff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.slider-arrow:hover {
    background: rgba(255, 255, 255, 0.1);
}

.slider-arrow i {
    font-size: 24px;
}

@media (max-width: 768px) {
    .movie-item {
        width: calc((100% - 15px * 2) / 3); /* 3 items per row on mobile */
    }
    
    .slider-arrow {
        width: 30px;
        height: 100px; /* Smaller height for mobile */
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.movie-slider');
    
    sliders.forEach(slider => {
        const row = slider.querySelector('.movie-row');
        const prevBtn = slider.parentElement.querySelector('.prev-arrow');
        const nextBtn = slider.parentElement.querySelector('.next-arrow');
        let position = 0;
        const itemWidth = row.querySelector('.movie-item').offsetWidth + 15; // Include gap
        const visibleItems = Math.floor(slider.offsetWidth / itemWidth);
        const maxPosition = row.children.length - visibleItems;

        prevBtn.addEventListener('click', () => {
            position = Math.min(position + visibleItems, 0);
            updateSliderPosition();
        });

        nextBtn.addEventListener('click', () => {
            position = Math.max(position - visibleItems, -maxPosition);
            updateSliderPosition();
        });

        function updateSliderPosition() {
            row.style.transform = `translateX(${position * itemWidth}px)`;
            // Update button states
            prevBtn.disabled = position >= 0;
            nextBtn.disabled = position <= -maxPosition;
        }

        // Initial button states
        updateSliderPosition();
    });
});
</script>

<!-- Popular Series Section -->
<div class="content-section popular-series">
    <div class="section-header d-flex justify-content-between align-items-center mb-4">
        <h2 class="section-title">Popular Series</h2>
        <a href="series.php" class="view-all text-white">View All <i class="fas fa-arrow-right"></i></a>
    </div>
    
    <div class="movie-slider-container">
        <button class="slider-arrow prev-arrow" aria-label="Previous">
            <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="movie-slider">
            <div class="movie-row">
                <?php 
                if(!empty($series_result)):
                    foreach($series_result as $series): 
                ?>
                    <div class="movie-item">
                        <a href="show.php?id=<?php echo $series['id']; ?>" class="content-card">
                            <div class="movie-poster-wrapper">
                                <img src="<?php echo htmlspecialchars($series['poster_path']); ?>" 
                                     alt="<?php echo htmlspecialchars($series['title']); ?>"
                                     loading="lazy"
                                     onerror="this.src='assets/images/default-poster.jpg'">
                                     
                                <div class="card-info">
                                    <h3><?php echo htmlspecialchars($series['title']); ?></h3>
                                    <div class="meta-info">
                                        <?php if(isset($series['season_count'])): ?>
                                            <span class="seasons"><?php echo $series['season_count']; ?> Season<?php echo $series['season_count'] > 1 ? 's' : ''; ?></span>
                                        <?php endif; ?>
                                        <?php if(isset($series['first_air_date'])): ?>
                                            <span class="year"><?php echo date('Y', strtotime($series['first_air_date'])); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php 
                    endforeach;
                else:
                ?>
                    <div class="no-content">No series available</div>
                <?php endif; ?>
            </div>
        </div>

        <button class="slider-arrow next-arrow" aria-label="Next">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.movie-slider-container');
    
    sliders.forEach(slider => {
        const row = slider.querySelector('.movie-row');
        const prevBtn = slider.querySelector('.prev-arrow');
        const nextBtn = slider.querySelector('.next-arrow');
        const items = slider.querySelectorAll('.movie-item');
        
        if (!items.length) return;

        const itemWidth = items[0].offsetWidth + 15; // Include gap
        let position = 0;
        
        // Calculate how many items are visible based on container width
        const visibleItems = Math.floor(slider.offsetWidth / itemWidth);
        const maxPosition = Math.max(0, items.length - visibleItems);
        
        // Initially hide prev button
        prevBtn.style.opacity = '0';
        prevBtn.style.pointerEvents = 'none';
        
        prevBtn.addEventListener('click', () => {
            position = Math.min(position + 1, 0);
            updateSliderPosition();
        });

        nextBtn.addEventListener('click', () => {
            position = Math.max(position - 1, -maxPosition);
            updateSliderPosition();
        });

        function updateSliderPosition() {
            row.style.transform = `translateX(${position * itemWidth}px)`;
            
            // Update button visibility
            prevBtn.style.opacity = position < 0 ? '1' : '0';
            prevBtn.style.pointerEvents = position < 0 ? 'auto' : 'none';
            
            nextBtn.style.opacity = position > -maxPosition ? '1' : '0';
            nextBtn.style.pointerEvents = position > -maxPosition ? 'auto' : 'none';
        }

        // Initial check for next button
        if (items.length <= visibleItems) {
            nextBtn.style.opacity = '0';
            nextBtn.style.pointerEvents = 'none';
        }
    });
});
</script>

<style>
/* Popular Series Section Styles */
.popular-series {
    margin: 2rem 0;
}

.section-header {
    padding: 0 1rem;
}

.view-all {
    font-size: 0.9rem;
    text-decoration: none;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: #e50914 !important;
}

.content-slider {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: minmax(150px, 1fr);
    gap: 1rem;
    overflow-x: auto;
    padding: 1rem;
    scroll-snap-type: x mandatory;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.content-slider::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

.content-card {
    scroll-snap-align: start;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
    aspect-ratio: 2/3;
    background: #1a1a1a;
    text-decoration: none;
}

.content-card:hover {
    transform: scale(1.05);
}

.movie-poster-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.movie-poster-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0) 100%);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.content-card:hover .card-info {
    transform: translateY(-5px);
}

.card-info h3 {
    color: white;
    font-size: 0.9rem;
    margin: 0 0 0.5rem 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.meta-info {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 0.8rem;
}

.seasons {
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
}

.year {
    color: #aaa;
}

/* Loading animation */
.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    z-index: -1;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .content-slider {
        grid-auto-columns: minmax(120px, 1fr);
        gap: 0.8rem;
    }

    .card-info {
        padding: 0.8rem;
    }

    .card-info h3 {
        font-size: 0.8rem;
    }

    .meta-info {
        font-size: 0.7rem;
        gap: 0.6rem;
    }

    .seasons {
        padding: 1px 4px;
    }
}

/* Touch device optimizations */
@media (hover: none) {
    .content-card:active {
        transform: scale(0.98);
    }

    .card-info {
        transform: translateY(0) !important;
    }
}

/* Smooth scrolling for modern browsers */
@media (prefers-reduced-motion: no-preference) {
    .content-slider {
        scroll-behavior: smooth;
    }
}

/* Slider Container and Navigation */
.movie-slider-container {
    position: relative;
    padding: 0 40px; /* Make room for arrows */
}

.movie-slider {
    overflow: hidden;
}

.movie-row {
    display: flex;
    gap: 15px;
    transition: transform 0.3s ease;
}

.movie-item {
    flex: 0 0 calc(16.666% - 15px); /* 6 items per row */
    min-width: 150px;
}

/* Arrow Buttons */
.slider-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider-arrow:hover {
    background: rgba(255, 255, 255, 0.2);
}

.prev-arrow {
    left: 0;
}

.next-arrow {
    right: 0;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .movie-item {
        flex: 0 0 calc(20% - 15px); /* 5 items per row */
    }
}

@media (max-width: 992px) {
    .movie-item {
        flex: 0 0 calc(25% - 15px); /* 4 items per row */
    }
}

@media (max-width: 768px) {
    .movie-item {
        flex: 0 0 calc(33.333% - 15px); /* 3 items per row */
    }
    
    .movie-slider-container {
        padding: 0 30px;
    }
    
    .slider-arrow {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .movie-item {
        flex: 0 0 calc(50% - 15px); /* 2 items per row */
    }
    
    .movie-slider-container {
        padding: 0 25px;
    }
}
</style>

<!-- Latest Movies Section -->
<div class="container">
    <div class="content-section latest-movies">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title">Latest Movies</h2>
            <a href="latest.php" class="view-all text-white">View All <i class="fas fa-arrow-right"></i></a>
        </div>
        
        <div class="row row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-lg-6 g-3">
            <?php 
            if(!empty($latest_result)):
                foreach($latest_result as $movie): 
            ?>
                <div class="col">
                    <a href="movie.php?id=<?php echo $movie['id']; ?>" class="content-card">
                        <div class="movie-poster-wrapper">
                            <img src="<?php echo htmlspecialchars($movie['poster_path']); ?>" 
                                 alt="<?php echo htmlspecialchars($movie['title']); ?>"
                                 class="img-fluid"
                                 loading="lazy"
                                 onerror="this.src='assets/images/default-poster.jpg'">
                                 
                            <div class="card-info">
                                <h3 class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></h3>
                                <div class="meta-info">
                                    <?php if(isset($movie['release_date'])): ?>
                                        <span class="year"><?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                                    <?php endif; ?>
                                    <?php if(isset($movie['rating']) && $movie['rating'] > 0): ?>
                                        <span class="rating">
                                            <i class="fas fa-star text-warning"></i>
                                            <?php echo number_format($movie['rating'], 1); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php 
                endforeach;
            else:
            ?>
                <div class="col-12">
                    <div class="no-content">No movies available</div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.content-card {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: transform 0.2s;
    height: 100%;
}

.content-card:hover {
    transform: translateY(-5px);
}

.movie-poster-wrapper {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 2/3;
}

.movie-poster-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    color: white;
}

.movie-title {
    font-size: 0.9rem;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.meta-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    margin-top: 5px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 4px;
}

.rating i {
    font-size: 0.8rem;
}

@media (max-width: 576px) {
    .movie-title {
        font-size: 0.8rem;
    }
    
    .meta-info {
        font-size: 0.7rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.movie-slider');
    
    sliders.forEach(slider => {
        const row = slider.querySelector('.movie-row');
        const prevBtn = slider.parentElement.querySelector('.prev-arrow');
        const nextBtn = slider.parentElement.querySelector('.next-arrow');
        let position = 0;
        const itemWidth = row.querySelector('.movie-item').offsetWidth + 15; // Include gap
        const visibleItems = Math.floor(slider.offsetWidth / itemWidth);
        const maxPosition = row.children.length - visibleItems;

        prevBtn.addEventListener('click', () => {
            position = Math.min(position + visibleItems, 0);
            updateSliderPosition();
        });

        nextBtn.addEventListener('click', () => {
            position = Math.max(position - visibleItems, -maxPosition);
            updateSliderPosition();
        });

        function updateSliderPosition() {
            row.style.transform = `translateX(${position * itemWidth}px)`;
            // Update button states
            prevBtn.disabled = position >= 0;
            nextBtn.disabled = position <= -maxPosition;
        }

        // Initial button states
        updateSliderPosition();
    });
});
</script>

<!-- Bengali Content Section -->
<div class="content-section bengali-content">
    <div class="section-header d-flex justify-content-between align-items-center mb-4">
        <h2 class="section-title">বাংলা মুভি ও সিরিজ</h2>
        <a href="bengali.php" class="view-all text-white">View All <i class="fas fa-arrow-right"></i></a>
    </div>
    
    <div class="movie-slider-container">
        <button class="slider-arrow prev-arrow" aria-label="Previous">
            <i class="fas fa-chevron-left"></i>
        </button>
        
        <div class="movie-slider">
            <div class="movie-row">
                <?php 
                if(!empty($bengali_movies)):
                    foreach($bengali_movies as $movie): 
                ?>
                    <div class="movie-item">
                        <a href="manual_player.php?id=<?php echo $movie['id']; ?>" class="content-card">
                            <span class="content-type-badge">Movie</span>
                            <div class="movie-poster-wrapper">
                                <img src="<?php echo htmlspecialchars($movie['poster_path']); ?>" 
                                     alt="<?php echo htmlspecialchars($movie['title']); ?>"
                                     loading="lazy"
                                     onerror="this.src='assets/images/default-poster.jpg'">
                                     
                                <div class="card-info">
                                    <h3><?php echo htmlspecialchars($movie['title']); ?></h3>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php 
                    endforeach;
                else:
                ?>
                    <div class="no-content">কোন মুভি পাওয়া যায়নি</div>
                <?php endif; ?>
            </div>
        </div>

        <button class="slider-arrow next-arrow" aria-label="Next">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.movie-slider');
    
    sliders.forEach(slider => {
        const row = slider.querySelector('.movie-row');
        const prevBtn = slider.parentElement.querySelector('.prev-arrow');
        const nextBtn = slider.parentElement.querySelector('.next-arrow');
        let position = 0;
        const itemWidth = row.querySelector('.movie-item').offsetWidth + 15; // Include gap
        const visibleItems = Math.floor(slider.offsetWidth / itemWidth);
        const maxPosition = row.children.length - visibleItems;

        prevBtn.addEventListener('click', () => {
            position = Math.min(position + visibleItems, 0);
            updateSliderPosition();
        });

        nextBtn.addEventListener('click', () => {
            position = Math.max(position - visibleItems, -maxPosition);
            updateSliderPosition();
        });

        function updateSliderPosition() {
            row.style.transform = `translateX(${position * itemWidth}px)`;
            // Update button states
            prevBtn.disabled = position >= 0;
            nextBtn.disabled = position <= -maxPosition;
        }

        // Initial button states
        updateSliderPosition();
    });
});
</script>

<style>
/* Updated slider and card styles */
.movie-slider-container {
    margin: 0 -15px;
    position: relative;
}

.movie-slider {
    overflow: hidden;
    padding: 0 15px;
}

.movie-row-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
}

.movie-row {
    display: flex;
    transition: transform 0.3s ease;
    gap: 15px;
}

.movie-item {
    flex: 0 0 auto;
    width: calc((100% - 75px) / 6); /* 6 items per row on desktop */
}

.content-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: scale(1.05);
}

.content-card img {
    width: 100%;
    aspect-ratio: 2/3;
    object-fit: cover;
}

.card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    color: white;
}

.slider-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0,0,0,0.7);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.prev-arrow {
    left: 0;
}

.next-arrow {
    right: 0;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .movie-item {
        width: calc((100% - 15px) / 2) !important; /* 2 items per row on mobile */
    }

    .content-card {
        margin-bottom: 15px;
    }

    .content-card img {
        min-height: 250px; /* Standardized height for mobile */
    }

    .card-info h3 {
        font-size: 14px;
        margin-bottom: 5px;
        line-height: 1.3;
    }

    .slider-arrow {
        width: 35px;
        height: 35px;
    }

    .movie-slider-container {
        margin: 0 -10px;
    }

    .movie-slider {
        padding: 0 10px;
    }

    .movie-row {
        gap: 10px;
    }
}

/* Small mobile devices */
@media (max-width: 375px) {
    .content-card img {
        min-height: 220px;
    }
}

/* Hero Section Styles */
.hero-section {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    margin-top: -56px; /* Adjust based on your navbar height */
    padding-top: 76px; /* Navbar height + 20px */
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-info {
    max-width: 800px;
}

.show-title {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.show-meta {
    font-size: 1.1rem;
}

.show-overview {
    opacity: 0.9;
}

.hero-buttons .btn {
    padding: 0.8rem 1.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-buttons .btn-danger {
    background-color: #e50914;
    border-color: #e50914;
}

.hero-buttons .btn-danger:hover {
    background-color: #d30813;
    border-color: #d30813;
}

/* Content Sections */
.content-section {
    padding: 20px;
    margin-bottom: 30px;
}

.content-section h2 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.content-slider {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    overflow-x: auto;
    padding: 10px 0;
}

.content-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: transform 0.3s ease;
    aspect-ratio: 2/3;
    background: #333;
}

.content-card:hover {
    transform: scale(1.05);
}

.content-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

.card-info {
    padding: 10px;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    transition: all 0.3s ease;
}

.card-info h3 {
    color: white;
    font-size: 0.9rem;
    margin: 5px 0;
}

.rating {
    color: #ffd700;
    font-size: 0.8rem;
}

/* Additional Styles */
.meta-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.8rem;
    color: #aaa;
}

.seasons {
    background: rgba(255,255,255,0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

.year {
    color: #aaa;
}

.runtime {
    color: #aaa;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .show-title {
        font-size: 2.5rem;
    }
    
    .show-overview {
        font-size: 1rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

.no-content {
    color: #aaa;
    text-align: center;
    padding: 20px;
    width: 100%;
    font-style: italic;
}

img[src=""] {
    display: none;
}

.content-card img {
    min-height: 200px;
    background: #333;
}

/* Add loading animation */
.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #333 0%, #444 50%, #333 100%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.content-card img[src] {
    opacity: 1;
}

.content-card img[src] + .card-info::before {
    display: none;
}

/* Add poster styles */
.hero-poster {
    flex-shrink: 0;
    width: 300px;
    height: 450px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.5);
    transition: transform 0.3s ease;
}

.hero-poster:hover {
    transform: scale(1.02);
}

.hero-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-info {
    flex: 1;
    padding-bottom: 20px;
}

/* Movie Card Styles */
.movie-card {
    position: relative;
    transition: transform 0.3s ease;
    margin-bottom: 1rem;
    background: #1a1a1a;
    border-radius: 8px;
    overflow: hidden;
}

.movie-card:hover {
    transform: translateY(-5px);
}

.card-img-top {
    position: relative;
    padding-top: 150%; /* 2:3 aspect ratio */
    overflow: hidden;
}

.card-img-top img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.movie-card .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.movie-card:hover .overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
    padding: 1rem;
    width: 100%;
}

.rating {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.play-button {
    width: 50px;
    height: 50px;
    background: rgba(229, 9, 20, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.play-button i {
    color: white;
    font-size: 1.5rem;
}

.year {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.movie-title {
    color: white;
    font-size: 0.9rem;
    margin: 0.5rem;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.card-link {
    text-decoration: none;
}

.section-title {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .movie-card .overlay {
        opacity: 1;
        background: rgba(0, 0, 0, 0.5);
    }
    
    .play-button {
        width: 40px;
        height: 40px;
    }
    
    .play-button i {
        font-size: 1.2rem;
    }
    
    .movie-title {
        font-size: 0.8rem;
    }
}

/* Add these styles */
.content-type-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(229, 9, 20, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 2;
    text-transform: uppercase;
    font-weight: 500;
}

.movie-card {
    position: relative; /* Make sure this exists */
}

.bengali-content {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    margin: 20px 0;
}

.bengali-content .section-title {
    color: #fff;
    font-size: 1.5rem;
    margin-bottom: 0;
}

.bengali-content .movie-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
    aspect-ratio: 2/3;
    background: #333;
}

.bengali-content .movie-card:hover {
    transform: scale(1.05);
}

.bengali-content .poster-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.bengali-content .movie-poster {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.bengali-content .poster-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bengali-content .movie-card:hover .poster-overlay {
    opacity: 1;
}

.bengali-content .poster-overlay i {
    font-size: 3rem;
    color: #fff;
}

.bengali-content .movie-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0) 100%);
}

.bengali-content .movie-title {
    font-size: 0.9rem;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bengali-content .btn-netflix {
    background-color: #e50914;
    border-color: #e50914;
    color: white;
    font-size: 0.8rem;
    padding: 4px 8px;
    width: 100%;
}

.bengali-content .btn-netflix:hover {
    background-color: #b20710;
    border-color: #b20710;
}

@media (max-width: 768px) {
    .content-card {
        margin-bottom: 20px;
    }
    
    .content-card img {
        height: auto;
        min-height: 200px; /* Ensure minimum height for posters */
        object-fit: cover;
    }

    .card-info h3 {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .movie-slider .movie-item {
        width: calc((100% - 15px) / 2); /* Show 2 items per row in slider on mobile */
    }
}

/* TV Focus Styles */
.tv-focus-item {
    display: block;
    position: relative;
    outline: none;
}

.tv-focus-item:focus {
    outline: 3px solid #e50914;
    transform: scale(1.05);
    transition: all 0.2s ease;
    z-index: 1;
}

/* Ensure content cards are navigable */
.content-card {
    cursor: pointer;
}

/* Improve visibility of focused items */
.content-card:focus-within {
    box-shadow: 0 0 0 3px #e50914;
    transform: scale(1.05);
}

@media (hover: none) and (pointer: coarse) {
    .content-card:active {
        transform: scale(0.98);
    }
}

/* Add these styles */
.content-card {
    display: block;
    position: relative;
    text-decoration: none;
    color: inherit;
    transition: transform 0.2s ease;
    outline: none;
    width: 100%;
    height: 100%;
}

.content-card:focus {
    transform: scale(1.05);
    outline: 3px solid #e50914;
    z-index: 1;
}

.content-card:hover {
    transform: scale(1.05);
}

.content-card:focus-visible {
    outline: 3px solid #e50914;
}

.content-card {
    height: 100%;
    background: #141414;
    border-radius: 8px;
    overflow: hidden;
}

.content-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 2/3;
}

.movie-poster-wrapper {
    position: relative;
    width: 100%;
    padding-top: 150%; /* 2:3 Aspect ratio */
    overflow: hidden;
}

.movie-poster-wrapper img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .movie-poster-wrapper {
        padding-top: 140%; /* Slightly shorter on mobile */
    }
    
    .content-card img {
        min-height: 200px;
    }
}

/* Latest Series Section */
.latest-series {
    padding: 2rem 0;
    background: rgba(0, 0, 0, 0.2);
    margin: 1rem 0;
}

.series-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 1rem;
    padding: 0 1rem;
}

.series-card {
    position: relative;
    background: #1a1a1a;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.series-card:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.series-link {
    text-decoration: none;
    color: inherit;
}

.poster-container {
    position: relative;
    padding-top: 150%; /* 2:3 aspect ratio */
}

.poster-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.series-card:hover .overlay {
    opacity: 1;
}

.play-icon {
    width: 50px;
    height: 50px;
    background: rgba(229, 9, 20, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.play-icon i {
    color: white;
    font-size: 1.2rem;
}

.seasons-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    position: absolute;
    bottom: 10px;
    left: 10px;
}

.series-info {
    padding: 0.8rem;
    background: linear-gradient(to bottom, rgba(26,26,26,0.8), #1a1a1a);
}

.series-title {
    color: #fff;
    font-size: 0.9rem;
    margin: 0 0 0.5rem 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.series-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: #999;
}

.rating i {
    color: #ffd700;
    margin-right: 0.2rem;
}

/* Quality badge */
.quality-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(229, 9, 20, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
}

/* Loading animation */
.series-card.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .series-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.8rem;
    }

    .play-icon {
        width: 40px;
        height: 40px;
    }

    .play-icon i {
        font-size: 1rem;
    }

    .series-info {
        padding: 0.6rem;
    }

    .series-title {
        font-size: 0.8rem;
    }

    .series-meta {
        font-size: 0.7rem;
        gap: 0.8rem;
    }

    .seasons-badge {
        font-size: 0.7rem;
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .series-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.6rem;
    }
}

/* Hover effects for touch devices */
@media (hover: none) {
    .overlay {
        opacity: 1;
        background: rgba(0, 0, 0, 0.3);
    }
    
    .series-card:active {
        transform: scale(0.98);
    }
}
</style>

<?php include "includes/footer.php"; ?>

