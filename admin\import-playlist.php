<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

function parseM3U($url) {
    $content = @file_get_contents($url);
    if ($content === false) {
        throw new Exception("প্লেলিস্ট URL অ্যাক্সেস করা যাচ্ছে না");
    }
    
    $lines = explode("\n", $content);
    $channels = [];
    $current = null;

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        if (strpos($line, '#EXTINF:') === 0) {
            // Parse channel info
            preg_match('/tvg-name="([^"]*)"/', $line, $name);
            preg_match('/tvg-logo="([^"]*)"/', $line, $logo);
            preg_match('/group-title="([^"]*)"/', $line, $category);
            
            // If tvg-name not found, try to get name from the end of the line
            if (empty($name[1])) {
                $parts = explode(',', $line);
                $name[1] = end($parts);
            }
            
            $current = [
                'name' => $name[1] ?? '',
                'logo' => $logo[1] ?? '',
                'category' => $category[1] ?? 'Uncategorized',
                'stream_url' => ''
            ];
        } elseif ($line[0] !== '#' && $current) {
            $current['stream_url'] = $line;
            $channels[] = $current;
            $current = null;
        }
    }

    return $channels;
}

$message = '';
$type = '';
$preview_channels = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['preview'])) {
            $playlist_url = $_POST['playlist_url'];
            $preview_channels = parseM3U($playlist_url);
            $message = count($preview_channels) . "টি চ্যানেল পাওয়া গেছে। নিচে প্রিভিউ দেখুন।";
            $type = 'info';
        } 
        elseif (isset($_POST['import'])) {
            $playlist_url = $_POST['playlist_url'];
            $channels = parseM3U($playlist_url);
            
            $stmt = $db->prepare("INSERT INTO live_channels (name, slug, stream_url, logo, category, status, sort_order) 
                                 VALUES (?, ?, ?, ?, ?, 'active', 0)
                                 ON DUPLICATE KEY UPDATE 
                                 stream_url = VALUES(stream_url),
                                 logo = VALUES(logo),
                                 category = VALUES(category)");

            $imported = 0;
            foreach ($channels as $channel) {
                if (empty($channel['name']) || empty($channel['stream_url'])) continue;
                
                $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $channel['name']));
                
                $stmt->execute([
                    $channel['name'],
                    $slug,
                    $channel['stream_url'],
                    $channel['logo'],
                    $channel['category']
                ]);
                
                $imported++;
            }

            $message = "সফলভাবে {$imported}টি চ্যানেল ইম্পোর্�� করা হয়েছে!";
            $type = 'success';
        }
    } catch (Exception $e) {
        $message = "ত্রুটি: " . $e->getMessage();
        $type = 'danger';
    }
}

$page_title = 'Import M3U Playlist';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">M3U প্লেলিস্ট ইম্পোর্ট করুন</h3>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $type; ?>"><?php echo $message; ?></div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">প্লেলিস্ট URL</label>
                            <input type="url" name="playlist_url" class="form-control" required
                                   placeholder="M3U প্লেলিস্ট URL দিন"
                                   value="<?php echo $_POST['playlist_url'] ?? ''; ?>">
                            <small class="text-muted">উদাহরণ: http://example.com/playlist.m3u</small>
                        </div>
                        <button type="submit" name="preview" class="btn btn-info me-2">প্রিভিউ দেখুন</button>
                        <button type="submit" name="import" class="btn btn-primary">ইম্পোর্ট করুন</button>
                    </form>

                    <?php if (!empty($preview_channels)): ?>
                        <div class="mt-4">
                            <h4>প্রিভিউ (<?php echo count($preview_channels); ?>টি চ্যানেল)</h4>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>নাম</th>
                                            <th>লোগো</th>
                                            <th>ক্যাটাগরি</th>
                                            <th>স্ট্রিম URL</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($preview_channels as $channel): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($channel['name']); ?></td>
                                                <td>
                                                    <?php if ($channel['logo']): ?>
                                                        <img src="<?php echo htmlspecialchars($channel['logo']); ?>" 
                                                             alt="Logo" style="height: 30px;">
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($channel['category']); ?></td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 300px;">
                                                        <?php echo htmlspecialchars($channel['stream_url']); ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
