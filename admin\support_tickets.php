<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$page_title = "Support Tickets";
require_once 'includes/header.php';

// Get tickets with user information
$stmt = $db->query("
    SELECT 
        st.*,
        u.username,
        u.email,
        (SELECT COUNT(*) FROM support_messages WHERE ticket_id = st.id) as message_count,
        (SELECT MAX(created_at) FROM support_messages WHERE ticket_id = st.id) as last_reply
    FROM support_tickets st
    LEFT JOIN users u ON st.user_id = u.id
    ORDER BY st.status = 'open' DESC, st.created_at DESC
");
$tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6>সাপোর্ট টিকেট ম্যানেজমেন্ট</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th>টিকেট আইডি</th>
                                    <th>ইউজার</th>
                                    <th>বিষয়</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>মেসেজ</th>
                                    <th>শেষ রিপ্লাই</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tickets as $ticket): ?>
                                <tr>
                                    <td>#<?= $ticket['id'] ?></td>
                                    <td>
                                        <div>
                                            <div class="d-flex flex-column">
                                                <h6 class="mb-0 text-sm"><?= htmlspecialchars($ticket['username']) ?></h6>
                                                <p class="text-xs text-secondary mb-0"><?= $ticket['email'] ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($ticket['subject']) ?></td>
                                    <td>
                                        <span class="badge badge-sm bg-<?= $ticket['status'] === 'open' ? 'success' : 'secondary' ?>">
                                            <?= $ticket['status'] === 'open' ? 'চলমান' : 'সমাধান হয়েছে' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-sm bg-primary"><?= $ticket['message_count'] ?></span>
                                    </td>
                                    <td>
                                        <?= date('d M Y H:i', strtotime($ticket['last_reply'])) ?>
                                    </td>
                                    <td>
                                        <a href="view_ticket.php?id=<?= $ticket['id'] ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-reply"></i> রিপ্লাই
                                        </a>
                                        <?php if($ticket['status'] === 'open'): ?>
                                        <button onclick="closeTicket(<?= $ticket['id'] ?>)" 
                                                class="btn btn-success btn-sm">
                                            <i class="fas fa-check"></i> সমাধান
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function closeTicket(ticketId) {
    if (confirm('আপনি কি নিশ্চিত যে এই টিকেটটি সমাধান করতে চান?')) {
        fetch('ajax/update_ticket.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ticket_id: ticketId,
                status: 'closed'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
