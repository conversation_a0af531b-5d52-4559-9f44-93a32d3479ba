Options +FollowSymLinks
RewriteEngine On

# Basic PHP Settings
php_value memory_limit 256M
php_value max_execution_time 300
php_value upload_max_filesize 64M
php_value post_max_size 64M
php_flag display_errors on
php_value error_reporting E_ALL
php_value session.cookie_lifetime 2592000
php_value session.gc_maxlifetime 2592000
php_value session.cookie_secure On
php_value session.cookie_httponly On
php_value session.use_only_cookies On
php_value session.cookie_samesite "Lax"

# Security Headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header always append X-Frame-Options SAMEORIGIN
    Header set Content-Security-Policy "frame-ancestors 'self'"
</IfModule>

# CORS Settings
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "same-origin"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
    Header set Access-Control-Allow-Credentials "true"
</IfModule>

# Enable CORS for proxy.php
<Files "proxy.php">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</Files>

# Block bad bots
RewriteCond %{HTTP_USER_AGENT} ^.*(bot|spider|crawler|scraper).*$ [NC]
RewriteRule .* - [F,L]

# Error Documents
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php

# Prevent directory listing
Options -Indexes

# Force HTTPS (uncomment if needed)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Cache Control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “alt-php82” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-alt-php82 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit

# Prevent iframe embedding from unauthorized domains
Header set X-Frame-Options "SAMEORIGIN"

# Prevent clickjacking
Header always append X-Frame-Options SAMEORIGIN

# Add security headers
Header set X-XSS-Protection "1; mode=block"
Header set X-Content-Type-Options "nosniff"

# Block known ad domains
RewriteEngine On
RewriteCond %{HTTP_REFERER} ^http(s)?://(www\.)?([^.]+\.)?doubleclick\.net [NC,OR]
RewriteCond %{HTTP_REFERER} ^http(s)?://(www\.)?([^.]+\.)?adnxs\.com [NC,OR]
RewriteCond %{HTTP_REFERER} ^http(s)?://(www\.)?([^.]+\.)?popads\.net [NC]
RewriteRule .* - [F]

# Disable directory browsing
Options -Indexes

# Prevent access to .htaccess
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Prevent access to specific file types except in admin directory
<FilesMatch "\.(php|php3|php4|php5|phtml|phps)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Allow access to necessary PHP files
<FilesMatch "^(index|login|register|movie|show|season|episode|support|checkout|terms|faq|proxy)\.php$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Allow all PHP files in admin directory
<LocationMatch "^/admin/">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Allow from all
    </FilesMatch>
</LocationMatch>

# Alternative admin directory access method
<Directory "/path/to/your/admin">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Allow from all
    </FilesMatch>
</Directory>

# Additional security headers
Header set X-XSS-Protection "1; mode=block"
Header set X-Content-Type-Options "nosniff"
Header set X-Frame-Options "SAMEORIGIN"
Header set Referrer-Policy "no-referrer"
